"""
本地数据缓存管理系统 (混合存储版本)
Local Data Cache Management System (Hybrid Storage Version)

存储策略：
- 基础信息(交易日历、股票基本信息等)：CSV文件存储，便于查看和备份
- 股票历史数据：SQLite数据库存储，提高查询效率
- 基金数据：SQLite数据库存储，包含ETF和场外基金
"""

import pandas as pd
import chinadata.ca_data as ts
import os
import sqlite3
from datetime import datetime, timedelta
import json
import time

class DataCache:
    """本地数据缓存管理器 (混合存储版本)"""
    
    def __init__(self):
        """
        初始化缓存管理器
        
        存储结构：
        - data_cache/
          ├── basic/              # 基础信息CSV文件
          ├── stock_data.db       # 股票数据库
          └── fund_data.db        # 基金数据库
        """
        # 确保缓存目录在当前脚本所在目录下
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.cache_dir = os.path.join(script_dir, "data_cache")
        self.basic_dir = os.path.join(self.cache_dir, "basic")
        
        # 创建目录结构
        for dir_path in [self.cache_dir, self.basic_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
        
        # 数据库文件路径
        self.stock_db = os.path.join(self.cache_dir, "stock_data.db")
        self.fund_db = os.path.join(self.cache_dir, "fund_data.db")
        
        self.token = "f702ae16c20dd54a28af3af64a04eabb274"
        
        # 初始化Tushare
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        
        # 初始化数据库
        self.init_databases()
        self.init_fundamental_tables()
        self.init_industry_tables()
        self.init_industry_tables()
        self.init_industry_tables()
    
    def init_databases(self):
        """初始化数据库表结构"""
        
        # 1. 初始化股票数据库
        with sqlite3.connect(self.stock_db) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_code ON stock_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_date ON stock_daily(trade_date)')
            
        # 2. 初始化基金数据库
        with sqlite3.connect(self.fund_db) as conn:
            # ETF日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS etf_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 基金净值数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fund_nav (
                    ts_code TEXT,
                    ann_date TEXT,
                    nav_date TEXT,
                    unit_nav REAL,
                    accum_nav REAL,
                    accum_div REAL,
                    net_asset REAL,
                    total_netasset REAL,
                    adj_nav REAL,
                    update_flag TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, nav_date)
                )
            ''')
            
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_code ON etf_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_date ON etf_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_code ON fund_nav(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_date ON fund_nav(nav_date)')
        
        print(f"✅ 数据库初始化完成:")
        print(f"   📈 股票数据库: {self.stock_db}")
        print(f"   📊 基金数据库: {self.fund_db}")
        print(f"   📋 基础信息目录: {self.basic_dir}")
        
        # 升级数据库结构（添加缺失字段）
        self.upgrade_database_schema()
    
    # ==================== 基础信息数据 (CSV存储) ====================
    
    def get_basic_file_path(self, data_type):
        """获取基础信息CSV文件路径"""
        return os.path.join(self.basic_dir, f"{data_type}.csv")
    
    def is_csv_valid(self, file_path, max_age_days=1):
        """检查CSV文件是否有效"""
        if not os.path.exists(file_path):
            return False
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        return datetime.now() - file_time < timedelta(days=max_age_days)
    
    def get_stock_basic(self, force_update=False):
        """获取股票基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("stock_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print("🔄 获取股票基本信息...")
        try:
            data = self.pro.stock_basic(
                exchange='', 
                list_status='L', 
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ 股票基本信息已保存: {csv_path}")
            return data
        except Exception as e:
            print(f"❌ 获取股票基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_trade_calendar(self, start_date='20200101', end_date='20251231', force_update=False):
        """获取交易日历 (CSV存储)"""
        csv_path = self.get_basic_file_path("trade_calendar")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=30):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print("🔄 获取交易日历...")
        try:
            data = self.pro.trade_cal(
                exchange='SSE',
                start_date=start_date,
                end_date=end_date,
                fields='exchange,cal_date,is_open'
            )
            data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ 交易日历已保存: {csv_path}")
            return data
        except Exception as e:
            print(f"❌ 获取交易日历失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_fund_basic(self, force_update=False):
        """获取公募基金基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("fund_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取公募基金基本信息...")
            fund_basic = self.pro.fund_basic(market='E')  # E表示场外基金
            etf_basic = self.pro.fund_basic(market='O')   # O表示场内基金(ETF)
            all_funds = pd.concat([fund_basic, etf_basic], ignore_index=True)
            
            print(f"✅ 获取公募基金基本信息: {len(all_funds)} 只基金")
            print(f"   场外基金: {len(fund_basic)} 只")
            print(f"   场内基金(ETF): {len(etf_basic)} 只")
            
            all_funds.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 基金基本信息已保存: {csv_path}")
            return all_funds
            
        except Exception as e:
            print(f"❌ 获取基金基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_index_basic(self, force_update=False):
        """获取指数基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("index_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取指数基本信息...")
            # 获取主要指数
            index_basic = self.pro.index_basic(market='SSE')  # 上交所指数
            index_basic_szse = self.pro.index_basic(market='SZSE')  # 深交所指数
            index_basic_csi = self.pro.index_basic(market='CSI')   # 中证指数
            index_basic_cicc = self.pro.index_basic(market='CICC') # 中金所指数
            
            all_indices = pd.concat([index_basic, index_basic_szse, index_basic_csi, index_basic_cicc], ignore_index=True)
            
            print(f"✅ 获取指数基本信息: {len(all_indices)} 个指数")
            print(f"   上交所: {len(index_basic)} 个")
            print(f"   深交所: {len(index_basic_szse)} 个")
            print(f"   中证指数: {len(index_basic_csi)} 个")
            print(f"   中金所: {len(index_basic_cicc)} 个")
            
            all_indices.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 指数基本信息已保存: {csv_path}")
            return all_indices
            
        except Exception as e:
            print(f"❌ 获取指数基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_industry_classify(self, force_update=False):
        """获取行业分类信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("industry_classify")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=30):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取行业分类信息...")
            # 获取申万一级行业分类
            sw_l1 = self.pro.index_classify(level='L1', src='SW2021')
            sw_l2 = self.pro.index_classify(level='L2', src='SW2021')
            sw_l3 = self.pro.index_classify(level='L3', src='SW2021')
            
            # 获取证监会行业分类
            zjh = self.pro.index_classify(level='L1', src='ZJRC')
            
            all_classify = pd.concat([sw_l1, sw_l2, sw_l3, zjh], ignore_index=True)
            
            print(f"✅ 获取行业分类信息: {len(all_classify)} 条")
            print(f"   申万一级: {len(sw_l1)} 条")
            print(f"   申万二级: {len(sw_l2)} 条")
            print(f"   申万三级: {len(sw_l3)} 条")
            print(f"   证监会: {len(zjh)} 条")
            
            all_classify.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 行业分类信息已保存: {csv_path}")
            return all_classify
            
        except Exception as e:
            print(f"❌ 获取行业分类信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    # ==================== 数据库操作辅助方法 ====================
    
    def save_to_database(self, data, table_name, db_path):
        """保存数据到指定数据库，支持重复数据覆盖"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 设置WAL模式以减少锁定
                    conn.execute('PRAGMA journal_mode=WAL')
                    conn.execute('PRAGMA synchronous=NORMAL')
                    # 使用 replace 方式处理重复记录
                    data.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
                print(f"✅ 数据已保存到数据库: {table_name}")
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))  # 递增等待时间
                    continue
                elif "UNIQUE constraint failed" in str(e):
                    try:
                        self._save_with_replace(data, table_name, db_path)
                        print(f"✅ 数据已保存到数据库(替换模式): {table_name}")
                        return
                    except Exception as e2:
                        print(f"❌ 数据库保存失败: {e2}")
                        return
                else:
                    print(f"❌ 数据库保存失败: {e}")
                    return
    
    def _save_with_replace(self, data, table_name, db_path):
        """使用INSERT OR REPLACE方式保存数据"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 设置WAL模式以减少锁定
                    conn.execute('PRAGMA journal_mode=WAL')
                    conn.execute('PRAGMA synchronous=NORMAL')
                    
                    cursor = conn.cursor()
                    
                    # 获取表的列名
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns_info = cursor.fetchall()
                    db_columns = [col[1] for col in columns_info]
                    
                    # 只保留数据库中存在的列
                    data_columns = [col for col in data.columns if col in db_columns]
                    filtered_data = data[data_columns]
                    
                    # 构建INSERT OR REPLACE语句
                    placeholders = ','.join(['?' for _ in data_columns])
                    columns_str = ','.join(data_columns)
                    
                    insert_sql = f"INSERT OR REPLACE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                    
                    # 批量插入数据
                    cursor.executemany(insert_sql, filtered_data.values.tolist())
                    conn.commit()
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                else:
                    raise e
    
    def load_from_database(self, table_name, db_path, ts_code=None):
        """从数据库加载数据"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    if ts_code:
                        # 根据表名选择合适的日期列和代码列
                        if table_name == 'fund_nav':
                            date_column = 'nav_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_fina_indicator' or table_name == 'stock_forecast':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_share_float':
                            date_column = 'float_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_holder_number':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_dividend':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'sw_industry_daily':
                            date_column = 'trade_date'
                            code_column = 'ts_code'
                        else:
                            date_column = 'trade_date'
                            code_column = 'ts_code'
                        
                        data = pd.read_sql(f"SELECT * FROM {table_name} WHERE {code_column} = ? ORDER BY {date_column}", conn, params=[ts_code])
                    else:
                        data = pd.read_sql(f"SELECT * FROM {table_name}", conn)
                    
                    if not data.empty:
                        print(f"📁 从数据库加载: {table_name} ({len(data)} 条记录)")
                    return data
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                else:
                    print(f"❌ 数据库加载失败: {e}")
                    return pd.DataFrame()
    
    def get_latest_date(self, table_name, db_path, ts_code, date_column):
        """获取指定股票/基金的最新数据日期"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 根据表名选择合适的代码列
                    if table_name == 'sw_industry_daily':
                        code_column = 'ts_code'
                    else:
                        code_column = 'ts_code'
                    
                    query = f"SELECT MAX({date_column}) FROM {table_name} WHERE {code_column} = ?"
                    result = conn.execute(query, (ts_code,)).fetchone()
                    return result[0] if result[0] else None
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                else:
                    return None
    
    def delete_duplicate_records(self, table_name, db_path, ts_code, date_column):
        """删除重复记录，保留最新的"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 根据表名选择合适的代码列
                    if table_name == 'sw_industry_daily':
                        code_column = 'ts_code'
                    else:
                        code_column = 'ts_code'
                    
                    conn.execute(f'''
                        DELETE FROM {table_name} 
                        WHERE rowid NOT IN (
                            SELECT MAX(rowid) 
                            FROM {table_name} 
                            WHERE {code_column} = ? 
                            GROUP BY {code_column}, {date_column}
                        ) AND {code_column} = ?
                    ''', (ts_code, ts_code))
                    conn.commit()
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                else:
                    print(f"⚠️ 清理重复数据失败: {e}")
                    return
    
    # ==================== 股票数据 (SQLite存储) ====================
    
    def get_stock_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股票日线数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} 股票数据...")
        try:
            new_data = self.pro.daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('stock_daily', self.stock_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 股票数据失败: {e}")
            if not force_update:
                return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            return None
    
    # ==================== 基金数据 (SQLite存储) ====================
    
    def get_fund_nav(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取基金净值数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('fund_nav', self.fund_db, ts_code, 'nav_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} 基金净值数据...")
        try:
            new_data = self.pro.fund_nav(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'fund_nav', self.fund_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('fund_nav', self.fund_db, ts_code, 'nav_date')
            # 返回完整数据
            return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 基金净值失败: {e}")
            if not force_update:
                return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            return None
    
    def get_etf_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取ETF日线数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('etf_daily', self.fund_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} ETF数据...")
        try:
            new_data = self.pro.fund_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'etf_daily', self.fund_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('etf_daily', self.fund_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} ETF数据失败: {e}")
            if not force_update:
                return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            return None
    
    # ==================== 股票基本面数据 (SQLite存储) ====================
    
    def init_fundamental_tables(self):
        """初始化股票基本面数据表"""
        with sqlite3.connect(self.stock_db) as conn:
            # 财务指标表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_fina_indicator (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    eps REAL,
                    dt_eps REAL,
                    total_revenue_ps REAL,
                    revenue_ps REAL,
                    capital_rese_ps REAL,
                    surplus_rese_ps REAL,
                    undist_profit_ps REAL,
                    extra_item REAL,
                    profit_dedt REAL,
                    gross_margin REAL,
                    current_ratio REAL,
                    quick_ratio REAL,
                    cash_ratio REAL,
                    invturn_days REAL,
                    arturn_days REAL,
                    inv_turn REAL,
                    ar_turn REAL,
                    ca_turn REAL,
                    fa_turn REAL,
                    assets_turn REAL,
                    op_income REAL,
                    valuechange_income REAL,
                    interst_income REAL,
                    daa REAL,
                    ebit REAL,
                    ebitda REAL,
                    fcff REAL,
                    fcfe REAL,
                    current_exint REAL,
                    noncurrent_exint REAL,
                    interestdebt REAL,
                    netdebt REAL,
                    tangible_asset REAL,
                    working_capital REAL,
                    networking_capital REAL,
                    invest_capital REAL,
                    retained_earnings REAL,
                    diluted2_eps REAL,
                    bps REAL,
                    ocfps REAL,
                    retainedps REAL,
                    cfps REAL,
                    ebit_ps REAL,
                    fcff_ps REAL,
                    fcfe_ps REAL,
                    netprofit_margin REAL,
                    grossprofit_margin REAL,
                    cogs_of_sales REAL,
                    expense_of_sales REAL,
                    profit_to_gr REAL,
                    saleexp_to_gr REAL,
                    adminexp_of_gr REAL,
                    finaexp_of_gr REAL,
                    impai_ttm REAL,
                    gc_of_gr REAL,
                    op_of_gr REAL,
                    ebit_of_gr REAL,
                    roe REAL,
                    roe_waa REAL,
                    roe_dt REAL,
                    roa REAL,
                    npta REAL,
                    roic REAL,
                    roe_yearly REAL,
                    roa_yearly REAL,
                    roe_avg REAL,
                    opincome_of_ebt REAL,
                    investincome_of_ebt REAL,
                    n_op_profit_of_ebt REAL,
                    tax_to_ebt REAL,
                    dtprofit_to_profit REAL,
                    salescash_to_or REAL,
                    ocf_to_or REAL,
                    ocf_to_opincome REAL,
                    capitalized_to_da REAL,
                    debt_to_assets REAL,
                    assets_to_eqt REAL,
                    dp_assets_to_eqt REAL,
                    ca_to_assets REAL,
                    nca_to_assets REAL,
                    tbassets_to_totalassets REAL,
                    int_to_talcap REAL,
                    eqt_to_talcapital REAL,
                    currentdebt_to_debt REAL,
                    longdeb_to_debt REAL,
                    ocf_to_shortdebt REAL,
                    debt_to_eqt REAL,
                    eqt_to_debt REAL,
                    eqt_to_interestdebt REAL,
                    tangibleasset_to_debt REAL,
                    tangasset_to_intdebt REAL,
                    tangibleasset_to_netdebt REAL,
                    ocf_to_debt REAL,
                    ocf_to_interestdebt REAL,
                    ocf_to_netdebt REAL,
                    ebit_to_interest REAL,
                    longdebt_to_workingcapital REAL,
                    ebitda_to_debt REAL,
                    turn_days REAL,
                    roa_dp REAL,
                    roa2_yearly REAL,
                    fixed_assets REAL,
                    profit_prefin_exp REAL,
                    non_op_profit REAL,
                    op_to_ebt REAL,
                    nop_to_ebt REAL,
                    ocf_to_profit REAL,
                    cash_to_liqdebt REAL,
                    cash_to_liqdebt_withinterest REAL,
                    op_to_liqdebt REAL,
                    op_to_debt REAL,
                    roic_yearly REAL,
                    total_fa_trun REAL,
                    profit_to_op REAL,
                    q_opincome REAL,
                    q_investincome REAL,
                    q_dtprofit REAL,
                    q_eps REAL,
                    q_netprofit_margin REAL,
                    q_gsprofit_margin REAL,
                    q_exp_to_sales REAL,
                    q_profit_to_gr REAL,
                    q_saleexp_to_gr REAL,
                    q_adminexp_to_gr REAL,
                    q_finaexp_to_gr REAL,
                    q_impair_to_gr_ttm REAL,
                    q_gc_to_gr REAL,
                    q_op_to_gr REAL,
                    q_roe REAL,
                    q_dt_roe REAL,
                    q_npta REAL,
                    q_opincome_to_ebt REAL,
                    q_investincome_to_ebt REAL,
                    q_dtprofit_to_profit REAL,
                    q_salescash_to_or REAL,
                    q_ocf_to_sales REAL,
                    q_ocf_to_or REAL,
                    basic_eps_yoy REAL,
                    dt_eps_yoy REAL,
                    cfps_yoy REAL,
                    op_yoy REAL,
                    ebt_yoy REAL,
                    netprofit_yoy REAL,
                    dt_netprofit_yoy REAL,
                    ocf_yoy REAL,
                    roe_yoy REAL,
                    bps_yoy REAL,
                    assets_yoy REAL,
                    eqt_yoy REAL,
                    tr_yoy REAL,
                    or_yoy REAL,
                    q_gr_yoy REAL,
                    q_gr_qoq REAL,
                    q_sales_yoy REAL,
                    q_sales_qoq REAL,
                    q_op_yoy REAL,
                    q_op_qoq REAL,
                    q_profit_yoy REAL,
                    q_profit_qoq REAL,
                    q_netprofit_yoy REAL,
                    q_netprofit_qoq REAL,
                    equity_yoy REAL,
                    rd_exp REAL,
                    update_flag TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 每日估值指标表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily_basic (
                    ts_code TEXT,
                    trade_date TEXT,
                    close REAL,
                    turnover_rate REAL,
                    turnover_rate_f REAL,
                    volume_ratio REAL,
                    pe REAL,
                    pe_ttm REAL,
                    pb REAL,
                    ps REAL,
                    ps_ttm REAL,
                    dv_ratio REAL,
                    dv_ttm REAL,
                    total_share REAL,
                    float_share REAL,
                    free_share REAL,
                    total_mv REAL,
                    circ_mv REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 业绩预告表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_forecast (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    type TEXT,
                    p_change_min REAL,
                    p_change_max REAL,
                    net_profit_min REAL,
                    net_profit_max REAL,
                    last_parent_net REAL,
                    first_ann_date TEXT,
                    summary TEXT,
                    change_reason TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 股票复权因子表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_adj_factor (
                    ts_code TEXT,
                    trade_date TEXT,
                    adj_factor REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 分红送转表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_dividend (
                    ts_code TEXT,
                    end_date TEXT,
                    ann_date TEXT,
                    div_proc TEXT,
                    stk_div REAL,
                    stk_bo_rate REAL,
                    stk_co_rate REAL,
                    cash_div REAL,
                    cash_div_tax REAL,
                    record_date TEXT,
                    ex_date TEXT,
                    pay_date TEXT,
                    div_listdate TEXT,
                    imp_ann_date TEXT,
                    base_share REAL,
                    base_date TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, end_date, ann_date)
                )
            ''')
            
            # 限售解禁表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_share_float (
                    ts_code TEXT,
                    ann_date TEXT,
                    float_date TEXT,
                    float_share REAL,
                    float_ratio REAL,
                    holder_name TEXT,
                    share_type TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, float_date)
                )
            ''')
            
            # 股东户数表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_holder_number (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    holder_num INTEGER,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 指数日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS index_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 指数成分股权重表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS index_weight (
                    index_code TEXT,
                    con_code TEXT,
                    trade_date TEXT,
                    weight REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (index_code, con_code, trade_date)
                )
            ''')
            
            # 行业分类表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_industry_classify (
                    ts_code TEXT,
                    industry_code TEXT,
                    industry_name TEXT,
                    src TEXT,
                    level TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, industry_code, src)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_code ON stock_fina_indicator(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_date ON stock_fina_indicator(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_daily_basic_code ON stock_daily_basic(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_code ON stock_forecast(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_date ON stock_forecast(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_date ON stock_forecast(end_date)')
            
            # 新增表的索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_adj_factor_code ON stock_adj_factor(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_adj_factor_date ON stock_adj_factor(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_dividend_code ON stock_dividend(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_dividend_date ON stock_dividend(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_share_float_code ON stock_share_float(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_share_float_date ON stock_share_float(float_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_holder_number_code ON stock_holder_number(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_holder_number_date ON stock_holder_number(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_code ON index_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_date ON index_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_code ON index_weight(index_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_con ON index_weight(con_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_date ON index_weight(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_code ON stock_industry_classify(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_name ON stock_industry_classify(industry_code)')
    
    def get_stock_adj_factor(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取复权因子数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_adj_factor', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 复权因子")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 复权因子数据...")
        try:
            new_data = self.pro.adj_factor(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无复权因子数据")
                if not force_update:
                    return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_adj_factor', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 复权因子失败: {e}")
            if not force_update:
                return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_dividend(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取分红送转数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_dividend', self.stock_db, ts_code, 'end_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=30):
                        print(f"📁 使用数据库缓存: {ts_code} 分红送转")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 分红送转数据...")
        try:
            new_data = self.pro.dividend(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无分红送转数据")
                if not force_update:
                    return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_dividend', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 分红送转失败: {e}")
            if not force_update:
                return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_share_float(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取限售解禁数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_share_float', self.stock_db, ts_code, 'float_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=30):
                        print(f"📁 使用数据库缓存: {ts_code} 限售解禁")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 限售解禁数据...")
        try:
            new_data = self.pro.share_float(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无限售解禁数据")
                if not force_update:
                    return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_share_float', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 限售解禁失败: {e}")
            if not force_update:
                return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_holder_number(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股东户数数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_holder_number', self.stock_db, ts_code, 'end_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=90):
                        print(f"📁 使用数据库缓存: {ts_code} 股东户数")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 股东户数数据...")
        try:
            new_data = self.pro.stk_holdernumber(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无股东户数数据")
                if not force_update:
                    return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_holder_number', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 股东户数失败: {e}")
            if not force_update:
                return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            return None
    
    def get_index_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('index_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 指数日线")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 指数日线数据...")
        try:
            new_data = self.pro.index_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无指数日线数据")
                if not force_update:
                    return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'index_daily', self.stock_db)
            # 返回完整数据
            return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 指数日线失败: {e}")
            if not force_update:
                return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            return None
    
    def get_index_weight(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取指数成分股权重数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 指数成分股权重数据...")
        try:
            new_data = self.pro.index_weight(
                index_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无指数成分股权重数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'index_weight', self.stock_db)
            print(f"✅ {ts_code} 指数成分股权重数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 指数成分股权重失败: {e}")
            return None
    
    def get_stock_fina_indicator(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取财务指标数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 财务指标数据...")
        try:
            new_data = self.pro.fina_indicator(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无财务指标数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_fina_indicator', self.stock_db)
            print(f"✅ {ts_code} 财务指标数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 财务指标失败: {e}")
            return None
    
    def get_stock_forecast(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取业绩预告数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 业绩预告数据...")
        try:
            new_data = self.pro.forecast(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无业绩预告数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_forecast', self.stock_db)
            print(f"✅ {ts_code} 业绩预告数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 业绩预告失败: {e}")
            return None
    
    def get_stock_daily_basic(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股票每日估值指标数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 估值数据...")
        try:
            new_data = self.pro.daily_basic(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无估值数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_daily_basic', self.stock_db)
            print(f"✅ {ts_code} 估值数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 估值数据失败: {e}")
            return None
    
    def get_comprehensive_dividend_data(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取综合分红送转数据，主要使用dividend接口"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 分红送转数据...")
        
        try:
            # 使用已有的dividend方法
            dividend_data = self.get_stock_dividend(ts_code, start_date, end_date, force_update)
            
            if dividend_data is not None and not dividend_data.empty:
                print(f"✅ {ts_code} 分红送转数据已获取 (共{len(dividend_data)}条)")
                return dividend_data
            else:
                print(f"⚠️ {ts_code} 无分红送转数据")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ 获取 {ts_code} 分红送转数据失败: {e}")
            return pd.DataFrame()
    
    def cache_all_comprehensive_dividend(self):
        """批量缓存所有股票的综合分红送转数据"""
        print("🔄 开始批量缓存综合分红送转数据...")
        stock_list = self.get_stock_list()
        
        if stock_list.empty:
            print("❌ 无法获取股票列表")
            return
        
        success_count = 0
        total_count = len(stock_list)
        
        for index, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            name = stock.get('name', ts_code)
            
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({ts_code})")
            
            try:
                result = self.get_comprehensive_dividend_data(ts_code)
                if result is not None and not result.empty:
                    success_count += 1
                
                # 控制请求频率
                time.sleep(0.2)
                
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)  # 错误时等待更长时间
        
        print(f"\n🎉 批量缓存完成！成功: {success_count}/{total_count}")

    # ==================== 批量缓存方法 ====================
    
    def get_stock_list(self):
        """获取股票列表"""
        stock_basic = self.get_stock_basic()
        if stock_basic is None or stock_basic.empty:
            print("❌ 无法获取股票基本信息")
            return pd.DataFrame()
        return stock_basic
    
    def cache_all_stocks_data(self):
        """批量缓存所有股票的日线数据"""
        print("🔄 开始批量缓存股票日线数据...")
        stock_list = self.get_stock_list()
        
        if stock_list.empty:
            print("❌ 无法获取股票列表")
            return
        
        success_count = 0
        total_count = len(stock_list)
        
        for index, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            name = stock.get('name', ts_code)
            
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({ts_code})")
            
            try:
                result = self.get_stock_daily(ts_code)
                if result is not None and not result.empty:
                    success_count += 1
                
                # 控制请求频率
                time.sleep(0.2)
                
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)  # 错误时等待更长时间
        
        print(f"\n🎉 批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_all_daily_basic(self, start_date=None, end_date=None):
        """批量缓存市场估值数据"""
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 开始批量缓存市场估值数据 ({start_date} - {end_date})...")
        stock_list = self.get_stock_list()
        
        if stock_list.empty:
            print("❌ 无法获取股票列表")
            return
        
        success_count = 0
        total_count = len(stock_list)
        
        for index, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            name = stock.get('name', ts_code)
            
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({ts_code})")
            
            try:
                result = self.get_stock_daily_basic(ts_code, start_date, end_date)
                if result is not None and not result.empty:
                    success_count += 1
                
                # 控制请求频率
                time.sleep(0.2)
                
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)
        
        print(f"\n🎉 市场估值数据批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_all_funds_data(self):
        """批量缓存所有基金数据"""
        print("🔄 开始批量缓存基金数据...")
        fund_basic = self.get_fund_basic()
        
        if fund_basic is None or fund_basic.empty:
            print("❌ 无法获取基金列表")
            return
        
        # 分类处理ETF和场外基金
        etf_funds = fund_basic[
            (fund_basic['market'] == 'O') & 
            (fund_basic['ts_code'].str.endswith('.SH') | fund_basic['ts_code'].str.endswith('.SZ'))
        ]
        
        nav_funds = fund_basic[
            (fund_basic['market'] == 'E') | 
            (fund_basic['ts_code'].str.endswith('.OF'))
        ]
        
        success_count = 0
        
        # 处理ETF数据
        print(f"\n📈 处理ETF数据: {len(etf_funds)} 只")
        for index, fund in etf_funds.iterrows():
            ts_code = fund['ts_code']
            name = fund.get('name', ts_code)
            
            print(f"📊 处理ETF {index+1}/{len(etf_funds)}: {name} ({ts_code})")
            
            try:
                result = self.get_etf_daily(ts_code)
                if result is not None and not result.empty:
                    success_count += 1
                time.sleep(0.2)
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)
        
        # 处理场外基金净值数据
        print(f"\n📊 处理场外基金净值: {len(nav_funds)} 只")
        for index, fund in nav_funds.iterrows():
            ts_code = fund['ts_code']
            name = fund.get('name', ts_code)
            
            print(f"📊 处理基金 {index+1}/{len(nav_funds)}: {name} ({ts_code})")
            
            try:
                result = self.get_fund_nav(ts_code)
                if result is not None and not result.empty:
                    success_count += 1
                time.sleep(0.2)
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)
        
        total_count = len(etf_funds) + len(nav_funds)
        print(f"\n🎉 基金数据批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_all_fundamental_data(self):
        """批量缓存所有股票的基本面数据"""
        print("🔄 开始批量缓存股票基本面数据...")
        stock_list = self.get_stock_list()
        
        if stock_list.empty:
            print("❌ 无法获取股票列表")
            return
        
        success_count = 0
        total_count = len(stock_list)
        
        for index, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            name = stock.get('name', ts_code)
            
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({ts_code})")
            
            try:
                # 获取财务指标
                fina_result = self.get_stock_fina_indicator(ts_code)
                if fina_result is not None and not fina_result.empty:
                    success_count += 1
                
                time.sleep(0.2)
                
                # 获取业绩预告
                forecast_result = self.get_stock_forecast(ts_code)
                
                time.sleep(0.2)
                
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(1)
        
        print(f"\n🎉 基本面数据批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_all_extended_data(self):
        """批量缓存扩展数据(复权、分红、解禁、股东户数)"""
        print("🔄 开始批量缓存扩展数据...")
        stock_list = self.get_stock_list()
        
        if stock_list.empty:
            print("❌ 无法获取股票列表")
            return
        
        success_count = 0
        total_count = len(stock_list)
        
        for index, stock in stock_list.iterrows():
            ts_code = stock['ts_code']
            name = stock.get('name', ts_code)
            
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({ts_code})")
            
            try:
                # 复权因子
                adj_result = self.get_stock_adj_factor(ts_code)
                
                # 分红送转
                dividend_result = self.get_stock_dividend(ts_code)
                
                # 限售解禁
                float_result = self.get_stock_share_float(ts_code)
                
                # 股东户数
                holder_result = self.get_stock_holder_number(ts_code)
                
                if any([adj_result is not None and not adj_result.empty,
                       dividend_result is not None and not dividend_result.empty,
                       float_result is not None and not float_result.empty,
                       holder_result is not None and not holder_result.empty]):
                    success_count += 1
                    print(f"✅ {name} 扩展数据处理完成")
                else:
                    print(f"⚠️ {name} 无扩展数据")
                
                # 控制API调用频率
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ {name} 扩展数据处理失败: {e}")
                time.sleep(0.1)
                continue
        
        print(f"\n✅ 扩展数据缓存完成: {success_count}/{total_count}")
    
    def cache_all_index_data(self):
        """批量缓存指数数据(指数日线、成分股权重)"""
        print("🔄 开始批量缓存指数数据...")
        
        # 获取主要指数列表
        major_indices = [
            '000001.SH',  # 上证指数
            '000300.SH',  # 沪深300
            '000905.SH',  # 中证500
            '000852.SH',  # 中证1000
            '399001.SZ',  # 深证成指
            '399006.SZ',  # 创业板指
            '399303.SZ',  # 国证2000
        ]
        
        success_count = 0
        total_count = len(major_indices)
        
        for index, ts_code in enumerate(major_indices):
            print(f"\n📊 处理指数 {index+1}/{total_count}: {ts_code}")
            
            try:
                # 指数日线数据
                daily_result = self.get_index_daily(ts_code)
                
                # 指数成分股权重
                weight_result = self.get_index_weight(ts_code)
                
                if any([daily_result is not None and not daily_result.empty,
                       weight_result is not None and not weight_result.empty]):
                    success_count += 1
                
                # 控制请求频率
                time.sleep(1.0)  # 指数数据请求间隔更长
                
            except Exception as e:
                print(f"❌ 处理 {ts_code} 失败: {e}")
                time.sleep(2)
        
        print(f"\n🎉 指数数据批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_all_industry_data(self):
        """批量缓存行业板块数据(简化版)"""
        print("🔄 开始批量缓存行业板块数据...")
        
        # 主要的申万一级行业指数代码
        main_industries = [
            ('801010.SI', '农林牧渔'),
            ('801020.SI', '采掘'),
            ('801030.SI', '基础化工'),
            ('801040.SI', '钢铁'),
            ('801050.SI', '有色金属'),
            ('801080.SI', '电子'),
            ('801110.SI', '家用电器'),
            ('801120.SI', '食品饮料'),
            ('801150.SI', '医药生物'),
            ('801160.SI', '公用事业'),
            ('801170.SI', '交通运输'),
            ('801180.SI', '房地产'),
            ('801200.SI', '商业贸易'),
            ('801230.SI', '综合'),
            ('801710.SI', '建筑材料'),
            ('801720.SI', '建筑装饰'),
            ('801730.SI', '电气设备'),
            ('801740.SI', '国防军工'),
            ('801750.SI', '计算机'),
            ('801760.SI', '传媒'),
            ('801770.SI', '通信'),
            ('801780.SI', '银行'),
            ('801790.SI', '非银金融'),
            ('801880.SI', '汽车'),
            ('801890.SI', '机械设备'),
        ]
        
        success_count = 0
        total_count = len(main_industries)
        
        print(f"📋 准备处理主要申万行业: {total_count} 个")
        
        for index, (industry_code, industry_name) in enumerate(main_industries):
            print(f"\n📊 处理 {index+1}/{total_count}: {industry_name} ({industry_code})")
            
            try:
                # 只获取申万行业指数日线数据
                daily_result = self.get_sw_industry_daily(industry_code)
                
                if daily_result is not None and not daily_result.empty:
                    success_count += 1
                    print(f"✅ {industry_name} 数据已缓存: {len(daily_result)} 条")
                else:
                    print(f"⚠️ {industry_name} 无数据")
                
                # 控制请求频率
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ 处理 {industry_name} 失败: {e}")
                time.sleep(1)  # 错误时等待更长时间
        
        print(f"\n🎉 行业板块数据批量缓存完成！成功: {success_count}/{total_count}")
    
    def cache_main_industry_indices(self):
        """缓存主要行业指数数据"""
        print("🔄 缓存主要行业指数数据...")
        
        # 主要的申万一级行业指数代码
        main_industries = [
            ('801010.SI', '农林牧渔'),
            ('801020.SI', '采掘'),
            ('801030.SI', '化工'),
            ('801040.SI', '钢铁'),
            ('801050.SI', '有色金属'),
            ('801080.SI', '电子'),
            ('801110.SI', '家用电器'),
            ('801120.SI', '食品饮料'),
            ('801130.SI', '纺织服装'),
            ('801140.SI', '轻工制造'),
            ('801150.SI', '医药生物'),
            ('801160.SI', '公用事业'),
            ('801170.SI', '交通运输'),
            ('801180.SI', '房地产'),
            ('801200.SI', '商业贸易'),
            ('801210.SI', '休闲服务'),
            ('801230.SI', '综合'),
            ('801710.SI', '建筑材料'),
            ('801720.SI', '建筑装饰'),
            ('801730.SI', '电气设备'),
            ('801740.SI', '国防军工'),
            ('801750.SI', '计算机'),
            ('801760.SI', '传媒'),
            ('801770.SI', '通信'),
            ('801780.SI', '银行'),
            ('801790.SI', '非银金融'),
            ('801880.SI', '汽车'),
            ('801890.SI', '机械设备'),
        ]
        
        success_count = 0
        total_count = len(main_industries)
        
        for index, (code, name) in enumerate(main_industries):
            print(f"\n📊 处理 {index+1}/{total_count}: {name} ({code})")
            
            try:
                # 获取行业指数日线数据
                daily_result = self.get_sw_industry_daily(code)
                
                if daily_result is not None and not daily_result.empty:
                    success_count += 1
                    print(f"✅ {name} 数据已缓存: {len(daily_result)} 条")
                else:
                    print(f"⚠️ {name} 无数据")
                
                # 控制请求频率
                time.sleep(0.3)
                
            except Exception as e:
                print(f"❌ 处理 {name} 失败: {e}")
                time.sleep(1)
        
        print(f"\n🎉 主要行业指数缓存完成！成功: {success_count}/{total_count}")
    
    def get_industry_summary(self):
        """获取行业板块数据统计"""
        print("📊 行业板块数据统计")
        print("=" * 40)
        
        if os.path.exists(self.stock_db):
            with sqlite3.connect(self.stock_db) as conn:
                try:
                    # 行业成分股统计
                    industry_count = conn.execute("SELECT COUNT(DISTINCT industry_code) FROM industry_stock").fetchone()[0]
                    stock_count = conn.execute("SELECT COUNT(DISTINCT con_code) FROM industry_stock").fetchone()[0]
                    print(f"📈 行业成分股:")
                    print(f"   行业数量: {industry_count}")
                    print(f"   股票数量: {stock_count}")
                except:
                    print(f"📈 行业成分股: 暂无数据")
                
                try:
                    # 行业指数统计
                    industry_daily_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM industry_daily").fetchone()[0]
                    industry_daily_records = conn.execute("SELECT COUNT(*) FROM industry_daily").fetchone()[0]
                    print(f"\n📊 行业指数日线:")
                    print(f"   行业指数: {industry_daily_count} 个")
                    print(f"   总记录数: {industry_daily_records:,} 条")
                except:
                    print(f"\n📊 行业指数日线: 暂无数据")
                
                try:
                    # 申万行业指数统计
                    sw_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM sw_industry_daily").fetchone()[0]
                    sw_records = conn.execute("SELECT COUNT(*) FROM sw_industry_daily").fetchone()[0]
                    print(f"\n📊 申万行业指数:")
                    print(f"   指数数量: {sw_count} 个")
                    print(f"   总记录数: {sw_records:,} 条")
                except:
                    print(f"\n📊 申万行业指数: 暂无数据")
                
                try:
                    # 资金流向统计
                    money_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM industry_money_flow").fetchone()[0]
                    money_records = conn.execute("SELECT COUNT(*) FROM industry_money_flow").fetchone()[0]
                    print(f"\n💰 行业资金流向:")
                    print(f"   行业数量: {money_count} 个")
                    print(f"   总记录数: {money_records:,} 条")
                except:
                    print(f"\n💰 行业资金流向: 暂无数据")
        else:
            print("❌ 数据库文件不存在")
    
    def upgrade_database_schema(self):
        """升级数据库结构，添加缺失的字段"""
        try:
            # 升级基金数据库
            with sqlite3.connect(self.fund_db) as conn:
                cursor = conn.cursor()
                
                # 检查并添加 ann_date 字段
                cursor.execute("PRAGMA table_info(fund_nav)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'ann_date' not in columns:
                    print("添加 ann_date 字段到 fund_nav 表...")
                    cursor.execute("ALTER TABLE fund_nav ADD COLUMN ann_date TEXT")
                    
                if 'update_flag' not in columns:
                    print("添加 update_flag 字段到 fund_nav 表...")
                    cursor.execute("ALTER TABLE fund_nav ADD COLUMN update_flag TEXT")
                
                conn.commit()
            
            # 升级股票数据库 - 检查财务指标表
            with sqlite3.connect(self.stock_db) as conn:
                cursor = conn.cursor()
                
                # 检查财务指标表是否存在 roa2_yearly 字段
                try:
                    cursor.execute("PRAGMA table_info(stock_fina_indicator)")
                    fina_columns = [column[1] for column in cursor.fetchall()]
                    
                    if 'roa2_yearly' not in fina_columns:
                        print("添加 roa2_yearly 字段到 stock_fina_indicator 表...")
                        cursor.execute("ALTER TABLE stock_fina_indicator ADD COLUMN roa2_yearly REAL")
                except:
                    # 如果表不存在，什么都不做
                    pass
                
                conn.commit()
                
            print("数据库结构升级完成")
                
        except Exception as e:
            print(f"数据库升级出错: {e}")
    
    def get_stock_fundamental_summary(self, ts_code):
        """获取股票基本面数据汇总"""
        print(f"📊 {ts_code} 基本面数据汇总")
        print("=" * 40)
        
        # 获取最新财务指标
        fina_data = self.load_from_database('stock_fina_indicator', self.stock_db, ts_code=ts_code)
        if not fina_data.empty:
            latest_fina = fina_data.iloc[-1]
            print(f"📈 最新财务指标 ({latest_fina['end_date']}):")
            print(f"   每股收益(EPS): {latest_fina.get('eps', 'N/A')}")
            print(f"   每股净资产(BPS): {latest_fina.get('bps', 'N/A')}")
            print(f"   净资产收益率(ROE): {latest_fina.get('roe', 'N/A')}%")
            print(f"   总资产收益率(ROA): {latest_fina.get('roa', 'N/A')}%")
            print(f"   净利润率: {latest_fina.get('netprofit_margin', 'N/A')}%")
            print(f"   毛利润率: {latest_fina.get('grossprofit_margin', 'N/A')}%")
            print(f"   流动比率: {latest_fina.get('current_ratio', 'N/A')}")
            print(f"   资产负债率: {latest_fina.get('debt_to_assets', 'N/A')}%")
        else:
            print("   暂无财务指标数据")
        
        # 获取最新估值数据
        basic_data = self.load_from_database('stock_daily_basic', self.stock_db, ts_code=ts_code)
        if not basic_data.empty:
            latest_basic = basic_data.iloc[-1]
            print(f"\n💰 最新估值指标 ({latest_basic['trade_date']}):")
            print(f"   市盈率(PE): {latest_basic.get('pe', 'N/A')}")
            print(f"   市盈率TTM: {latest_basic.get('pe_ttm', 'N/A')}")
            print(f"   市净率(PB): {latest_basic.get('pb', 'N/A')}")
            print(f"   市销率(PS): {latest_basic.get('ps', 'N/A')}")
            print(f"   总市值(万元): {latest_basic.get('total_mv', 'N/A')}")
            print(f"   流通市值(万元): {latest_basic.get('circ_mv', 'N/A')}")
            print(f"   换手率: {latest_basic.get('turnover_rate', 'N/A')}%")
        else:
            print("\n   暂无估值数据")
        
        # 获取业绩预告
        forecast_data = self.load_from_database('stock_forecast', self.stock_db, ts_code=ts_code)
        if not forecast_data.empty:
            latest_forecast = forecast_data.iloc[-1]
            print(f"\n📋 最新业绩预告 ({latest_forecast['end_date']}):")
            print(f"   变动类型: {latest_forecast.get('type', 'N/A')}")
            print(f"   净利润变动: {latest_forecast.get('p_change_min', 'N/A')}% ~ {latest_forecast.get('p_change_max', 'N/A')}%")
            print(f"   业绩说明: {latest_forecast.get('summary', 'N/A')[:100]}...")
        else:
            print("\n   暂无业绩预告")
        
        return {
            'fina_data': fina_data,
            'basic_data': basic_data,
            'forecast_data': forecast_data
        }
    
    # ==================== 行业板块数据 (SQLite存储) ====================
    
    def init_industry_tables(self):
        """初始化行业板块数据表"""
        with sqlite3.connect(self.stock_db) as conn:
            # 行业成分股表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_stock (
                    industry_code TEXT,
                    con_code TEXT,
                    con_name TEXT,
                    trade_date TEXT,
                    weight REAL,
                    src TEXT,
                    level TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (industry_code, con_code, trade_date, src)
                )
            ''')
            
            # 行业指数日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    turnover_rate REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 申万行业分类历史数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sw_industry_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    name TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    pe REAL,
                    pb REAL,
                    ps REAL,
                    pcf REAL,
                    market_cap REAL,
                    turnover_rate REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 行业资金流向表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_money_flow (
                    ts_code TEXT,
                    trade_date TEXT,
                    buy_sm_amount REAL,
                    buy_md_amount REAL,
                    buy_lg_amount REAL,
                    buy_elg_amount REAL,
                    sell_sm_amount REAL,
                    sell_md_amount REAL,
                    sell_lg_amount REAL,
                    sell_elg_amount REAL,
                    net_mf_amount REAL,
                    net_mf_vol REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_code ON industry_stock(industry_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_con ON industry_stock(con_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_date ON industry_stock(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_daily_code ON industry_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_daily_date ON industry_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sw_industry_code ON sw_industry_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sw_industry_date ON sw_industry_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_money_code ON industry_money_flow(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_money_date ON industry_money_flow(trade_date)')
    
    def get_industry_stocks(self, industry_code, src='SW2021', level='L1', force_update=False):
        """获取行业成分股数据"""
        print(f"🔄 获取行业成分股: {industry_code} ({src} {level})")
        try:
            # 尝试不同的Tushare API方法
            new_data = None
            
            # 方法1: 使用hs_const接口获取成分股
            try:
                new_data = self.pro.hs_const(
                    hs_type=industry_code.replace('.SI', '')
                )
                if not new_data.empty:
                    print(f"   ✓ 通过hs_const获取到数据")
            except:
                pass
            
            # 方法2: 如果是申万行业，尝试sw_member接口
            if (new_data is None or new_data.empty) and 'SW' in src:
                try:
                    new_data = self.pro.sw_member(
                        index_code=industry_code
                    )
                    if not new_data.empty:
                        print(f"   ✓ 通过sw_member获取到数据")
                except:
                    pass
            
            if new_data is None or new_data.empty:
                print(f"⚠️ {industry_code} 无成分股数据")
                return pd.DataFrame()
            
            # 添加来源信息
            new_data['src'] = src
            new_data['level'] = level
            if 'trade_date' not in new_data.columns:
                new_data['trade_date'] = datetime.now().strftime('%Y%m%d')
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'industry_stock', self.stock_db)
            print(f"✅ {industry_code} 成分股数据已保存 ({len(new_data)} 条)")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {industry_code} 成分股失败: {e}")
            return pd.DataFrame()
    
    def get_industry_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取行业指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('industry_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 行业指数")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 行业指数数据...")
        try:
            # 尝试使用不同的接口获取行业指数数据
            new_data = None
            
            # 首先尝试通过指数日线接口
            try:
                new_data = self.pro.index_daily(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                if not new_data.empty:
                    print(f"   ✓ 通过index_daily获取到数据")
            except:
                pass
            
            # 如果失败，尝试申万行业指数接口
            if new_data is None or new_data.empty:
                try:
                    new_data = self.pro.sw_daily(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    if not new_data.empty:
                        print(f"   ✓ 通过sw_daily获取到数据")
                except:
                    pass
            
            if new_data is None or new_data.empty:
                print(f"⚠️ {ts_code} 无行业指数数据")
                if not force_update:
                    return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
                return pd.DataFrame()
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'industry_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('industry_daily', self.stock_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 行业指数失败: {e}")
            if not force_update:
                return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            return None
    
    def get_sw_industry_daily(self, index_code, start_date='20100101', end_date=None, force_update=False):
        """获取申万行业指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('sw_industry_daily', self.stock_db, index_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {index_code} 申万行业")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
        
        print(f"🔄 获取 {index_code} 申万行业指数数据...")
        try:
            new_data = self.pro.sw_daily(
                ts_code=index_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {index_code} 无申万行业数据")
                if not force_update:
                    return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
                return new_data
            
            # 检查并添加缺失的字段到表结构
            self._ensure_sw_industry_columns(new_data.columns)
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'sw_industry_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('sw_industry_daily', self.stock_db, index_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            
        except Exception as e:
            print(f"❌ 获取 {index_code} 申万行业失败: {e}")
            if not force_update:
                return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            return None
    
    def _ensure_sw_industry_columns(self, api_columns):
        """确保sw_industry_daily表包含API返回的所有字段"""
        try:
            with sqlite3.connect(self.stock_db) as conn:
                # 获取当前表结构
                cursor = conn.execute("PRAGMA table_info(sw_industry_daily)")
                existing_columns = [row[1] for row in cursor.fetchall()]
                
                # 检查缺失的字段
                missing_columns = []
                for col in api_columns:
                    if col not in existing_columns and col != 'updated_at':
                        missing_columns.append(col)
                
                # 添加缺失的字段
                for col in missing_columns:
                    try:
                        # 根据字段名猜测数据类型
                        if col in ['ts_code', 'trade_date', 'name']:
                            col_type = 'TEXT'
                        else:
                            col_type = 'REAL'
                        
                        alter_sql = f"ALTER TABLE sw_industry_daily ADD COLUMN {col} {col_type}"
                        conn.execute(alter_sql)
                        print(f"✅ 添加字段: {col} ({col_type})")
                    except Exception as e:
                        print(f"⚠️ 添加字段 {col} 失败: {e}")
                
                if missing_columns:
                    conn.commit()
                    
        except Exception as e:
            print(f"⚠️ 检查表结构失败: {e}")
    
    def get_industry_money_flow(self, ts_code, start_date='20190101', end_date=None, force_update=False):
        """获取行业资金流向数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f" 获取 {ts_code} 行业资金流向数据...")
        try:
            # 由于Tushare可能没有直接的行业资金流向接口，我们跳过这个功能
            print(f"⚠️ {ts_code} 行业资金流向功能暂不可用")
            return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 资金流向失败: {e}")
            return pd.DataFrame()

# ==================== 主入口 ====================

def main():
    """混合存储版主入口，支持SQLite数据库和CSV基础信息"""
    print("🗄️ 本地数据缓存系统 (混合存储版)")
    print("=" * 50)
    cache = DataCache()
    
    print("\n请选择操作模式:")
    print("1. 缓存/更新所有股票数据")
    print("2. 缓存/更新所有基金数据")
    print("3. 缓存/更新股票基本面数据")
    print("4. 缓存市场估值数据(近30天)")
    print("5. 缓存/更新扩展数据(复权、分红、解禁、股东户数)")
    print("6. 缓存/更新指数数据(指数日线、成分股权重)")
    print("7. 缓存/更新分红送转数据(dividend接口)")
    print("8. 缓存/更新行业板块数据(成分股、指数、资金流向)")
    print("9. 缓存主要行业指数数据")
    print("10. 显示数据库统计")
    print("11. 显示基金存储详情")
    print("12. 显示行业板块统计")
    print("13. 预览基金分类")
    print("14. 清理过期缓存")
    
    choice = input("\n请输入选择(1-14): ").strip()
    
    if choice == "1":
        cache.cache_all_stocks_data()
    elif choice == "2":
        cache.cache_all_funds_data()
    elif choice == "3":
        cache.cache_all_fundamental_data()
    elif choice == "4":
        cache.cache_all_daily_basic()
    elif choice == "5":
        cache.cache_all_extended_data()
    elif choice == "6":
        cache.cache_all_index_data()
    elif choice == "7":
        cache.cache_all_comprehensive_dividend()
    elif choice == "8":
        cache.cache_all_industry_data()
    elif choice == "9":
        cache.cache_main_industry_indices()
    elif choice == "10":
        cache.get_database_summary()
    elif choice == "11":
        cache.get_fund_storage_info()
    elif choice == "12":
        cache.get_industry_summary()
    elif choice == "13":
        cache.preview_fund_classification()
    elif choice == "14":
        cache.clean_cache()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()

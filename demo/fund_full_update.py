#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基金全量更新工具
Fund Full Update Tool

专门用于基金数据的全量更新，支持真正的批量处理
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_manager import UpdateManager, DataCache
except ImportError as e:
    print(f"❌ 无法导入数据管理模块: {e}")
    sys.exit(1)


class FundFullUpdater:
    """基金全量更新器"""
    
    def __init__(self):
        """初始化更新器"""
        try:
            self.update_manager = UpdateManager()
            self.data_cache = DataCache()
            print("✅ 基金全量更新器初始化成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def update_all_funds(self, start_date=None, end_date=None, batch_size=1000):
        """更新所有基金数据"""
        print("🚀 开始基金全量更新")
        print("=" * 50)
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        if start_date is None:
            # 默认更新最近30天的数据
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"📅 更新日期范围: {start_date} - {end_date}")
        
        # 获取基金列表
        fund_basic = self.data_cache.get_fund_basic()
        if fund_basic is None or fund_basic.empty:
            print("❌ 无法获取基金基本信息")
            return 0
        
        print(f"📊 基金总数: {len(fund_basic)} 只")
        
        # 分别处理ETF和场外基金（根据代码后缀判断）
        etf_funds = fund_basic[fund_basic['ts_code'].str.contains('\.SH$|\.SZ$', regex=True)]  # 真正的ETF
        regular_funds = fund_basic[fund_basic['ts_code'].str.contains('\.OF$', regex=True)]  # 场外基金
        
        print(f"   ETF基金: {len(etf_funds)} 只")
        print(f"   场外基金: {len(regular_funds)} 只")
        
        total_success = 0
        
        # 1. 更新ETF数据
        if not etf_funds.empty:
            print(f"\n📈 更新ETF数据...")
            etf_success = self._update_etf_funds(etf_funds, start_date, end_date, batch_size)
            total_success += etf_success
        
        # 2. 更新场外基金数据
        if not regular_funds.empty:
            print(f"\n💰 更新场外基金数据...")
            fund_success = self._update_regular_funds(regular_funds, start_date, end_date, batch_size)
            total_success += fund_success
        
        print(f"\n✅ 基金全量更新完成！")
        print(f"📊 总计更新: {total_success:,} 条记录")
        
        return total_success
    
    def _update_etf_funds(self, etf_funds, start_date, end_date, batch_size):
        """更新ETF基金数据（逐个处理，因为批量API不稳定）"""
        print(f"   处理 {len(etf_funds)} 只ETF基金...")

        success_count = 0
        etf_codes = etf_funds['ts_code'].tolist()

        # ETF数据逐个获取（fund_daily API批量查询不稳定）
        for i, etf_code in enumerate(etf_codes):
            print(f"   ETF {i+1}/{len(etf_codes)}: {etf_code}")

            try:
                # 使用单个ETF查询
                etf_data = self.update_manager.api.get_fund_daily(
                    ts_code=etf_code,
                    start_date=start_date,
                    end_date=end_date
                )

                if etf_data is not None and not etf_data.empty:
                    self.update_manager.db.save_to_database(etf_data, 'etf_daily', self.update_manager.config.fund_db)
                    success_count += len(etf_data)
                    print(f"   ✅ {etf_code} 完成: {len(etf_data):,} 条记录")
                else:
                    print(f"   ⚠️ {etf_code} 无数据")

                # 控制API调用频率
                if i < len(etf_codes) - 1:  # 不是最后一个
                    import time
                    time.sleep(0.2)

            except Exception as e:
                print(f"   ❌ {etf_code} 失败: {e}")

        return success_count
    
    def _update_regular_funds(self, regular_funds, start_date, end_date, batch_size):
        """更新场外基金数据"""
        print(f"   处理 {len(regular_funds)} 只场外基金...")
        
        success_count = 0
        fund_codes = regular_funds['ts_code'].tolist()
        
        # 分批处理场外基金
        total_batches = (len(fund_codes) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(fund_codes))
            batch_codes = fund_codes[start_idx:end_idx]
            
            print(f"   基金批次 {batch_idx + 1}/{total_batches}: {len(batch_codes)} 只")
            
            try:
                # 使用基金净值API
                nav_data = self.update_manager.api.get_fund_nav(
                    start_date=start_date,
                    end_date=end_date,
                    ts_codes=batch_codes
                )
                
                if nav_data is not None and not nav_data.empty:
                    self.update_manager.db.save_to_database(nav_data, 'fund_nav', self.update_manager.config.fund_db)
                    success_count += len(nav_data)
                    print(f"   ✅ 基金批次 {batch_idx + 1} 完成: {len(nav_data):,} 条记录")
                else:
                    print(f"   ⚠️ 基金批次 {batch_idx + 1} 无数据")
                
            except Exception as e:
                print(f"   ❌ 基金批次 {batch_idx + 1} 失败: {e}")
        
        return success_count
    
    def update_recent_funds(self, days=7):
        """更新最近几天的基金数据"""
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        
        print(f"🔄 更新最近 {days} 天的基金数据")
        return self.update_all_funds(start_date, end_date, batch_size=500)
    
    def update_fund_sample(self, sample_size=100):
        """更新基金样本数据（用于测试）"""
        print(f"🧪 更新基金样本数据 ({sample_size} 只)")
        
        # 获取基金列表
        fund_basic = self.data_cache.get_fund_basic()
        if fund_basic is None or fund_basic.empty:
            print("❌ 无法获取基金基本信息")
            return 0
        
        # 随机选择样本
        sample_funds = fund_basic.sample(min(sample_size, len(fund_basic)))
        
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        success_count = 0
        
        # 分别处理ETF和场外基金（根据代码后缀判断）
        etf_sample = sample_funds[sample_funds['ts_code'].str.contains('\.SH$|\.SZ$', regex=True)]
        regular_sample = sample_funds[sample_funds['ts_code'].str.contains('\.OF$', regex=True)]
        
        if not etf_sample.empty:
            etf_success = self._update_etf_funds(etf_sample, start_date, end_date, batch_size=50)
            success_count += etf_success
        
        if not regular_sample.empty:
            fund_success = self._update_regular_funds(regular_sample, start_date, end_date, batch_size=50)
            success_count += fund_success
        
        print(f"✅ 样本更新完成: {success_count:,} 条记录")
        return success_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基金全量更新工具')
    parser.add_argument('mode', nargs='?', default='recent',
                       choices=['all', 'recent', 'sample'],
                       help='更新模式: all(全量), recent(最近), sample(样本)')
    parser.add_argument('--start', type=str, help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end', type=str, help='结束日期 (YYYYMMDD)')
    parser.add_argument('--days', type=int, default=7, help='最近天数 (默认7天)')
    parser.add_argument('--batch', type=int, default=1000, help='批次大小 (默认1000)')
    parser.add_argument('--sample', type=int, default=100, help='样本大小 (默认100)')
    
    args = parser.parse_args()
    
    try:
        updater = FundFullUpdater()
        
        if args.mode == 'all':
            # 全量更新
            result = updater.update_all_funds(args.start, args.end, args.batch)
        elif args.mode == 'recent':
            # 最近更新
            result = updater.update_recent_funds(args.days)
        elif args.mode == 'sample':
            # 样本更新
            result = updater.update_fund_sample(args.sample)
        
        if result > 0:
            print(f"\n🎉 更新成功: {result:,} 条记录")
        else:
            print(f"\n⚠️ 更新完成，但无新数据")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()

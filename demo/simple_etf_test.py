#!/usr/bin/env python3
"""
简化的ETF测试脚本
"""

import sys
sys.path.append('.')

# 直接测试chinadata
import chinadata.ca_data as ts
import pandas as pd

# 设置token（从config获取）
from data_manager.config import config
ts.set_token(config.tushare_token)
pro = ts.pro_api()

print("=== 简化ETF测试 ===")

# 首先查看可用的ETF
print("\n1. 查询ETF基本信息:")
try:
    etf_basic = pro.fund_basic(market='E')  # E表示ETF
    print(f"ETF基金数量: {len(etf_basic)}")
    print("前10个ETF:")
    print(etf_basic[['ts_code', 'name', 'management', 'list_date']].head(10))
    
    # 查看我们测试的代码是否在列表中
    test_codes = ['510300.SH', '510500.SH', '510050.SH', '159915.SZ', '159919.SZ']
    print(f"\n检查测试代码是否存在:")
    for code in test_codes:
        exists = code in etf_basic['ts_code'].values
        print(f"  {code}: {'✅存在' if exists else '❌不存在'}")
        
except Exception as e:
    print(f"❌ 获取ETF基本信息失败: {e}")

print("\n2. 测试单个ETF数据获取:")
test_code = '510300.SH'  # 沪深300ETF
try:
    # 测试最近的数据
    result = pro.fund_daily(ts_code=test_code, start_date='20250701', end_date='20250726')
    print(f"  {test_code} 结果:")
    print(f"    类型: {type(result)}")
    print(f"    是否为空: {result.empty if hasattr(result, 'empty') else 'N/A'}")
    if hasattr(result, 'empty') and not result.empty:
        print(f"    数据条数: {len(result)}")
        print(f"    日期范围: {result['trade_date'].min()} - {result['trade_date'].max()}")
        print("    最新几条:")
        print(result.tail(3))
    elif result is None:
        print("    返回None")
    else:
        print(f"    返回值: {result}")
        
except Exception as e:
    print(f"  ❌ 获取失败: {e}")

print("\n3. 测试交易日历:")
try:
    cal = pro.trade_cal(exchange='SSE', start_date='20250720', end_date='20250726')
    print("最近的交易日:")
    print(cal[cal['is_open']==1][['cal_date', 'is_open']])
except Exception as e:
    print(f"❌ 获取交易日历失败: {e}")

"""
简化版量化选股策略演示
Simplified Quantitative Stock Selection Demo

本演示文件展示了经典的量化选股策略，包括：
1. 价值选股策略
2. 动量选股策略  
3. 低波动率策略
4. 多因子综合策略
"""

import pandas as pd
import numpy as np
import sqlite3
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_sample_data():
    """加载示例数据"""
    print("📊 生成示例股票数据...")
    
    # 生成50只示例股票的数据
    np.random.seed(42)  # 确保结果可重现
    
    stock_codes = [f"{i:06d}.{'SZ' if i < 300000 else 'SH'}" for i in range(1, 51)]
    
    data = []
    for i, code in enumerate(stock_codes):
        # 生成随机但合理的财务指标
        pe = np.random.lognormal(2.5, 0.8)  # PE在5-50之间
        pb = np.random.lognormal(0.5, 0.6)  # PB在0.5-5之间
        
        # 生成价格数据（模拟60天）
        prices = []
        price = 10 + np.random.random() * 40  # 初始价格10-50元
        for day in range(60):
            change = np.random.normal(0, 0.02)  # 日收益率标准差2%
            price *= (1 + change)
            prices.append(price)
        
        # 计算技术指标
        returns = np.diff(prices) / prices[:-1]
        momentum_20d = (prices[-1] / prices[-21] - 1) if len(prices) >= 21 else 0
        momentum_60d = (prices[-1] / prices[0] - 1)
        volatility = np.std(returns) * np.sqrt(252)
        
        # 成交量数据
        volume = np.random.lognormal(15, 1)  # 成交量
        volume_ratio = 0.8 + np.random.random() * 0.4  # 量比0.8-1.2
        
        data.append({
            'ts_code': code,
            'name': f'股票{i+1:02d}',
            'latest_price': prices[-1],
            'pe': pe,
            'pb': pb,
            'momentum_20d': momentum_20d,
            'momentum_60d': momentum_60d,
            'volatility': volatility,
            'volume_ratio': volume_ratio,
            'market_cap': prices[-1] * volume * 100,  # 简化市值计算
            'prices': prices,
            'returns': returns
        })
    
    df = pd.DataFrame(data)
    print(f"✅ 生成了 {len(df)} 只示例股票的数据")
    return df

def value_strategy(df, top_n=10):
    """价值选股策略：低PE、低PB"""
    print("\n💰 价值选股策略")
    
    # 过滤有效数据
    valid_df = df[(df['pe'] > 0) & (df['pb'] > 0)].copy()
    
    # 计算价值评分（PE和PB的倒数排名）
    valid_df['pe_rank'] = valid_df['pe'].rank(ascending=True)
    valid_df['pb_rank'] = valid_df['pb'].rank(ascending=True)
    valid_df['value_score'] = (valid_df['pe_rank'] + valid_df['pb_rank']) / 2
    
    # 选择价值评分最高的股票
    selected = valid_df.nsmallest(top_n, 'value_score')
    
    print(f"✅ 选出 {len(selected)} 只价值股:")
    for _, stock in selected.iterrows():
        print(f"  {stock['ts_code']} ({stock['name']}): PE={stock['pe']:.2f}, PB={stock['pb']:.2f}")
    
    return selected

def momentum_strategy(df, top_n=10):
    """动量选股策略：强势股"""
    print("\n🚀 动量选股策略")
    
    # 计算动量评分
    df_copy = df.copy()
    df_copy['momentum_score'] = (df_copy['momentum_20d'] * 0.6 + df_copy['momentum_60d'] * 0.4)
    
    # 选择动量最强的股票
    selected = df_copy.nlargest(top_n, 'momentum_score')
    
    print(f"✅ 选出 {len(selected)} 只动量股:")
    for _, stock in selected.iterrows():
        print(f"  {stock['ts_code']} ({stock['name']}): 20日涨幅={stock['momentum_20d']:.2%}, 60日涨幅={stock['momentum_60d']:.2%}")
    
    return selected

def low_volatility_strategy(df, top_n=10):
    """低波动率策略：稳健股"""
    print("\n📉 低波动率策略")
    
    # 选择波动率最低的股票
    selected = df.nsmallest(top_n, 'volatility')
    
    print(f"✅ 选出 {len(selected)} 只低波动股:")
    for _, stock in selected.iterrows():
        print(f"  {stock['ts_code']} ({stock['name']}): 年化波动率={stock['volatility']:.2%}")
    
    return selected

def multi_factor_strategy(df, top_n=10):
    """多因子综合策略"""
    print("\n🎯 多因子综合策略")
    
    df_copy = df.copy()
    
    # 因子标准化
    df_copy['pe_norm'] = (df_copy['pe'] - df_copy['pe'].mean()) / df_copy['pe'].std()
    df_copy['pb_norm'] = (df_copy['pb'] - df_copy['pb'].mean()) / df_copy['pb'].std()
    df_copy['momentum_norm'] = (df_copy['momentum_20d'] - df_copy['momentum_20d'].mean()) / df_copy['momentum_20d'].std()
    df_copy['vol_norm'] = (df_copy['volatility'] - df_copy['volatility'].mean()) / df_copy['volatility'].std()
    
    # 综合评分：价值(40%) + 动量(30%) + 质量(30%)
    df_copy['multi_score'] = (
        -0.2 * df_copy['pe_norm'] +      # 低PE
        -0.2 * df_copy['pb_norm'] +      # 低PB
        0.3 * df_copy['momentum_norm'] +  # 高动量
        -0.3 * df_copy['vol_norm']       # 低波动
    )
    
    # 选择综合评分最高的股票
    selected = df_copy.nlargest(top_n, 'multi_score')
    
    print(f"✅ 选出 {len(selected)} 只多因子股:")
    for _, stock in selected.iterrows():
        print(f"  {stock['ts_code']} ({stock['name']}): 综合评分={stock['multi_score']:.2f}")
    
    return selected

def simple_backtest(selected_stocks, holding_days=30):
    """简单回测分析"""
    print(f"\n📈 回测分析 - 持仓{holding_days}天")
    
    portfolio_returns = []
    
    for _, stock in selected_stocks.iterrows():
        prices = stock['prices']
        if len(prices) >= holding_days:
            # 计算持仓期收益
            start_price = prices[-holding_days]
            end_price = prices[-1]
            stock_return = (end_price / start_price - 1)
            portfolio_returns.append(stock_return)
    
    if not portfolio_returns:
        print("❌ 无有效回测数据")
        return None
    
    # 计算组合表现
    portfolio_return = np.mean(portfolio_returns)
    portfolio_std = np.std(portfolio_returns)
    win_rate = len([r for r in portfolio_returns if r > 0]) / len(portfolio_returns)
    sharpe_ratio = portfolio_return / portfolio_std if portfolio_std > 0 else 0
    
    print("="*50)
    print("📊 回测结果汇总")
    print("="*50)
    print(f"📈 组合收益率: {portfolio_return:.2%}")
    print(f"📊 收益波动率: {portfolio_std:.2%}")
    print(f"🎯 胜率: {win_rate:.1%}")
    print(f"📊 夏普比率: {sharpe_ratio:.2f}")
    print(f"📊 股票数量: {len(portfolio_returns)}")
    
    return {
        'return': portfolio_return,
        'volatility': portfolio_std,
        'win_rate': win_rate,
        'sharpe_ratio': sharpe_ratio,
        'stock_count': len(portfolio_returns)
    }

def plot_strategy_comparison(results):
    """绘制策略对比图"""
    print("\n📊 生成策略对比图表...")
    
    strategies = list(results.keys())
    returns = [results[s]['return'] for s in strategies]
    volatilities = [results[s]['volatility'] for s in strategies]
    sharpe_ratios = [results[s]['sharpe_ratio'] for s in strategies]
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
    
    # 收益率对比
    ax1.bar(strategies, [r*100 for r in returns], color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    ax1.set_title('策略收益率对比 (%)')
    ax1.set_ylabel('收益率 (%)')
    plt.setp(ax1.get_xticklabels(), rotation=45)
    
    # 波动率对比
    ax2.bar(strategies, [v*100 for v in volatilities], color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    ax2.set_title('策略波动率对比 (%)')
    ax2.set_ylabel('波动率 (%)')
    plt.setp(ax2.get_xticklabels(), rotation=45)
    
    # 夏普比率对比
    ax3.bar(strategies, sharpe_ratios, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    ax3.set_title('策略夏普比率对比')
    ax3.set_ylabel('夏普比率')
    plt.setp(ax3.get_xticklabels(), rotation=45)
    
    # 风险收益散点图
    ax4.scatter(volatilities, returns, s=100, alpha=0.7)
    for i, strategy in enumerate(strategies):
        ax4.annotate(strategy, (volatilities[i], returns[i]), 
                    xytext=(5, 5), textcoords='offset points')
    ax4.set_xlabel('波动率')
    ax4.set_ylabel('收益率')
    ax4.set_title('风险收益散点图')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('strategy_comparison.png', dpi=300, bbox_inches='tight')
    print("✅ 图表已保存为 strategy_comparison.png")
    plt.show()

def run_demo():
    """运行演示"""
    print("🚀 量化选股策略演示")
    print("="*50)
    
    # 加载示例数据
    df = load_sample_data()
    
    # 运行各种策略
    strategies = {
        '价值策略': value_strategy,
        '动量策略': momentum_strategy,
        '低波动策略': low_volatility_strategy,
        '多因子策略': multi_factor_strategy
    }
    
    strategy_results = {}
    
    for strategy_name, strategy_func in strategies.items():
        print(f"\n{'='*20} {strategy_name} {'='*20}")
        
        # 执行选股
        selected_stocks = strategy_func(df.copy())
        
        # 回测分析
        backtest_result = simple_backtest(selected_stocks, holding_days=30)
        
        if backtest_result:
            strategy_results[strategy_name] = backtest_result
    
    # 策略对比总结
    if strategy_results:
        print("\n" + "="*60)
        print("📊 策略对比总结")
        print("="*60)
        
        comparison_data = []
        for strategy_name, result in strategy_results.items():
            comparison_data.append({
                '策略': strategy_name,
                '收益率': f"{result['return']:.2%}",
                '波动率': f"{result['volatility']:.2%}",
                '胜率': f"{result['win_rate']:.1%}",
                '夏普比率': f"{result['sharpe_ratio']:.2f}",
                '股票数': result['stock_count']
            })
        
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # 生成对比图表
        plot_strategy_comparison(strategy_results)
        
        print("\n💡 策略建议:")
        print("1. 价值策略：适合长期投资，关注低估值股票")
        print("2. 动量策略：适合趋势行情，关注强势股票")
        print("3. 低波动策略：适合稳健投资，关注防御性股票")
        print("4. 多因子策略：结合多种因子，分散化投资")

if __name__ == "__main__":
    run_demo()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定期数据更新脚本 - 简化版
用于一键全量更新或快速更新，支持定时任务自动运行

使用方法:
1. 直接运行: python daily_update.py
2. 命令行模式: python daily_update.py --mode quick/full/summary
3. 定时任务设置:
   - Windows计划任务: 每工作日18:00运行
   - Linux crontab: 0 18 * * 1-5 cd /path/to/demo && python daily_update.py --mode quick
"""

import os
import sys
import time
import argparse
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_cache_manager import DataCache

class DailyUpdater:
    """定期数据更新器 - 简化版"""
    
    def __init__(self):
        self.cache = DataCache()
        self.start_time = datetime.now()
        self.log_messages = []
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        self.log_messages.append(log_msg)
    
    def update_all(self):
        """一键全量更新（股票、基金、指数、行业）"""
        self.log("\n🚀 开始全量数据更新...")
        
        # 更新所有股票数据
        self.cache.cache_all_stocks_data()
        
        # 更新所有基金数据
        self.cache.cache_all_funds_data()
        
        # 更新所有指数数据
        self.cache.cache_all_index_data()
        
        # 更新所有行业数据
        self.cache.cache_all_industry_data()
        
        self.log("🎉 全量数据更新完成！")
    
    def update_quick(self):
        """快速更新（仅更新指数、部分热门股票和基金）"""
        self.log("\n🚀 开始快速数据更新...")
        
        # 仅更新主要指数
        self.cache.cache_all_index_data()
        
        # 更新主要行业指数
        self.cache.cache_main_industry_indices()
        
        # 可根据需要添加热门股票/基金的快速更新
        self.log("🎉 快速数据更新完成！")
    
    def show_summary(self):
        """显示数据库最新数据日期和统计"""
        self.log("\n📈 数据更新汇总:")
        
        try:
            # 获取数据库文件大小和基本统计
            if hasattr(self.cache, 'stock_db') and os.path.exists(self.cache.stock_db):
                stock_size = os.path.getsize(self.cache.stock_db) / 1024 / 1024
                self.log(f"📈 股票数据库: {stock_size:.2f} MB")
                
                # 简单查询数据库获取统计信息
                with sqlite3.connect(self.cache.stock_db) as conn:
                    try:
                        stock_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily").fetchone()[0]
                        record_count = conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()[0]
                        self.log(f"   股票数量: {stock_count:,} 只")
                        self.log(f"   日线记录: {record_count:,} 条")
                        
                        # 获取最新数据日期
                        latest_date = conn.execute("SELECT MAX(trade_date) FROM stock_daily").fetchone()[0]
                        if latest_date:
                            self.log(f"   最新日期: {latest_date}")
                    except Exception as e:
                        self.log(f"   ⚠️ 股票数据统计获取失败: {e}")
            
            if hasattr(self.cache, 'fund_db') and os.path.exists(self.cache.fund_db):
                fund_size = os.path.getsize(self.cache.fund_db) / 1024 / 1024
                self.log(f"📊 基金数据库: {fund_size:.2f} MB")
                
                with sqlite3.connect(self.cache.fund_db) as conn:
                    try:
                        etf_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM etf_daily").fetchone()[0]
                        nav_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM fund_nav").fetchone()[0]
                        self.log(f"   ETF数量: {etf_count:,} 只")
                        self.log(f"   基金净值: {nav_count:,} 只")
                    except Exception as e:
                        self.log(f"   ⚠️ 基金数据统计获取失败: {e}")
            
            # 显示基础信息文件
            if hasattr(self.cache, 'basic_dir') and os.path.exists(self.cache.basic_dir):
                csv_files = [f for f in os.listdir(self.cache.basic_dir) if f.endswith('.csv')]
                csv_size = sum(os.path.getsize(os.path.join(self.cache.basic_dir, f)) 
                             for f in csv_files) / 1024 / 1024
                self.log(f"📋 基础信息: {len(csv_files)} 个CSV文件 ({csv_size:.2f} MB)")
                
        except Exception as e:
            self.log(f"❌ 获取汇总信息失败: {e}")
        
        # 显示本次更新耗时
        elapsed = datetime.now() - self.start_time
        self.log(f"\n⏱️ 本次更新耗时: {elapsed.total_seconds():.1f} 秒")
        
        return elapsed

def main():
    """主入口函数"""
    print("📅 A股数据定期更新工具 (简化版)")
    print("=" * 40)
    
    updater = DailyUpdater()
    
    # 如果有命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == 'quick':
            updater.update_quick()
            updater.show_summary()
        elif mode == 'all' or mode == 'full':
            updater.update_all()
            updater.show_summary()
        elif mode == 'summary':
            updater.show_summary()
        else:
            print("用法: python daily_update.py [quick|all|summary]")
    else:
        # 交互模式
        print("请选择更新模式:")
        print("1. 快速更新 (仅更新主要指数和行业)")
        print("2. 全量更新 (更新所有数据)")
        print("3. 查看更新汇总")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            updater.update_quick()
            updater.show_summary()
        elif choice == '2':
            updater.update_all()
            updater.show_summary()
        elif choice == '3':
            updater.show_summary()
        elif choice == '4':
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            return

if __name__ == "__main__":
    main()

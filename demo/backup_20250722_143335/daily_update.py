#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定期数据更新脚本 - 增量更新版
用于一键增量或全量更新，支持定时任务自动运行

使用方法:
1. 直接运行: python daily_update.py
2. 命令行模式: python daily_update.py [quick|all|stocks|funds|indexes|industries|summary] [--force]
3. 定时任务设置:
   - Windows计划任务: 每工作日18:00运行
   - Linux crontab: 0 18 * * 1-5 cd /path/to/demo && python daily_update.py quick
"""

import os
import sys
import time
import argparse
import sqlite3
import pandas as pd
from datetime import datetime, timedelta

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_cache_manager import DataCache

class DailyUpdater:
    """定期数据更新器 - 增量更新版"""
    
    def __init__(self):
        self.cache = DataCache()
        self.start_time = datetime.now()
        self.log_messages = []
        self.last_cleanup_date = None
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        self.log_messages.append(log_msg)
    
    def update_stocks(self, force_update=False):
        """增量更新股票数据"""
        if force_update:
            self.log("\n📈 开始强制更新股票数据...")
        else:
            self.log("\n📈 开始增量更新股票数据...")
        self.cache.cache_all_stocks_data(force_update=force_update)
        self.log("✅ 股票数据更新完成！")
    
    def update_funds(self, force_update=False):
        """增量更新基金数据"""
        if force_update:
            self.log("\n💰 开始强制更新基金数据...")
        else:
            self.log("\n💰 开始增量更新基金数据...")
        self.cache.cache_all_funds_data(force_update=force_update)
        self.log("✅ 基金数据更新完成！")
    
    def update_indexes(self, force_update=False):
        """增量更新指数数据"""
        if force_update:
            self.log("\n📊 开始强制更新指数数据...")
        else:
            self.log("\n📊 开始增量更新指数数据...")
        self.cache.cache_all_index_data(force_update=force_update)
        self.log("✅ 指数数据更新完成！")
    
    def update_industries(self, force_update=False):
        """增量更新行业数据"""
        if force_update:
            self.log("\n🏭 开始强制更新行业数据...")
        else:
            self.log("\n🏭 开始增量更新行业数据...")
        self.cache.cache_all_industry_data(force_update=force_update)
        self.log("✅ 行业数据更新完成！")

    def cleanup_delisted_data(self, force_cleanup=False):
        """清理退市股票和基金数据（每周执行一次）"""
        try:
            # 检查是否需要清理（每周一次）
            cleanup_flag_file = os.path.join(self.cache.basic_dir, "last_cleanup_date.txt")

            should_cleanup = force_cleanup
            if not force_cleanup:
                if os.path.exists(cleanup_flag_file):
                    with open(cleanup_flag_file, 'r') as f:
                        last_cleanup = f.read().strip()
                        last_cleanup_date = datetime.strptime(last_cleanup, '%Y-%m-%d')
                        days_since_cleanup = (datetime.now() - last_cleanup_date).days
                        should_cleanup = days_since_cleanup >= 7  # 每周清理一次
                else:
                    should_cleanup = True  # 首次运行

            if should_cleanup:
                self.log("\n🧹 开始清理退市股票和基金数据...")

                # 导入数据清理器
                try:
                    from data_cleaner import DataCleaner
                    cleaner = DataCleaner(self.cache)

                    # 查找退市股票和基金
                    delisted_stocks = cleaner.find_delisted_stocks()
                    delisted_funds = cleaner.find_delisted_funds()

                    if delisted_stocks or delisted_funds:
                        self.log(f"   发现退市股票: {len(delisted_stocks)} 只")
                        self.log(f"   发现退市基金: {len(delisted_funds)} 只")

                        # 自动清理（不需要用户确认）
                        cleaner.clean_delisted_stocks(delisted_stocks)
                        cleaner.clean_delisted_funds(delisted_funds)
                        cleaner.update_basic_info_files()

                        self.log(f"✅ 清理完成，删除 {cleaner.cleanup_report['total_records_deleted']} 条记录")
                    else:
                        self.log("   无需清理退市数据")

                    # 更新清理日期标记
                    with open(cleanup_flag_file, 'w') as f:
                        f.write(datetime.now().strftime('%Y-%m-%d'))

                except ImportError:
                    self.log("⚠️ 数据清理器模块未找到，跳过清理")
                except Exception as e:
                    self.log(f"⚠️ 数据清理失败: {e}")
            else:
                self.log("ℹ️ 距离上次清理不足7天，跳过清理")

        except Exception as e:
            self.log(f"❌ 清理退市数据失败: {e}")
    
    def update_all(self, force_update=False):
        """一键全量更新（股票、基金、指数、行业）"""
        if force_update:
            self.log("\n🚀 开始强制全量数据更新...")
        else:
            self.log("\n🚀 开始增量数据更新...")
        
        # 依次更新各类数据
        self.update_stocks(force_update)
        self.update_funds(force_update)
        self.update_indexes(force_update)
        self.update_industries(force_update)
        
        if force_update:
            self.log("🎉 强制全量数据更新完成！")
        else:
            self.log("🎉 增量数据更新完成！")
    
    def update_quick(self, force_update=False):
        """快速更新（仅更新指数和行业数据）"""
        if force_update:
            self.log("\n⚡ 开始强制快速数据更新...")
        else:
            self.log("\n⚡ 开始增量快速数据更新...")
        
        # 更新指数数据
        self.update_indexes(force_update)
        
        # 更新行业数据
        self.update_industries(force_update)

        # 定期清理退市数据
        self.cleanup_delisted_data()

        if force_update:
            self.log("🎉 强制快速数据更新完成！")
        else:
            self.log("🎉 增量快速数据更新完成！")
    
    def show_summary(self):
        """显示数据库最新数据日期和统计"""
        self.log("\n📈 数据更新汇总:")
        
        try:
            # 获取数据库文件大小和基本统计
            if hasattr(self.cache, 'stock_db') and os.path.exists(self.cache.stock_db):
                stock_size = os.path.getsize(self.cache.stock_db) / 1024 / 1024
                self.log(f"📈 股票数据库: {stock_size:.2f} MB")
                
                # 简单查询数据库获取统计信息
                with sqlite3.connect(self.cache.stock_db) as conn:
                    try:
                        stock_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily").fetchone()[0]
                        record_count = conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()[0]
                        self.log(f"   股票数量: {stock_count:,} 只")
                        self.log(f"   日线记录: {record_count:,} 条")
                        
                        # 获取最新数据日期
                        latest_date = conn.execute("SELECT MAX(trade_date) FROM stock_daily").fetchone()[0]
                        if latest_date:
                            self.log(f"   最新日期: {latest_date}")
                    except Exception as e:
                        self.log(f"   ⚠️ 股票数据统计获取失败: {e}")
            
            if hasattr(self.cache, 'fund_db') and os.path.exists(self.cache.fund_db):
                fund_size = os.path.getsize(self.cache.fund_db) / 1024 / 1024
                self.log(f"📊 基金数据库: {fund_size:.2f} MB")
                
                with sqlite3.connect(self.cache.fund_db) as conn:
                    try:
                        etf_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM etf_daily").fetchone()[0]
                        nav_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM fund_nav").fetchone()[0]
                        self.log(f"   ETF数量: {etf_count:,} 只")
                        self.log(f"   基金净值: {nav_count:,} 只")
                    except Exception as e:
                        self.log(f"   ⚠️ 基金数据统计获取失败: {e}")
            
            # 显示基础信息文件
            if hasattr(self.cache, 'basic_dir') and os.path.exists(self.cache.basic_dir):
                csv_files = [f for f in os.listdir(self.cache.basic_dir) if f.endswith('.csv')]
                csv_size = sum(os.path.getsize(os.path.join(self.cache.basic_dir, f)) 
                             for f in csv_files) / 1024 / 1024
                self.log(f"📋 基础信息: {len(csv_files)} 个CSV文件 ({csv_size:.2f} MB)")
                
        except Exception as e:
            self.log(f"❌ 获取汇总信息失败: {e}")
        
        # 显示本次更新耗时
        elapsed = datetime.now() - self.start_time
        self.log(f"\n⏱️ 本次更新耗时: {elapsed.total_seconds():.1f} 秒")
        
        return elapsed

def main():
    """主入口函数"""
    print("📅 A股数据定期更新工具 (增量更新版)")
    print("=" * 45)
    
    updater = DailyUpdater()
    
    # 如果有命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        force = '--force' in sys.argv or '-f' in sys.argv
        
        if mode == 'quick':
            updater.update_quick(force_update=force)
            updater.show_summary()
        elif mode == 'all' or mode == 'full':
            updater.update_all(force_update=force)
            updater.show_summary()
        elif mode == 'stocks':
            updater.update_stocks(force_update=force)
            updater.show_summary()
        elif mode == 'funds':
            updater.update_funds(force_update=force)
            updater.show_summary()
        elif mode == 'indexes':
            updater.update_indexes(force_update=force)
            updater.show_summary()
        elif mode == 'industries':
            updater.update_industries(force_update=force)
            updater.show_summary()
        elif mode == 'summary':
            updater.show_summary()
        else:
            print("用法: python daily_update.py [quick|all|stocks|funds|indexes|industries|summary] [--force]")
            print("   --force: 强制全量更新，忽略现有数据")
    else:
        # 交互模式
        print("请选择更新模式:")
        print("1. 快速增量更新 (仅更新指数和行业)")
        print("2. 全量增量更新 (更新所有数据)")
        print("3. 仅增量更新股票数据")
        print("4. 仅增量更新基金数据")
        print("5. 仅增量更新指数数据")
        print("6. 仅增量更新行业数据")
        print("7. 查看更新汇总")
        print("8. 强制全量更新所有数据")
        print("0. 退出")
        
        choice = input("\n请选择 (0-8): ").strip()
        
        if choice == '1':
            updater.update_quick()
            updater.show_summary()
        elif choice == '2':
            updater.update_all()
            updater.show_summary()
        elif choice == '3':
            updater.update_stocks()
            updater.show_summary()
        elif choice == '4':
            updater.update_funds()
            updater.show_summary()
        elif choice == '5':
            updater.update_indexes()
            updater.show_summary()
        elif choice == '6':
            updater.update_industries()
            updater.show_summary()
        elif choice == '7':
            updater.show_summary()
        elif choice == '8':
            print("⚠️ 强制全量更新将重新获取所有历史数据，确认继续？(y/N)")
            confirm = input().strip().lower()
            if confirm == 'y':
                updater.update_all(force_update=True)
                updater.show_summary()
            else:
                print("👋 已取消")
        elif choice == '0':
            print("👋 再见!")
            return
        else:
            print("❌ 无效选择")
            return

if __name__ == "__main__":
    main()

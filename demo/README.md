# 股票数据管理与可视化系统

## 📋 项目概述

这是一个完整的股票数据管理与可视化系统，支持股票、基金、指数和行业数据的获取、存储、更新和可视化分析。

### 🎯 核心功能

1. **数据管理**：股票、基金、指数、行业数据的获取和存储
2. **数据更新**：增量和全量数据更新，支持定时任务
3. **数据可视化**：K线图、技术分析、股票对比、行业分析
4. **量化分析**：基础的量化选股策略和数据分析

## 📁 项目结构

```
demo/
├── README.md                      # 项目说明文档
├── data_manager/                  # 核心数据管理模块
│   ├── __init__.py               # 模块初始化
│   ├── config.py                 # 配置管理
│   ├── api_client.py             # Tushare API客户端
│   ├── database_manager.py       # 数据库管理
│   ├── basic_data_manager.py     # 基础数据管理
│   ├── data_cache_facade.py      # 数据缓存外观类（兼容旧接口）
│   ├── update_manager.py         # 数据更新管理
│   ├── visualization_manager.py  # 可视化管理
│   └── analysis_manager.py       # 数据分析管理
├── daily_update_modular.py       # 日常数据更新脚本
├── visualization_tool.py         # 可视化工具
├── analysis_tool.py              # 数据分析工具
├── quant_demo_simple.py          # 简单量化分析示例
├── quant_stock_selection.py      # 量化选股策略
├── data_cache/                   # 数据存储目录
│   ├── basic/                    # 基础数据缓存
│   ├── stock_data.db            # 股票数据库
│   ├── fund_data.db             # 基金数据库
│   └── index_data.db            # 指数数据库
├── charts/                      # 可视化图片输出目录
└── backup_archive/               # 备份和测试文件
```

## 🔄 模块化重构说明

本项目已完成模块化重构，将原来的 `data_cache_manager.py` 拆分为多个专业模块：

- **config.py** - 配置管理
- **database_manager.py** - 数据库操作
- **api_client.py** - API客户端
- **basic_data_manager.py** - 基础数据管理
- **update_manager.py** - 数据更新管理
- **visualization_manager.py** - 可视化管理
- **data_cache_facade.py** - 外观类，提供向后兼容的统一接口

### 导入方式更新

**新的推荐导入方式**：
```python
from data_manager import DataCache, UpdateManager, VisualizationManager
```

**兼容的旧导入方式**：
```python
from data_manager.data_cache_facade import DataCache
```

所有现有脚本已自动更新导入方式，无需手动修改。

## 🚀 快速开始

### 环境要求

```bash
# Python 3.7+
pip install pandas numpy matplotlib tushare sqlite3
```

### 配置设置

1. **设置Tushare Token**：
   ```python
   # 在 data_manager/config.py 中设置
   TUSHARE_TOKEN = "your_tushare_token_here"
   ```

2. **数据目录**：
   - 数据自动存储在 `data_cache/` 目录
   - 数据库文件：`stock_data.db`、`fund_data.db`、`index_data.db`

### 基本使用

#### 1. 数据更新

```bash
# 快速更新（推荐日常使用）
python3 daily_update_modular.py quick

# 全量更新
python3 daily_update_modular.py all

# 交互模式
python3 daily_update_modular.py
```

#### 2. 数据可视化

```bash
# K线图分析
python3 visualization_tool.py kline 000001.SZ --days 60

# 技术分析
python3 visualization_tool.py technical 600519.SH --days 90

# 股票对比
python3 visualization_tool.py compare 000001.SZ,600000.SH --days 60

# 行业分析
python3 visualization_tool.py sector

# 交互模式
python3 visualization_tool.py
```

#### 3. 量化分析

```bash
# 简单量化分析
python3 quant_demo_simple.py

# 量化选股策略
python3 quant_stock_selection.py

# 数据分析
python3 analyze_cached_data.py
```

## 📊 功能详解

### 数据管理模块

#### 核心类
- **DataCache**：统一数据访问接口
- **UpdateManager**：数据更新管理
- **VisualizationManager**：可视化管理
- **DatabaseManager**：数据库操作
- **TushareClient**：API客户端

#### 支持的数据类型
- **股票数据**：日线数据、估值数据、基础信息
- **基金数据**：ETF日线、基金净值、基础信息
- **指数数据**：主要指数日线数据
- **行业数据**：申万行业指数数据

### 可视化功能

#### K线图分析
- 专业蜡烛图显示
- 移动平均线（MA5/10/20/60）
- 成交量分析
- MACD技术指标

#### 技术分析图
- 布林带分析
- RSI相对强弱指标
- MACD详细分析
- 成交量技术分析

#### 对比分析
- 多股票价格对比
- 归一化涨跌幅显示
- 行业表现对比

#### 图片输出
- 所有图表自动保存到 `charts/` 目录
- 高清PNG格式（300 DPI）
- 文件名包含股票代码和日期
- 完美支持中文字体显示（Mac优化）

### 数据分析

#### 基本功能
```bash
# 数据概览
python3 analysis_tool.py overview

# 股票分析
python3 analysis_tool.py stock --codes "600519.SH,000858.SZ" --days 30

# 基金分析
python3 analysis_tool.py fund --days 30

# 市场概况
python3 analysis_tool.py market

# 数据质量检查
python3 analysis_tool.py quality

# 完整报告
python3 analysis_tool.py report
```

#### 分析功能
- 股票表现分析（涨跌幅、波动率）
- 基金表现分析（ETF、净值基金）
- 市场概况统计（行业分布、基金类型）
- 数据质量检查（抽样验证）
- 数据库统计信息

### 量化策略

#### 基础策略
- 均线策略
- 动量策略
- 价值投资策略

#### 选股策略
- 技术指标筛选
- 基本面筛选
- 综合评分排序

## 🔧 程序化使用

### 数据获取

```python
from data_manager import DataCache

# 初始化
cache = DataCache()

# 获取股票数据
stock_data = cache.get_stock_daily('000001.SZ')
stock_basic = cache.get_stock_basic()

# 获取基金数据
fund_data = cache.get_fund_nav('510300.SH')
etf_data = cache.get_etf_daily('510300.SH')

# 获取指数数据
index_data = cache.get_index_daily('000001.SH')
```

### 数据更新

```python
from data_manager import UpdateManager

# 初始化更新管理器
updater = UpdateManager()

# 更新数据
updater.update_quick()  # 快速更新
updater.update_all()    # 全量更新

# 显示统计
updater.show_summary()
```

### 可视化

```python
from data_manager import VisualizationManager

# 初始化可视化管理器
viz = VisualizationManager()

# 绘制图表
viz.plot_kline('000001.SZ', days=60)
viz.plot_technical_analysis('600519.SH', days=90)
viz.plot_comparison(['000001.SZ', '600000.SH'], days=120)
viz.plot_sector_analysis()
```

## ⚙️ 配置说明

### 主要配置项

```python
# data_manager/config.py

# API配置
TUSHARE_TOKEN = "your_token"
API_RETRY_COUNT = 3
API_RETRY_DELAY = 1.0

# 数据库配置
DB_TIMEOUT = 30.0
DB_MAX_RETRIES = 3

# 缓存配置
CACHE_MAX_AGE_DAYS = 7
CLEANUP_INTERVAL_DAYS = 7
```

### 定时任务设置

#### Linux/Mac (crontab)
```bash
# 每工作日18:00快速更新
0 18 * * 1-5 cd /path/to/demo && python3 daily_update_modular.py quick

# 每周六02:00全量更新
0 2 * * 6 cd /path/to/demo && python3 daily_update_modular.py all
```

#### Windows (计划任务)
- 任务名称：股票数据更新
- 触发器：每工作日18:00
- 操作：`python3 daily_update_modular.py quick`

## 📈 使用建议

### 日常使用流程
1. **数据更新**：每日使用 `quick` 模式更新数据
2. **技术分析**：使用可视化工具分析个股
3. **选股策略**：运行量化选股找到候选股票
4. **深度研究**：对候选股票进行详细技术分析

### 性能优化
- 使用增量更新减少API调用
- 定期清理过期数据
- 合理设置缓存时间

### 注意事项
- 确保Tushare Token有效
- 注意API调用频率限制
- 定期备份重要数据

## 🔍 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证Tushare Token
   - 确认API调用频率

2. **数据库锁定**
   - 等待其他进程完成
   - 检查文件权限
   - 重启程序

3. **中文字体显示**
   - Mac系统已自动配置Arial Unicode MS字体
   - 如有问题请检查系统字体安装

### 日志查看
程序运行时会显示详细日志，包括：
- 数据更新进度
- API调用状态
- 错误信息和建议

## 📞 技术支持

### 项目特点
- **模块化设计**：易于扩展和维护
- **完整功能**：数据获取、存储、分析、可视化一体化
- **高性能**：优化的数据库操作和API调用
- **用户友好**：支持命令行和交互式两种模式

### 扩展建议
- 添加更多技术指标
- 实现实时数据更新
- 增加更多量化策略
- 开发Web界面

---

**版本**：1.0  
**更新时间**：2025-07-22  
**兼容性**：Python 3.7+, macOS/Linux/Windows

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据可视化工具
Stock Data Visualization Tool

提供K线图、技术分析、基金净值、行业分析等可视化功能

使用方法:
1. 交互模式: python visualization_tool.py
2. 命令行模式: python visualization_tool.py kline 000001.SZ
3. 批量分析: python visualization_tool.py compare 000001.SZ,000002.SZ,600000.SH
"""

import os
import sys
import argparse
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_manager import DataCache
    from data_manager.visualization_manager import VisualizationManager
except ImportError as e:
    print(f"❌ 无法导入数据管理模块: {e}")
    print("请确保 data_manager 模块已正确安装")
    print("当前工作目录:", os.getcwd())
    print("Python路径:", sys.path)
    sys.exit(1)

class VisualizationTool:
    """可视化工具主类"""
    
    def __init__(self):
        """初始化可视化工具"""
        try:
            self.data_cache = DataCache()
            self.viz_manager = VisualizationManager(self.data_cache)
            print("✅ 可视化工具初始化成功")
            print("📁 图片将保存到 charts/ 目录")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def plot_kline(self, ts_code, days=120, ma_periods=None):
        """绘制K线图"""
        if ma_periods is None:
            ma_periods = [5, 10, 20, 60]
        
        return self.viz_manager.plot_kline(
            ts_code=ts_code,
            days=days,
            ma_periods=ma_periods,
            show_volume=True,
            show_macd=True
        )
    
    def plot_technical(self, ts_code, days=120):
        """绘制技术分析图"""
        return self.viz_manager.plot_technical_analysis(ts_code, days)
    
    def plot_comparison(self, ts_codes, days=120):
        """绘制股票对比图"""
        if isinstance(ts_codes, str):
            ts_codes = ts_codes.split(',')
        
        return self.viz_manager.plot_comparison(ts_codes, days)
    
    def plot_fund(self, ts_code, days=120):
        """绘制基金净值图"""
        return self.viz_manager.plot_fund_nav(ts_code, days)
    
    def plot_sector(self):
        """绘制行业分析图"""
        return self.viz_manager.plot_sector_analysis()
    
    def get_stock_suggestions(self):
        """获取股票建议列表"""
        stock_basic = self.data_cache.get_stock_basic()
        if stock_basic.empty:
            return []
        
        # 推荐一些知名股票
        suggestions = [
            ('000001.SZ', '平安银行'),
            ('000002.SZ', '万科A'),
            ('600000.SH', '浦发银行'),
            ('600036.SH', '招商银行'),
            ('600519.SH', '贵州茅台'),
            ('000858.SZ', '五粮液'),
            ('002415.SZ', '海康威视'),
            ('300059.SZ', '东方财富'),
            ('600276.SH', '恒瑞医药'),
            ('000725.SZ', '京东方A')
        ]
        
        # 过滤存在的股票
        available_suggestions = []
        for code, name in suggestions:
            if code in stock_basic['ts_code'].values:
                available_suggestions.append((code, name))
        
        return available_suggestions

def run_interactive_mode():
    """交互模式"""
    try:
        tool = VisualizationTool()
        
        while True:
            print("\n" + "="*60)
            print("📊 股票数据可视化工具")
            print("="*60)
            print("请选择功能:")
            print("1. K线图分析")
            print("2. 技术指标分析")
            print("3. 股票对比分析")
            print("4. 基金净值分析")
            print("5. 行业分析")
            print("6. 查看推荐股票")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                # K线图分析
                print("\n📈 K线图分析")
                ts_code = input("请输入股票代码 (如: 000001.SZ): ").strip().upper()
                if ts_code:
                    days = input("请输入天数 (默认120): ").strip()
                    days = int(days) if days.isdigit() else 120
                    
                    ma_input = input("请输入均线周期 (默认5,10,20,60): ").strip()
                    if ma_input:
                        try:
                            ma_periods = [int(x.strip()) for x in ma_input.split(',')]
                        except:
                            ma_periods = [5, 10, 20, 60]
                    else:
                        ma_periods = [5, 10, 20, 60]
                    
                    tool.plot_kline(ts_code, days, ma_periods)
            
            elif choice == '2':
                # 技术指标分析
                print("\n📊 技术指标分析")
                ts_code = input("请输入股票代码 (如: 000001.SZ): ").strip().upper()
                if ts_code:
                    days = input("请输入天数 (默认120): ").strip()
                    days = int(days) if days.isdigit() else 120
                    tool.plot_technical(ts_code, days)
            
            elif choice == '3':
                # 股票对比分析
                print("\n📈 股票对比分析")
                ts_codes = input("请输入股票代码 (用逗号分隔，如: 000001.SZ,600000.SH): ").strip().upper()
                if ts_codes:
                    days = input("请输入天数 (默认120): ").strip()
                    days = int(days) if days.isdigit() else 120
                    tool.plot_comparison(ts_codes, days)
            
            elif choice == '4':
                # 基金净值分析
                print("\n💰 基金净值分析")
                ts_code = input("请输入基金代码 (如: 510300.SH): ").strip().upper()
                if ts_code:
                    days = input("请输入天数 (默认120): ").strip()
                    days = int(days) if days.isdigit() else 120
                    tool.plot_fund(ts_code, days)
            
            elif choice == '5':
                # 行业分析
                print("\n🏭 行业分析")
                tool.plot_sector()
            
            elif choice == '6':
                # 查看推荐股票
                print("\n📋 推荐股票列表:")
                suggestions = tool.get_stock_suggestions()
                if suggestions:
                    for i, (code, name) in enumerate(suggestions, 1):
                        print(f"  {i:2d}. {code} - {name}")
                else:
                    print("  暂无推荐股票")
            
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            # 询问是否继续
            print("\n是否继续其他分析？(y/N)")
            continue_choice = input().strip().lower()
            if continue_choice != 'y':
                print("👋 再见!")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 交互模式执行失败: {e}")
        return False

def run_command_mode(args):
    """命令行模式"""
    try:
        tool = VisualizationTool()
        
        mode = args.mode.lower()
        
        if mode == 'kline':
            if not args.code:
                print("❌ 请提供股票代码")
                return False
            
            print(f"📈 绘制 {args.code} K线图")
            tool.plot_kline(args.code, args.days)
        
        elif mode == 'technical':
            if not args.code:
                print("❌ 请提供股票代码")
                return False
            
            print(f"📊 绘制 {args.code} 技术分析图")
            tool.plot_technical(args.code, args.days)
        
        elif mode == 'compare':
            if not args.code:
                print("❌ 请提供股票代码（用逗号分隔）")
                return False
            
            print(f"📈 绘制股票对比图: {args.code}")
            tool.plot_comparison(args.code, args.days)
        
        elif mode == 'fund':
            if not args.code:
                print("❌ 请提供基金代码")
                return False
            
            print(f"💰 绘制 {args.code} 基金净值图")
            tool.plot_fund(args.code, args.days)
        
        elif mode == 'sector':
            print("🏭 绘制行业分析图")
            tool.plot_sector()
        
        else:
            print("❌ 无效的模式")
            print("支持的模式: kline, technical, compare, fund, sector")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='股票数据可视化工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python visualization_tool.py                           # 交互模式
  python visualization_tool.py kline 000001.SZ           # K线图
  python visualization_tool.py technical 600519.SH       # 技术分析
  python visualization_tool.py compare 000001.SZ,600000.SH  # 股票对比
  python visualization_tool.py fund 510300.SH            # 基金净值
  python visualization_tool.py sector                    # 行业分析

支持的模式:
  kline       K线图分析
  technical   技术指标分析
  compare     股票对比分析
  fund        基金净值分析
  sector      行业分析
        """
    )
    
    parser.add_argument(
        'mode', 
        nargs='?', 
        choices=['kline', 'technical', 'compare', 'fund', 'sector'],
        help='分析模式'
    )
    
    parser.add_argument(
        'code',
        nargs='?',
        help='股票/基金代码'
    )
    
    parser.add_argument(
        '--days', 
        type=int,
        default=120,
        help='分析天数 (默认: 120)'
    )
    
    args = parser.parse_args()
    
    print("📊 股票数据可视化工具")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        if args.mode:
            # 命令行模式
            success = run_command_mode(args)
        else:
            # 交互模式
            success = run_interactive_mode()
        
        if success:
            print(f"\n✅ 程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"\n❌ 程序执行失败: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断程序: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        print(f"⏰ 退出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文字体测试脚本
Chinese Font Test Script

用于检测和测试Mac系统上的中文字体显示
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import sys
import os

def list_available_fonts():
    """列出系统可用字体"""
    print("🔍 检测系统可用字体...")
    
    # 获取所有字体
    fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 过滤中文相关字体
    chinese_fonts = []
    keywords = ['PingFang', 'Hiragino', 'STHeiti', 'SimHei', 'Arial Unicode', 
                'Heiti', 'Apple', 'Microsoft', 'YaHei', 'SimSun', 'KaiTi',
                'WenQuanYi', 'Noto', 'Source Han']
    
    for font in set(fonts):
        for keyword in keywords:
            if keyword.lower() in font.lower():
                chinese_fonts.append(font)
                break
    
    print(f"📊 系统信息: {platform.system()} {platform.release()}")
    print(f"📝 总字体数量: {len(set(fonts))}")
    print(f"🈶 中文相关字体: {len(chinese_fonts)}")
    
    if chinese_fonts:
        print("\n🈶 可用的中文字体:")
        for i, font in enumerate(sorted(chinese_fonts), 1):
            print(f"  {i:2d}. {font}")
    else:
        print("\n⚠️ 未找到中文字体")
    
    return sorted(chinese_fonts)

def test_font_display(font_name):
    """测试指定字体的中文显示效果"""
    print(f"\n🧪 测试字体: {font_name}")
    
    try:
        # 设置字体
        plt.rcParams['font.sans-serif'] = [font_name]
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 测试文本
        test_texts = [
            '股票代码',
            '价格 (元)',
            '成交量',
            '涨跌幅 (%)',
            '移动平均线',
            '技术指标分析',
            '平安银行(000001.SZ)',
            '贵州茅台(600519.SH)'
        ]
        
        # 绘制测试文本
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.9 - i * 0.1, text, fontsize=14, transform=ax.transAxes)
        
        ax.set_title(f'中文字体测试 - {font_name}', fontsize=16, fontweight='bold')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存测试图片
        filename = f"font_test_{font_name.replace(' ', '_')}.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        print(f"✅ 测试图片已保存: {filename}")
        
        plt.close(fig)
        return True
        
    except Exception as e:
        print(f"❌ 字体测试失败: {e}")
        return False

def create_font_comparison():
    """创建字体对比图"""
    print("\n📊 创建字体对比图...")
    
    # Mac常用中文字体
    mac_fonts = [
        'PingFang SC',
        'Hiragino Sans GB', 
        'STHeiti',
        'Arial Unicode MS',
        'Heiti TC'
    ]
    
    # 检查可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    usable_fonts = [font for font in mac_fonts if font in available_fonts]
    
    if not usable_fonts:
        print("❌ 没有找到可用的中文字体")
        return False
    
    print(f"✅ 找到 {len(usable_fonts)} 个可用字体: {usable_fonts}")
    
    # 创建对比图
    fig, axes = plt.subplots(len(usable_fonts), 1, figsize=(12, 2 * len(usable_fonts)))
    if len(usable_fonts) == 1:
        axes = [axes]
    
    test_text = "股票技术分析 - 平安银行(000001.SZ) 价格走势图"
    
    for i, font in enumerate(usable_fonts):
        try:
            ax = axes[i]
            ax.text(0.05, 0.5, test_text, fontsize=14, 
                   fontfamily=font, transform=ax.transAxes)
            ax.text(0.05, 0.1, f"字体: {font}", fontsize=10, 
                   color='gray', transform=ax.transAxes)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            
            # 添加边框
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color('lightgray')
                
        except Exception as e:
            print(f"⚠️ 字体 {font} 测试失败: {e}")
    
    plt.suptitle('Mac中文字体对比测试', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    filename = "mac_chinese_fonts_comparison.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight')
    print(f"✅ 字体对比图已保存: {filename}")
    
    plt.close(fig)
    return True

def recommend_font_setup():
    """推荐字体设置"""
    print("\n💡 Mac字体设置建议:")
    
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 推荐字体优先级
    recommendations = [
        ('PingFang SC', '苹果默认中文字体，现代简洁'),
        ('Hiragino Sans GB', '苹果经典中文字体，清晰易读'),
        ('STHeiti', '华文黑体，兼容性好'),
        ('Arial Unicode MS', '支持中文的Arial字体'),
    ]
    
    print("\n📋 推荐字体列表:")
    for i, (font, desc) in enumerate(recommendations, 1):
        status = "✅ 可用" if font in available_fonts else "❌ 不可用"
        print(f"  {i}. {font} - {desc} [{status}]")
    
    # 找到最佳字体
    best_font = None
    for font, _ in recommendations:
        if font in available_fonts:
            best_font = font
            break
    
    if best_font:
        print(f"\n🎯 推荐使用: {best_font}")
        print("\n📝 在代码中设置:")
        print(f"plt.rcParams['font.sans-serif'] = ['{best_font}']")
        print("plt.rcParams['axes.unicode_minus'] = False")
    else:
        print("\n⚠️ 未找到推荐字体，请安装中文字体")

def install_font_guide():
    """字体安装指南"""
    print("\n📖 Mac中文字体安装指南:")
    print("="*50)
    
    print("\n1. 检查系统字体:")
    print("   打开 '字体册' 应用 -> 查看已安装的中文字体")
    
    print("\n2. 安装新字体:")
    print("   - 下载字体文件(.ttf 或 .otf)")
    print("   - 双击字体文件 -> 点击'安装字体'")
    print("   - 或拖拽到 '字体册' 应用中")
    
    print("\n3. 推荐下载字体:")
    print("   - 思源黑体 (Source Han Sans)")
    print("   - 文泉驿微米黑 (WenQuanYi Micro Hei)")
    print("   - Noto Sans CJK SC")
    
    print("\n4. 重启Python程序:")
    print("   安装字体后需要重启Python程序才能生效")
    
    print("\n5. 清除matplotlib缓存:")
    print("   rm -rf ~/.matplotlib")

def main():
    """主函数"""
    print("🔤 Mac中文字体检测和配置工具")
    print("="*50)
    
    # 1. 列出可用字体
    chinese_fonts = list_available_fonts()
    
    # 2. 推荐字体设置
    recommend_font_setup()
    
    # 3. 创建字体对比图
    if chinese_fonts:
        create_font_comparison()
        
        # 4. 测试最佳字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        mac_fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
        
        for font in mac_fonts:
            if font in available_fonts:
                test_font_display(font)
                break
    else:
        print("\n❌ 没有找到中文字体，显示安装指南...")
        install_font_guide()
    
    print("\n" + "="*50)
    print("📊 检测完成")
    print("="*50)
    
    if chinese_fonts:
        print("✅ 系统支持中文字体显示")
        print("📁 请查看生成的测试图片确认显示效果")
        print("\n🔧 如果字体显示不正常，请:")
        print("1. 重启Python程序")
        print("2. 清除matplotlib缓存: rm -rf ~/.matplotlib")
        print("3. 检查字体是否正确安装")
    else:
        print("⚠️ 系统缺少中文字体支持")
        print("📖 请参考上面的安装指南安装中文字体")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
量化交易策略Demo - 基于缓存数据的简单因子策略

基于已缓存的股票、基金、指数和行业数据，实现简单的量化策略：
1. 均值回归策略
2. 动量策略  
3. 技术指标策略
4. 行业轮动策略
5. 基金投资策略

数据来源：本地SQLite数据库 + CSV基础信息
"""

import os
import sys
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_cache_manager import DataCache

class QuantDemo:
    """量化策略Demo类"""
    
    def __init__(self):
        self.cache = DataCache()
        self.results = {}
        
    def load_stock_data(self, ts_code, days=252):
        """加载股票数据"""
        try:
            with sqlite3.connect(self.cache.stock_db) as conn:
                # 获取最近N天的数据
                query = """
                SELECT * FROM stock_daily 
                WHERE ts_code = ? 
                ORDER BY trade_date DESC 
                LIMIT ?
                """
                df = pd.read_sql(query, conn, params=[ts_code, days])
                if df.empty:
                    return None
                
                df = df.sort_values('trade_date').reset_index(drop=True)
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                return df
        except Exception as e:
            print(f"❌ 加载 {ts_code} 数据失败: {e}")
            return None
    
    def load_multiple_stocks(self, stock_list, days=252):
        """批量加载多只股票数据"""
        stock_data = {}
        for ts_code in stock_list:
            data = self.load_stock_data(ts_code, days)
            if data is not None:
                stock_data[ts_code] = data
        return stock_data
    
    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        if df is None or df.empty:
            return df
        
        # 移动平均线
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma10'] = df['close'].rolling(10).mean()
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma60'] = df['close'].rolling(60).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # 成交量指标
        df['volume_ma5'] = df['vol'].rolling(5).mean()
        df['volume_ratio'] = df['vol'] / df['volume_ma5']
        
        return df
    
    def strategy_mean_reversion(self, df, lookback=20, threshold=2):
        """均值回归策略"""
        if df is None or len(df) < lookback + 10:
            return pd.DataFrame()
        
        df = self.calculate_technical_indicators(df)
        
        # 信号生成：价格偏离20日均线超过2个标准差
        rolling_mean = df['close'].rolling(lookback).mean()
        rolling_std = df['close'].rolling(lookback).std()
        z_score = (df['close'] - rolling_mean) / rolling_std
        
        signals = pd.DataFrame(index=df.index)
        signals['price'] = df['close']
        signals['signal'] = 0
        
        # 买入信号：Z-score < -threshold (超跌)
        signals.loc[z_score < -threshold, 'signal'] = 1
        
        # 卖出信号：Z-score > threshold (超涨)
        signals.loc[z_score > threshold, 'signal'] = -1
        
        # 平仓信号：回归均线
        signals.loc[abs(z_score) < 0.5, 'signal'] = 0
        
        signals['z_score'] = z_score
        signals['position'] = signals['signal'].fillna(method='ffill')
        
        return signals
    
    def strategy_momentum(self, df, short_window=10, long_window=30):
        """动量策略"""
        if df is None or len(df) < long_window + 10:
            return pd.DataFrame()
        
        df = self.calculate_technical_indicators(df)
        
        signals = pd.DataFrame(index=df.index)
        signals['price'] = df['close']
        signals['signal'] = 0
        
        # 双均线策略
        signals['short_ma'] = df['close'].rolling(short_window).mean()
        signals['long_ma'] = df['close'].rolling(long_window).mean()
        
        # 金叉买入
        signals.loc[signals['short_ma'] > signals['long_ma'], 'signal'] = 1
        
        # 死叉卖出
        signals.loc[signals['short_ma'] <= signals['long_ma'], 'signal'] = 0
        
        # 结合RSI过滤
        signals.loc[(signals['signal'] == 1) & (df['rsi'] > 70), 'signal'] = 0  # 超买不买入
        signals.loc[(signals['signal'] == 0) & (df['rsi'] < 30), 'signal'] = 1  # 超卖买入
        
        signals['position'] = signals['signal']
        
        return signals
    
    def strategy_technical(self, df):
        """技术指标综合策略"""
        if df is None or len(df) < 60:
            return pd.DataFrame()
        
        df = self.calculate_technical_indicators(df)
        
        signals = pd.DataFrame(index=df.index)
        signals['price'] = df['close']
        signals['signal'] = 0
        
        # 多重条件买入信号
        buy_conditions = (
            (df['close'] > df['ma5']) &  # 价格在5日线上方
            (df['ma5'] > df['ma20']) &   # 均线多头排列
            (df['rsi'] > 30) & (df['rsi'] < 70) &  # RSI适中
            (df['macd'] > df['macd_signal']) &     # MACD金叉
            (df['volume_ratio'] > 1.2)             # 成交量放大
        )
        
        # 卖出信号
        sell_conditions = (
            (df['close'] < df['ma5']) |  # 价格跌破5日线
            (df['rsi'] > 80) |           # RSI超买
            (df['macd'] < df['macd_signal'])  # MACD死叉
        )
        
        signals.loc[buy_conditions, 'signal'] = 1
        signals.loc[sell_conditions, 'signal'] = -1
        
        # 生成持仓信号
        signals['position'] = 0
        current_position = 0
        
        for i in range(len(signals)):
            if signals.iloc[i]['signal'] == 1 and current_position == 0:
                current_position = 1
            elif signals.iloc[i]['signal'] == -1 and current_position == 1:
                current_position = 0
            signals.iloc[i, signals.columns.get_loc('position')] = current_position
        
        return signals
    
    def backtest_strategy(self, signals, initial_capital=100000, commission=0.001):
        """回测策略表现"""
        if signals.empty:
            return {}
        
        portfolio = pd.DataFrame(index=signals.index)
        portfolio['price'] = signals['price']
        portfolio['position'] = signals['position']
        
        # 计算持仓变化
        portfolio['holdings'] = portfolio['position'] * portfolio['price']
        
        # 计算交易成本
        position_diff = portfolio['position'].diff().fillna(0)
        trade_cost = abs(position_diff) * portfolio['price'] * commission
        trade_cost_cumsum = trade_cost.cumsum()
        
        # 计算现金余额
        cash_changes = -position_diff * portfolio['price']  # 买入为负，卖出为正
        portfolio['cash'] = initial_capital + cash_changes.cumsum() - trade_cost_cumsum
        
        # 计算总资产
        portfolio['total'] = portfolio['holdings'] + portfolio['cash']
        portfolio['returns'] = portfolio['total'].pct_change()
        portfolio['cumulative_returns'] = (1 + portfolio['returns']).cumprod()
        
        # 计算绩效指标
        if portfolio['total'].iloc[-1] == 0:
            return {
                'total_return': 0,
                'annual_return': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0,
                'total_trades': 0,
                'win_rate': 0,
                'final_value': initial_capital
            }
        
        total_return = (portfolio['total'].iloc[-1] / initial_capital - 1) * 100
        annual_return = total_return * (252 / len(portfolio)) if len(portfolio) > 0 else 0
        
        returns = portfolio['returns'].dropna()
        sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if len(returns) > 0 and returns.std() > 0 else 0
        
        # 计算最大回撤
        running_max = portfolio['total'].expanding().max()
        drawdown = (portfolio['total'] / running_max - 1) * 100
        max_drawdown = drawdown.min()
        
        # 交易统计
        trades = abs(position_diff).sum() / 2  # 买卖一对算一次交易
        win_rate = len(returns[returns > 0]) / len(returns) * 100 if len(returns) > 0 else 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': trades,
            'win_rate': win_rate,
            'final_value': portfolio['total'].iloc[-1]
        }
    
    def run_single_stock_demo(self, ts_code='000001.SZ', days=252):
        """运行单只股票的策略demo"""
        print(f"\n📊 运行单只股票策略Demo: {ts_code}")
        print("=" * 50)
        
        # 加载数据
        df = self.load_stock_data(ts_code, days)
        if df is None:
            print(f"❌ 无法加载 {ts_code} 数据")
            return
        
        print(f"📈 数据加载成功: {len(df)} 条记录 ({df['trade_date'].min()} - {df['trade_date'].max()})")
        
        strategies = {
            '均值回归策略': self.strategy_mean_reversion(df.copy()),
            '动量策略': self.strategy_momentum(df.copy()),
            '技术指标策略': self.strategy_technical(df.copy())
        }
        
        results = {}
        for name, signals in strategies.items():
            if not signals.empty:
                performance = self.backtest_strategy(signals)
                results[name] = performance
                
                print(f"\n🎯 {name} 回测结果:")
                print(f"   总收益率: {performance['total_return']:.2f}%")
                print(f"   年化收益率: {performance['annual_return']:.2f}%")
                print(f"   夏普比率: {performance['sharpe_ratio']:.2f}")
                print(f"   最大回撤: {performance['max_drawdown']:.2f}%")
                print(f"   交易次数: {performance['total_trades']:.0f}")
                print(f"   胜率: {performance['win_rate']:.1f}%")
        
        return results
    
    def get_top_stocks(self, limit=20):
        """获取市值前N的股票"""
        try:
            with sqlite3.connect(self.cache.stock_db) as conn:
                # 从daily_basic表获取最新的市值数据
                query = """
                SELECT ts_code, MAX(trade_date) as latest_date, total_mv
                FROM stock_daily_basic 
                WHERE total_mv > 0
                GROUP BY ts_code
                ORDER BY total_mv DESC
                LIMIT ?
                """
                df = pd.read_sql(query, conn, params=[limit])
                return df['ts_code'].tolist()
        except Exception as e:
            print(f"⚠️ 获取股票列表失败: {e}")
            # 备用方案：使用基础信息中的股票
            stock_basic = self.cache.get_stock_basic()
            if stock_basic is not None:
                return stock_basic.head(limit)['ts_code'].tolist()
            return []
    
    def run_portfolio_demo(self, stock_count=10, days=252):
        """运行投资组合策略demo"""
        print(f"\n📊 运行投资组合策略Demo (前{stock_count}只股票)")
        print("=" * 50)
        
        # 获取股票列表
        stock_list = self.get_top_stocks(stock_count)
        print(f"📋 选中股票: {stock_list}")
        
        # 加载数据
        stock_data = self.load_multiple_stocks(stock_list, days)
        print(f"📈 成功加载 {len(stock_data)} 只股票数据")
        
        if len(stock_data) == 0:
            print("❌ 无可用数据")
            return
        
        # 对每只股票运行技术指标策略
        portfolio_results = {}
        
        for ts_code, df in stock_data.items():
            signals = self.strategy_technical(df)
            if not signals.empty:
                performance = self.backtest_strategy(signals, initial_capital=10000)  # 每只股票1万元
                portfolio_results[ts_code] = performance
        
        # 汇总投资组合表现
        if portfolio_results:
            total_return = np.mean([r['total_return'] for r in portfolio_results.values()])
            total_value = sum([r['final_value'] for r in portfolio_results.values()])
            best_stock = max(portfolio_results.items(), key=lambda x: x[1]['total_return'])
            worst_stock = min(portfolio_results.items(), key=lambda x: x[1]['total_return'])
            
            print(f"\n🎯 投资组合整体表现:")
            print(f"   平均收益率: {total_return:.2f}%")
            print(f"   组合总价值: {total_value:,.0f} 元")
            print(f"   最佳股票: {best_stock[0]} ({best_stock[1]['total_return']:.2f}%)")
            print(f"   最差股票: {worst_stock[0]} ({worst_stock[1]['total_return']:.2f}%)")
            
            # 显示前5名表现
            sorted_results = sorted(portfolio_results.items(), key=lambda x: x[1]['total_return'], reverse=True)
            print(f"\n🏆 表现前5名:")
            for i, (stock, perf) in enumerate(sorted_results[:5]):
                print(f"   {i+1}. {stock}: {perf['total_return']:.2f}% (夏普: {perf['sharpe_ratio']:.2f})")
        
        return portfolio_results
    
    def run_industry_demo(self):
        """运行行业轮动策略demo"""
        print(f"\n📊 运行行业轮动策略Demo")
        print("=" * 50)
        
        try:
            with sqlite3.connect(self.cache.stock_db) as conn:
                # 获取申万行业指数数据
                query = """
                SELECT ts_code, name, COUNT(*) as data_count, 
                       MIN(trade_date) as start_date, MAX(trade_date) as end_date
                FROM sw_industry_daily 
                GROUP BY ts_code, name
                HAVING data_count > 100
                ORDER BY data_count DESC
                LIMIT 10
                """
                industry_list = pd.read_sql(query, conn)
                
                if industry_list.empty:
                    print("⚠️ 无可用行业数据")
                    return
                
                print(f"📋 找到 {len(industry_list)} 个行业指数")
                
                # 分析每个行业的近期表现
                industry_performance = {}
                
                for _, row in industry_list.iterrows():
                    ts_code = row['ts_code']
                    name = row['name'] if pd.notna(row['name']) else ts_code
                    
                    # 获取最近60天数据
                    query2 = """
                    SELECT * FROM sw_industry_daily 
                    WHERE ts_code = ? 
                    ORDER BY trade_date DESC 
                    LIMIT 60
                    """
                    df = pd.read_sql(query2, conn, params=[ts_code])
                    
                    if len(df) >= 20:
                        df = df.sort_values('trade_date').reset_index(drop=True)
                        df['trade_date'] = pd.to_datetime(df['trade_date'])
                        
                        # 计算收益率
                        df['returns'] = df['close'].pct_change()
                        total_return = (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100
                        volatility = df['returns'].std() * np.sqrt(252) * 100
                        
                        industry_performance[name] = {
                            'ts_code': ts_code,
                            'return': total_return,
                            'volatility': volatility,
                            'sharpe': total_return / volatility if volatility > 0 else 0
                        }
                
                # 显示行业排名
                if industry_performance:
                    sorted_industries = sorted(industry_performance.items(), key=lambda x: x[1]['return'], reverse=True)
                    
                    print(f"\n🏆 行业表现排名 (近60天):")
                    for i, (industry_name, perf) in enumerate(sorted_industries):
                        print(f"   {i+1}. {industry_name} ({perf['ts_code']}): {perf['return']:.2f}% (波动率: {perf['volatility']:.1f}%)")
                
                return industry_performance
                
        except Exception as e:
            print(f"❌ 行业分析失败: {e}")
            return {}

def main():
    """主函数"""
    print("📈 量化交易策略Demo")
    print("=" * 40)
    
    demo = QuantDemo()
    
    print("请选择Demo类型:")
    print("1. 单只股票策略测试")
    print("2. 投资组合策略测试")
    print("3. 行业轮动分析")
    print("4. 运行所有Demo")
    print("5. 退出")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == '1':
        ts_code = input("请输入股票代码 (默认: 000001.SZ): ").strip() or '000001.SZ'
        demo.run_single_stock_demo(ts_code)
        
    elif choice == '2':
        count = input("请输入股票数量 (默认: 10): ").strip() or '10'
        demo.run_portfolio_demo(int(count))
        
    elif choice == '3':
        demo.run_industry_demo()
        
    elif choice == '4':
        demo.run_single_stock_demo('000001.SZ')
        demo.run_portfolio_demo(5)
        demo.run_industry_demo()
        
    elif choice == '5':
        print("👋 再见!")
        
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()

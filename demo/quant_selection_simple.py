#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版量化选股策略
Simplified Quantitative Stock Selection

提供核心的量化选股功能，去除复杂的回测和投资计划生成
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_manager import DataCache
except ImportError as e:
    print(f"❌ 无法导入数据管理模块: {e}")
    sys.exit(1)


class SimpleQuantSelector:
    """简化版量化选股器"""
    
    def __init__(self):
        """初始化选股器"""
        try:
            self.cache = DataCache()
            print("✅ 量化选股器初始化成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def get_stock_universe(self, min_market_cap=50, exclude_st=True):
        """获取股票池"""
        print("🔍 构建股票池...")
        
        # 获取股票基本信息
        stock_basic = self.cache.get_stock_basic()
        if stock_basic is None or stock_basic.empty:
            print("❌ 无法获取股票基本信息")
            return []
        
        # 过滤条件
        filtered_stocks = stock_basic.copy()
        
        # 排除ST股票
        if exclude_st:
            filtered_stocks = filtered_stocks[~filtered_stocks['name'].str.contains('ST|退', na=False)]
        
        # 只保留主板和创业板
        filtered_stocks = filtered_stocks[filtered_stocks['market'].isin(['主板', '创业板', '科创板'])]
        
        print(f"✅ 股票池构建完成: {len(filtered_stocks)} 只股票")
        return filtered_stocks['ts_code'].tolist()
    
    def calculate_indicators(self, stock_codes, days=60):
        """计算技术指标"""
        print(f"📊 计算 {len(stock_codes)} 只股票的技术指标...")
        
        results = []
        processed = 0
        
        for ts_code in stock_codes:
            try:
                # 获取股票数据
                stock_data = self.cache.get_stock_daily(ts_code, from_db=True)
                if stock_data.empty:
                    continue
                
                # 取最近数据
                recent_data = stock_data.tail(days)
                if len(recent_data) < 20:  # 至少需要20天数据
                    continue
                
                # 计算基本指标
                current_price = recent_data['close'].iloc[-1]
                
                # 动量指标
                momentum_20d = (current_price / recent_data['close'].iloc[-20] - 1) * 100 if len(recent_data) >= 20 else None
                momentum_5d = (current_price / recent_data['close'].iloc[-5] - 1) * 100 if len(recent_data) >= 5 else None
                
                # 波动率
                returns = recent_data['close'].pct_change().dropna()
                volatility = returns.std() * 100 if len(returns) > 0 else None
                
                # 成交量比率
                avg_volume = recent_data['vol'].tail(20).mean()
                current_volume = recent_data['vol'].iloc[-1]
                volume_ratio = current_volume / avg_volume if avg_volume > 0 else None
                
                # 获取估值数据
                basic_data = self.cache.get_stock_daily_basic(ts_code, from_db=True)
                pe, pb = None, None
                if not basic_data.empty:
                    latest_basic = basic_data.tail(1)
                    pe = latest_basic['pe'].iloc[0] if 'pe' in latest_basic.columns else None
                    pb = latest_basic['pb'].iloc[0] if 'pb' in latest_basic.columns else None
                
                results.append({
                    'ts_code': ts_code,
                    'current_price': current_price,
                    'momentum_20d': momentum_20d,
                    'momentum_5d': momentum_5d,
                    'volatility': volatility,
                    'volume_ratio': volume_ratio,
                    'pe': pe,
                    'pb': pb
                })
                
                processed += 1
                if processed % 100 == 0:
                    print(f"   已处理: {processed}/{len(stock_codes)}")
                
            except Exception as e:
                continue
        
        df = pd.DataFrame(results)
        print(f"✅ 指标计算完成: {len(df)} 只股票有效")
        return df
    
    def value_strategy(self, df, top_n=10):
        """价值选股策略：低PE、低PB"""
        print("\n💰 价值选股策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['pe', 'pb'])
        valid_df = valid_df[(valid_df['pe'] > 0) & (valid_df['pb'] > 0)]
        
        if len(valid_df) == 0:
            print("❌ 无有效估值数据")
            return []
        
        # 计算价值评分（PE和PB越低越好）
        valid_df['value_score'] = (1 / valid_df['pe']) + (1 / valid_df['pb'])
        
        # 选择评分最高的股票
        selected = valid_df.nlargest(top_n, 'value_score')
        
        print(f"✅ 选出 {len(selected)} 只价值股")
        for _, stock in selected.head(5).iterrows():
            print(f"   {stock['ts_code']}: PE={stock['pe']:.2f}, PB={stock['pb']:.2f}")
        
        return selected['ts_code'].tolist()
    
    def momentum_strategy(self, df, top_n=10):
        """动量选股策略：强势股"""
        print("\n🚀 动量选股策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['momentum_20d', 'momentum_5d'])
        
        if len(valid_df) == 0:
            print("❌ 无有效动量数据")
            return []
        
        # 计算动量评分
        valid_df['momentum_score'] = valid_df['momentum_20d'] * 0.7 + valid_df['momentum_5d'] * 0.3
        
        # 选择动量最强的股票
        selected = valid_df.nlargest(top_n, 'momentum_score')
        
        print(f"✅ 选出 {len(selected)} 只强势股")
        for _, stock in selected.head(5).iterrows():
            print(f"   {stock['ts_code']}: 20日涨幅={stock['momentum_20d']:.2f}%")
        
        return selected['ts_code'].tolist()
    
    def low_volatility_strategy(self, df, top_n=10):
        """低波动率策略：稳健股"""
        print("\n📉 低波动率策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['volatility'])
        valid_df = valid_df[valid_df['volatility'] > 0]
        
        if len(valid_df) == 0:
            print("❌ 无有效波动率数据")
            return []
        
        # 选择波动率最低的股票
        selected = valid_df.nsmallest(top_n, 'volatility')
        
        print(f"✅ 选出 {len(selected)} 只低波动股")
        for _, stock in selected.head(5).iterrows():
            print(f"   {stock['ts_code']}: 波动率={stock['volatility']:.2f}%")
        
        return selected['ts_code'].tolist()
    
    def quality_strategy(self, df, top_n=10):
        """质量选股策略：综合质量评分"""
        print("\n⭐ 质量选股策略")
        
        # 过滤有效数据
        required_cols = ['pe', 'pb', 'volatility', 'volume_ratio']
        valid_df = df.dropna(subset=required_cols)
        valid_df = valid_df[(valid_df['pe'] > 0) & (valid_df['pb'] > 0) & (valid_df['volatility'] > 0)]
        
        if len(valid_df) == 0:
            print("❌ 无足够的质量数据")
            return []
        
        # 计算质量评分（标准化后综合）
        valid_df['pe_score'] = 1 / valid_df['pe']  # PE越低越好
        valid_df['pb_score'] = 1 / valid_df['pb']  # PB越低越好
        valid_df['vol_score'] = 1 / valid_df['volatility']  # 波动率越低越好
        valid_df['volume_score'] = np.log(valid_df['volume_ratio'])  # 成交量适中
        
        # 标准化评分
        for col in ['pe_score', 'pb_score', 'vol_score', 'volume_score']:
            valid_df[col] = (valid_df[col] - valid_df[col].mean()) / valid_df[col].std()
        
        # 综合评分
        valid_df['quality_score'] = (valid_df['pe_score'] + valid_df['pb_score'] + 
                                   valid_df['vol_score'] + valid_df['volume_score']) / 4
        
        # 选择质量评分最高的股票
        selected = valid_df.nlargest(top_n, 'quality_score')
        
        print(f"✅ 选出 {len(selected)} 只质量股")
        for _, stock in selected.head(5).iterrows():
            print(f"   {stock['ts_code']}: 质量评分={stock['quality_score']:.2f}")
        
        return selected['ts_code'].tolist()
    
    def run_selection(self, strategy='all', top_n=10):
        """运行选股策略"""
        print("🚀 简化版量化选股系统")
        print("=" * 40)
        
        # 获取股票池
        stock_list = self.get_stock_universe()
        if not stock_list:
            print("❌ 无法构建股票池")
            return {}
        
        # 计算指标
        df = self.calculate_indicators(stock_list[:500])  # 限制处理数量
        if df.empty:
            print("❌ 无法计算技术指标")
            return {}
        
        # 运行策略
        strategies = {
            'value': ('价值策略', self.value_strategy),
            'momentum': ('动量策略', self.momentum_strategy),
            'low_vol': ('低波动策略', self.low_volatility_strategy),
            'quality': ('质量策略', self.quality_strategy)
        }
        
        results = {}
        
        if strategy == 'all':
            # 运行所有策略
            for strategy_key, (strategy_name, strategy_func) in strategies.items():
                print(f"\n{'='*15} {strategy_name} {'='*15}")
                selected_stocks = strategy_func(df.copy(), top_n)
                results[strategy_key] = {
                    'name': strategy_name,
                    'stocks': selected_stocks
                }
        else:
            # 运行指定策略
            if strategy in strategies:
                strategy_name, strategy_func = strategies[strategy]
                print(f"\n{'='*15} {strategy_name} {'='*15}")
                selected_stocks = strategy_func(df.copy(), top_n)
                results[strategy] = {
                    'name': strategy_name,
                    'stocks': selected_stocks
                }
            else:
                print(f"❌ 未知策略: {strategy}")
                return {}
        
        # 打印结果汇总
        self.print_summary(results)
        
        return results
    
    def print_summary(self, results):
        """打印结果汇总"""
        print("\n" + "=" * 50)
        print("📊 选股结果汇总")
        print("=" * 50)
        
        for strategy_key, result in results.items():
            print(f"\n{result['name']}:")
            if result['stocks']:
                for i, stock in enumerate(result['stocks'][:5], 1):
                    print(f"  {i}. {stock}")
                if len(result['stocks']) > 5:
                    print(f"  ... 共{len(result['stocks'])}只股票")
            else:
                print("  无选中股票")
        
        print(f"\n⏰ 选股完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("💡 投资建议: 请结合市场环境和个人风险偏好进行投资决策")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简化版量化选股工具')
    parser.add_argument('strategy', nargs='?', default='all',
                       choices=['all', 'value', 'momentum', 'low_vol', 'quality'],
                       help='选股策略')
    parser.add_argument('--top', type=int, default=10, help='选股数量 (默认10只)')
    
    args = parser.parse_args()
    
    try:
        selector = SimpleQuantSelector()
        results = selector.run_selection(args.strategy, args.top)
        
        if results:
            print("\n✅ 选股完成")
        else:
            print("\n❌ 选股失败")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()

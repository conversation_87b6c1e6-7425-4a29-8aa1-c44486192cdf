# 量化选股策略系统说明

## 📋 文件概述

本目录新增了两个量化选股策略文件：

### 1. `quant_stock_selection.py` - 完整版量化选股系统
- **功能**：基于真实数据的完整量化选股策略系统
- **数据源**：使用项目中的SQLite数据库和Tushare API数据
- **特点**：包含完整的数据处理、选股策略和回测分析功能

### 2. `quant_demo_simple.py` - 简化版演示系统
- **功能**：使用模拟数据的量化选股策略演示
- **数据源**：程序内生成的随机但合理的股票数据
- **特点**：快速演示各种选股策略的效果，包含可视化图表

## 🎯 实现的选股策略

### 1. 价值选股策略 (Value Strategy)
- **原理**：寻找低估值的股票
- **指标**：低PE（市盈率）、低PB（市净率）
- **适用场景**：长期投资，熊市或震荡市
- **风险特征**：相对稳健，但可能存在价值陷阱

### 2. 动量选股策略 (Momentum Strategy)
- **原理**：追踪强势上涨的股票
- **指标**：20日涨幅、60日涨幅
- **适用场景**：牛市或趋势明确的市场
- **风险特征**：收益潜力大，但波动性较高

### 3. 低波动率策略 (Low Volatility Strategy)
- **原理**：选择价格波动较小的稳健股票
- **指标**：年化波动率
- **适用场景**：风险厌恶型投资者，市场不确定性高时
- **风险特征**：风险较低，但收益也相对有限

### 4. 质量选股策略 (Quality Strategy)
- **原理**：综合考虑估值、波动率等质量指标
- **指标**：PE、PB、波动率的综合评分
- **适用场景**：平衡型投资，追求稳健收益
- **风险特征**：风险收益相对平衡

### 5. 多因子综合策略 (Multi-Factor Strategy)
- **原理**：结合价值、动量、质量等多个因子
- **权重**：价值因子40%、动量因子30%、质量因子30%
- **适用场景**：分散化投资，降低单一因子风险
- **风险特征**：通过因子分散化降低整体风险

## 📊 回测分析指标

### 核心指标
1. **组合收益率**：选中股票组合的平均收益率
2. **收益波动率**：收益率的标准差，衡量风险
3. **胜率**：盈利股票占总股票数的比例
4. **夏普比率**：风险调整后的收益率指标
5. **超额收益**：相对于基准指数的超额表现

### 基准对比
- 使用沪深300指数作为基准
- 计算策略相对基准的超额收益
- 评估策略的有效性

## 🚀 使用方法

### 运行完整版系统
```bash
cd demo
python quant_stock_selection.py
```

**功能菜单**：
1. 运行策略对比分析 - 同时运行所有策略并对比
2. 单独运行价值策略 - 仅运行价值选股策略
3. 单独运行动量策略 - 仅运行动量选股策略
4. 单独运行低波动策略 - 仅运行低波动率策略
5. 单独运行质量策略 - 仅运行质量选股策略
6. 单独运行多因子策略 - 仅运行多因子综合策略

### 运行简化版演示
```bash
cd demo
python quant_demo_simple.py
```

**自动执行**：
- 生成50只模拟股票数据
- 运行所有选股策略
- 进行回测分析
- 生成策略对比图表
- 保存图表为 `strategy_comparison.png`

## 📈 演示结果示例

基于模拟数据的策略表现（30天持仓期）：

| 策略 | 收益率 | 波动率 | 胜率 | 夏普比率 | 股票数 |
|------|--------|--------|------|----------|--------|
| 价值策略 | -3.98% | 6.87% | 30.0% | -0.58 | 10 |
| 动量策略 | 10.48% | 5.15% | 100.0% | 2.03 | 10 |
| 低波动策略 | -4.33% | 6.85% | 40.0% | -0.63 | 10 |
| 多因子策略 | 5.66% | 5.58% | 80.0% | 1.02 | 10 |

**结果分析**：
- **动量策略**表现最佳，收益率10.48%，夏普比率2.03
- **多因子策略**表现稳健，收益率5.66%，胜率80%
- **价值策略**和**低波动策略**在此期间表现较差

## 💡 策略应用建议

### 1. 市场环境适应性
- **牛市**：优先考虑动量策略
- **熊市**：重点关注价值策略和低波动策略
- **震荡市**：使用多因子策略分散风险

### 2. 投资者类型匹配
- **激进型**：动量策略
- **稳健型**：质量策略、多因子策略
- **保守型**：低波动策略、价值策略

### 3. 组合构建建议
- 可以将多种策略结合使用
- 根据市场环境动态调整策略权重
- 定期重新平衡投资组合

## ⚠️ 风险提示

1. **历史表现不代表未来收益**
2. **模拟数据与真实市场存在差异**
3. **策略需要根据市场环境调整**
4. **建议结合基本面分析**
5. **注意控制仓位和风险**

## 🔧 技术特点

### 数据处理
- 支持大规模股票数据处理
- 智能数据清洗和异常值处理
- 多维度财务指标计算

### 策略框架
- 模块化设计，易于扩展
- 标准化的策略接口
- 灵活的参数配置

### 回测系统
- 完整的回测分析框架
- 多种风险收益指标
- 基准对比分析

### 可视化
- 策略对比图表
- 风险收益散点图
- 多维度性能分析

## 📚 扩展建议

1. **增加更多因子**：ROE、ROA、营收增长率等
2. **优化权重配置**：使用机器学习优化因子权重
3. **动态调仓**：根据市场环境动态调整持仓
4. **风险管理**：加入止损、仓位控制等风险管理机制
5. **实盘验证**：在真实市场环境中验证策略有效性

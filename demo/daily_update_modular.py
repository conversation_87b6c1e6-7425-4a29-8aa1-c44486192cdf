#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块化日常数据更新脚本
Modular Daily Data Update Script

基于新的模块化数据管理系统的日常更新脚本
支持命令行和交互式两种模式

使用方法:
1. 直接运行: python daily_update_modular.py
2. 命令行模式: python daily_update_modular.py [quick|all|stocks|funds|indexes|industries|summary] [--force]
3. 定时任务设置:
   - Windows计划任务: 每工作日18:00运行
   - Linux crontab: 0 18 * * 1-5 cd /path/to/demo && python daily_update_modular.py quick
"""

import os
import sys
import argparse
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_manager import UpdateManager, DataCache
except ImportError:
    print("❌ 无法导入模块化数据管理系统")
    print("请确保 data_manager 模块已正确安装")
    sys.exit(1)

class ModularDailyUpdater:
    """模块化日常数据更新器"""
    
    def __init__(self):
        """初始化更新器"""
        try:
            self.update_manager = UpdateManager()
            self.data_cache = DataCache()
            print("✅ 模块化数据管理系统初始化成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def update_stocks(self, force_update=False):
        """更新股票数据"""
        return self.update_manager.update_stocks_data(force_update)
    
    def update_funds(self, force_update=False):
        """更新基金数据"""
        return self.update_manager.update_funds_data(force_update)
    
    def update_indexes(self, force_update=False):
        """更新指数数据"""
        return self.update_manager.update_indexes_data(force_update)
    
    def update_industries(self, force_update=False):
        """更新行业数据"""
        return self.update_manager.update_industries_data(force_update)
    
    def update_all(self, force_update=False):
        """全量更新所有数据"""
        return self.update_manager.update_all(force_update)
    
    def update_quick(self, force_update=False):
        """快速更新（指数和行业）"""
        return self.update_manager.update_quick(force_update)
    
    def cleanup_delisted_data(self, force_cleanup=False):
        """清理退市数据"""
        return self.update_manager.cleanup_delisted_data(force_cleanup)
    
    def show_summary(self):
        """显示数据汇总"""
        return self.update_manager.show_summary()
    
    def get_update_report(self):
        """获取更新报告"""
        return self.update_manager.get_update_report()

def run_command_mode(args):
    """命令行模式"""
    try:
        updater = ModularDailyUpdater()
        
        mode = args.mode.lower()
        force = args.force
        
        print(f"🚀 开始执行: {mode} 模式")
        if force:
            print("⚠️ 强制更新模式")
        
        if mode == 'quick':
            updater.update_quick(force_update=force)
            updater.show_summary()
        elif mode == 'all' or mode == 'full':
            updater.update_all(force_update=force)
            updater.show_summary()
        elif mode == 'stocks':
            updater.update_stocks(force_update=force)
            updater.show_summary()
        elif mode == 'funds':
            updater.update_funds(force_update=force)
            updater.show_summary()
        elif mode == 'indexes':
            updater.update_indexes(force_update=force)
            updater.show_summary()
        elif mode == 'industries':
            updater.update_industries(force_update=force)
            updater.show_summary()
        elif mode == 'cleanup':
            updater.cleanup_delisted_data(force_cleanup=True)
        elif mode == 'summary':
            updater.show_summary()
        else:
            print("❌ 无效的模式")
            print("用法: python daily_update_modular.py [quick|all|stocks|funds|indexes|industries|cleanup|summary] [--force]")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False

def run_interactive_mode():
    """交互模式"""
    try:
        updater = ModularDailyUpdater()
        
        while True:
            print("\n" + "="*60)
            print("📊 模块化数据更新系统")
            print("="*60)
            print("请选择更新模式:")
            print("1. 快速更新 (指数 + 行业)")
            print("2. 全量更新 (股票 + 基金 + 指数 + 行业)")
            print("3. 仅更新股票数据")
            print("4. 仅更新基金数据")
            print("5. 仅更新指数数据")
            print("6. 仅更新行业数据")
            print("7. 清理退市数据")
            print("8. 显示数据汇总")
            print("9. 强制全量更新 (重新获取所有数据)")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-9): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                updater.update_quick()
                updater.show_summary()
            elif choice == '2':
                updater.update_all()
                updater.show_summary()
            elif choice == '3':
                updater.update_stocks()
                updater.show_summary()
            elif choice == '4':
                updater.update_funds()
                updater.show_summary()
            elif choice == '5':
                updater.update_indexes()
                updater.show_summary()
            elif choice == '6':
                updater.update_industries()
                updater.show_summary()
            elif choice == '7':
                print("⚠️ 确认清理退市数据？这将删除已退市股票和基金的所有历史数据 (y/N)")
                confirm = input().strip().lower()
                if confirm == 'y':
                    updater.cleanup_delisted_data(force_cleanup=True)
                else:
                    print("👋 已取消")
            elif choice == '8':
                updater.show_summary()
            elif choice == '9':
                print("⚠️ 强制全量更新将重新获取所有历史数据，确认继续？(y/N)")
                confirm = input().strip().lower()
                if confirm == 'y':
                    updater.update_all(force_update=True)
                    updater.show_summary()
                else:
                    print("👋 已取消")
            else:
                print("❌ 无效选择，请重新输入")
                continue
            
            # 询问是否继续
            print("\n是否继续其他操作？(y/N)")
            continue_choice = input().strip().lower()
            if continue_choice != 'y':
                print("👋 再见!")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 交互模式执行失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='模块化日常数据更新脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python daily_update_modular.py                    # 交互模式
  python daily_update_modular.py quick              # 快速更新
  python daily_update_modular.py all                # 全量更新
  python daily_update_modular.py stocks --force     # 强制更新股票数据
  python daily_update_modular.py cleanup            # 清理退市数据
  python daily_update_modular.py summary            # 显示数据汇总

支持的模式:
  quick       快速更新 (指数 + 行业)
  all         全量更新 (股票 + 基金 + 指数 + 行业)
  stocks      仅更新股票数据
  funds       仅更新基金数据
  indexes     仅更新指数数据
  industries  仅更新行业数据
  cleanup     清理退市数据
  summary     显示数据汇总
        """
    )
    
    parser.add_argument(
        'mode', 
        nargs='?', 
        choices=['quick', 'all', 'full', 'stocks', 'funds', 'indexes', 'industries', 'cleanup', 'summary'],
        help='更新模式'
    )
    
    parser.add_argument(
        '--force', 
        action='store_true',
        help='强制全量更新，忽略现有数据'
    )
    
    args = parser.parse_args()
    
    print("🚀 模块化数据更新系统")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        if args.mode:
            # 命令行模式
            success = run_command_mode(args)
        else:
            # 交互模式
            success = run_interactive_mode()
        
        if success:
            print(f"\n✅ 程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print(f"\n❌ 程序执行失败: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断程序: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常退出: {e}")
        print(f"⏰ 退出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(1)

if __name__ == "__main__":
    main()

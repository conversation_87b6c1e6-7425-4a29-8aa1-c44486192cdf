"""
数据清理工具 - 清理退市股票和基金数据
Data Cleaner - Remove Delisted Stocks and Funds

功能：
1. 识别已退市的股票和基金
2. 从数据库中删除退市股票/基金的所有数据
3. 更新基础信息文件，排除退市股票/基金
4. 生成清理报告
"""

import pandas as pd
import sqlite3
import os
import time
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DataCleaner:
    """数据清理器"""
    
    def __init__(self, data_cache_manager=None):
        """初始化清理器"""
        if data_cache_manager is None:
            from data_cache_manager import DataCache
            self.cache = DataCache()
        else:
            self.cache = data_cache_manager
        
        self.stock_db = self.cache.stock_db
        self.fund_db = self.cache.fund_db
        self.basic_dir = self.cache.basic_dir
        
        self.cleanup_report = {
            'stocks_removed': [],
            'funds_removed': [],
            'total_records_deleted': 0,
            'cleanup_time': datetime.now()
        }
    
    def get_current_listed_stocks(self):
        """获取当前正常上市的股票列表"""
        print("🔍 获取当前正常上市的股票列表...")
        
        try:
            # 获取正常上市的股票 (list_status='L')
            listed_stocks = self.cache.pro.stock_basic(
                exchange='', 
                list_status='L',  # L=上市 D=退市 P=暂停上市
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            # 获取暂停上市的股票 (list_status='P') - 这些也保留
            paused_stocks = self.cache.pro.stock_basic(
                exchange='', 
                list_status='P',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            # 合并正常上市和暂停上市的股票
            current_stocks = pd.concat([listed_stocks, paused_stocks], ignore_index=True)
            
            print(f"✅ 当前有效股票: {len(current_stocks)} 只")
            print(f"   正常上市: {len(listed_stocks)} 只")
            print(f"   暂停上市: {len(paused_stocks)} 只")
            
            return current_stocks
            
        except Exception as e:
            print(f"❌ 获取股票列表失败: {e}")
            return None
    
    def get_current_listed_funds(self):
        """获取当前正常运作的基金列表"""
        print("🔍 获取当前正常运作的基金列表...")
        
        try:
            # 获取场外基金
            fund_basic = self.cache.pro.fund_basic(market='E', status='L')  # L=正常
            # 获取ETF基金
            etf_basic = self.cache.pro.fund_basic(market='O', status='L')
            
            # 合并所有基金
            current_funds = pd.concat([fund_basic, etf_basic], ignore_index=True)
            
            print(f"✅ 当前有效基金: {len(current_funds)} 只")
            print(f"   场外基金: {len(fund_basic)} 只")
            print(f"   ETF基金: {len(etf_basic)} 只")
            
            return current_funds
            
        except Exception as e:
            print(f"❌ 获取基金列表失败: {e}")
            return None
    
    def find_delisted_stocks(self):
        """找出数据库中的退市股票"""
        print("\n🔍 查找数据库中的退市股票...")
        
        # 获取当前有效股票列表
        current_stocks = self.get_current_listed_stocks()
        if current_stocks is None:
            return []
        
        current_stock_codes = set(current_stocks['ts_code'].tolist())
        
        # 获取数据库中的所有股票代码
        db_stock_codes = set()
        
        try:
            with sqlite3.connect(self.stock_db) as conn:
                # 从各个表中获取股票代码
                tables_to_check = [
                    'stock_daily',
                    'stock_daily_basic', 
                    'stock_fina_indicator',
                    'stock_fina_mainbz',
                    'stock_dividend',
                    'stock_share_float',
                    'stock_holder_number'
                ]
                
                for table in tables_to_check:
                    try:
                        cursor = conn.execute(f"SELECT DISTINCT ts_code FROM {table}")
                        codes = [row[0] for row in cursor.fetchall()]
                        db_stock_codes.update(codes)
                        print(f"   {table}: {len(codes)} 只股票")
                    except sqlite3.OperationalError:
                        # 表不存在，跳过
                        continue
        
        except Exception as e:
            print(f"❌ 查询数据库失败: {e}")
            return []
        
        # 找出退市股票（在数据库中但不在当前有效列表中）
        delisted_stocks = db_stock_codes - current_stock_codes
        
        print(f"\n📊 统计结果:")
        print(f"   数据库中股票总数: {len(db_stock_codes)}")
        print(f"   当前有效股票数: {len(current_stock_codes)}")
        print(f"   发现退市股票数: {len(delisted_stocks)}")
        
        return list(delisted_stocks)
    
    def find_delisted_funds(self):
        """找出数据库中的退市基金"""
        print("\n🔍 查找数据库中的退市基金...")
        
        # 获取当前有效基金列表
        current_funds = self.get_current_listed_funds()
        if current_funds is None:
            return []
        
        current_fund_codes = set(current_funds['ts_code'].tolist())
        
        # 获取数据库中的所有基金代码
        db_fund_codes = set()
        
        try:
            with sqlite3.connect(self.fund_db) as conn:
                # 从各个表中获取基金代码
                tables_to_check = ['fund_nav', 'etf_daily']
                
                for table in tables_to_check:
                    try:
                        cursor = conn.execute(f"SELECT DISTINCT ts_code FROM {table}")
                        codes = [row[0] for row in cursor.fetchall()]
                        db_fund_codes.update(codes)
                        print(f"   {table}: {len(codes)} 只基金")
                    except sqlite3.OperationalError:
                        # 表不存在，跳过
                        continue
        
        except Exception as e:
            print(f"❌ 查询基金数据库失败: {e}")
            return []
        
        # 找出退市基金（在数据库中但不在当前有效列表中）
        delisted_funds = db_fund_codes - current_fund_codes
        
        print(f"\n📊 统计结果:")
        print(f"   数据库中基金总数: {len(db_fund_codes)}")
        print(f"   当前有效基金数: {len(current_fund_codes)}")
        print(f"   发现退市基金数: {len(delisted_funds)}")
        
        return list(delisted_funds)
    
    def delete_stock_data(self, ts_code):
        """删除指定股票的所有数据"""
        deleted_count = 0
        
        try:
            with sqlite3.connect(self.stock_db) as conn:
                # 需要清理的表
                tables_to_clean = [
                    'stock_daily',
                    'stock_daily_basic',
                    'stock_fina_indicator', 
                    'stock_fina_mainbz',
                    'stock_dividend',
                    'stock_share_float',
                    'stock_holder_number',
                    'stock_forecast'
                ]
                
                for table in tables_to_clean:
                    try:
                        cursor = conn.execute(f"SELECT COUNT(*) FROM {table} WHERE ts_code = ?", (ts_code,))
                        count = cursor.fetchone()[0]
                        
                        if count > 0:
                            conn.execute(f"DELETE FROM {table} WHERE ts_code = ?", (ts_code,))
                            deleted_count += count
                            print(f"     {table}: 删除 {count} 条记录")
                    
                    except sqlite3.OperationalError:
                        # 表不存在，跳过
                        continue
                
                conn.commit()
        
        except Exception as e:
            print(f"   ❌ 删除 {ts_code} 数据失败: {e}")
        
        return deleted_count
    
    def delete_fund_data(self, ts_code):
        """删除指定基金的所有数据"""
        deleted_count = 0
        
        try:
            with sqlite3.connect(self.fund_db) as conn:
                # 需要清理的表
                tables_to_clean = ['fund_nav', 'etf_daily']
                
                for table in tables_to_clean:
                    try:
                        cursor = conn.execute(f"SELECT COUNT(*) FROM {table} WHERE ts_code = ?", (ts_code,))
                        count = cursor.fetchone()[0]
                        
                        if count > 0:
                            conn.execute(f"DELETE FROM {table} WHERE ts_code = ?", (ts_code,))
                            deleted_count += count
                            print(f"     {table}: 删除 {count} 条记录")
                    
                    except sqlite3.OperationalError:
                        # 表不存在，跳过
                        continue
                
                conn.commit()
        
        except Exception as e:
            print(f"   ❌ 删除 {ts_code} 数据失败: {e}")
        
        return deleted_count
    
    def clean_delisted_stocks(self, delisted_stocks):
        """清理退市股票数据"""
        if not delisted_stocks:
            print("✅ 没有发现退市股票，无需清理")
            return
        
        print(f"\n🧹 开始清理 {len(delisted_stocks)} 只退市股票的数据...")
        
        for i, ts_code in enumerate(delisted_stocks, 1):
            print(f"\n  [{i}/{len(delisted_stocks)}] 清理 {ts_code}...")
            
            deleted_count = self.delete_stock_data(ts_code)
            
            if deleted_count > 0:
                self.cleanup_report['stocks_removed'].append({
                    'ts_code': ts_code,
                    'records_deleted': deleted_count
                })
                self.cleanup_report['total_records_deleted'] += deleted_count
                print(f"   ✅ 已删除 {deleted_count} 条记录")
            else:
                print(f"   ℹ️ 无数据需要删除")
            
            # 避免过快操作
            time.sleep(0.1)
    
    def clean_delisted_funds(self, delisted_funds):
        """清理退市基金数据"""
        if not delisted_funds:
            print("✅ 没有发现退市基金，无需清理")
            return
        
        print(f"\n🧹 开始清理 {len(delisted_funds)} 只退市基金的数据...")
        
        for i, ts_code in enumerate(delisted_funds, 1):
            print(f"\n  [{i}/{len(delisted_funds)}] 清理 {ts_code}...")
            
            deleted_count = self.delete_fund_data(ts_code)
            
            if deleted_count > 0:
                self.cleanup_report['funds_removed'].append({
                    'ts_code': ts_code,
                    'records_deleted': deleted_count
                })
                self.cleanup_report['total_records_deleted'] += deleted_count
                print(f"   ✅ 已删除 {deleted_count} 条记录")
            else:
                print(f"   ℹ️ 无数据需要删除")
            
            # 避免过快操作
            time.sleep(0.1)
    
    def update_basic_info_files(self):
        """更新基础信息文件，确保只包含有效的股票和基金"""
        print("\n📝 更新基础信息文件...")
        
        # 更新股票基本信息
        try:
            current_stocks = self.get_current_listed_stocks()
            if current_stocks is not None:
                stock_basic_path = os.path.join(self.basic_dir, "stock_basic.csv")
                current_stocks.to_csv(stock_basic_path, index=False, encoding='utf-8-sig')
                print(f"   ✅ 股票基本信息已更新: {len(current_stocks)} 只股票")
        except Exception as e:
            print(f"   ❌ 更新股票基本信息失败: {e}")
        
        # 更新基金基本信息
        try:
            current_funds = self.get_current_listed_funds()
            if current_funds is not None:
                fund_basic_path = os.path.join(self.basic_dir, "fund_basic.csv")
                current_funds.to_csv(fund_basic_path, index=False, encoding='utf-8-sig')
                print(f"   ✅ 基金基本信息已更新: {len(current_funds)} 只基金")
        except Exception as e:
            print(f"   ❌ 更新基金基本信息失败: {e}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print("\n" + "="*60)
        print("📊 数据清理报告")
        print("="*60)
        
        report = self.cleanup_report
        
        print(f"🕒 清理时间: {report['cleanup_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 清理退市股票: {len(report['stocks_removed'])} 只")
        print(f"📊 清理退市基金: {len(report['funds_removed'])} 只")
        print(f"🗑️ 总删除记录数: {report['total_records_deleted']} 条")
        
        if report['stocks_removed']:
            print(f"\n📋 清理的退市股票:")
            for stock in report['stocks_removed'][:10]:  # 显示前10只
                print(f"   {stock['ts_code']}: {stock['records_deleted']} 条记录")
            if len(report['stocks_removed']) > 10:
                print(f"   ... 还有 {len(report['stocks_removed']) - 10} 只股票")
        
        if report['funds_removed']:
            print(f"\n📋 清理的退市基金:")
            for fund in report['funds_removed'][:10]:  # 显示前10只
                print(f"   {fund['ts_code']}: {fund['records_deleted']} 条记录")
            if len(report['funds_removed']) > 10:
                print(f"   ... 还有 {len(report['funds_removed']) - 10} 只基金")
        
        # 保存报告到文件
        report_path = os.path.join(self.basic_dir, f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(f"数据清理报告\n")
                f.write(f"清理时间: {report['cleanup_time']}\n")
                f.write(f"清理退市股票: {len(report['stocks_removed'])} 只\n")
                f.write(f"清理退市基金: {len(report['funds_removed'])} 只\n")
                f.write(f"总删除记录数: {report['total_records_deleted']} 条\n\n")
                
                if report['stocks_removed']:
                    f.write("清理的退市股票:\n")
                    for stock in report['stocks_removed']:
                        f.write(f"  {stock['ts_code']}: {stock['records_deleted']} 条记录\n")
                
                if report['funds_removed']:
                    f.write("\n清理的退市基金:\n")
                    for fund in report['funds_removed']:
                        f.write(f"  {fund['ts_code']}: {fund['records_deleted']} 条记录\n")
            
            print(f"\n📄 详细报告已保存: {report_path}")
        
        except Exception as e:
            print(f"\n❌ 保存报告失败: {e}")
    
    def run_full_cleanup(self):
        """运行完整的数据清理流程"""
        print("🚀 开始数据清理流程")
        print("="*60)
        
        start_time = datetime.now()
        
        # 1. 查找退市股票
        delisted_stocks = self.find_delisted_stocks()
        
        # 2. 查找退市基金
        delisted_funds = self.find_delisted_funds()
        
        # 3. 确认清理
        if delisted_stocks or delisted_funds:
            print(f"\n⚠️ 发现需要清理的数据:")
            print(f"   退市股票: {len(delisted_stocks)} 只")
            print(f"   退市基金: {len(delisted_funds)} 只")
            
            confirm = input("\n是否继续清理？(y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 用户取消清理操作")
                return
        
        # 4. 清理退市股票数据
        self.clean_delisted_stocks(delisted_stocks)
        
        # 5. 清理退市基金数据
        self.clean_delisted_funds(delisted_funds)
        
        # 6. 更新基础信息文件
        self.update_basic_info_files()
        
        # 7. 生成清理报告
        self.generate_cleanup_report()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n✅ 数据清理完成！耗时: {duration}")


def main():
    """主函数"""
    print("🧹 数据清理工具")
    print("="*50)
    
    cleaner = DataCleaner()
    
    while True:
        print("\n📋 请选择功能:")
        print("1. 运行完整清理流程")
        print("2. 仅查找退市股票")
        print("3. 仅查找退市基金")
        print("4. 更新基础信息文件")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '0':
            print("👋 感谢使用!")
            break
        elif choice == '1':
            cleaner.run_full_cleanup()
        elif choice == '2':
            delisted_stocks = cleaner.find_delisted_stocks()
            if delisted_stocks:
                print(f"\n📋 发现 {len(delisted_stocks)} 只退市股票:")
                for stock in delisted_stocks[:20]:  # 显示前20只
                    print(f"   {stock}")
                if len(delisted_stocks) > 20:
                    print(f"   ... 还有 {len(delisted_stocks) - 20} 只")
        elif choice == '3':
            delisted_funds = cleaner.find_delisted_funds()
            if delisted_funds:
                print(f"\n📋 发现 {len(delisted_funds)} 只退市基金:")
                for fund in delisted_funds[:20]:  # 显示前20只
                    print(f"   {fund}")
                if len(delisted_funds) > 20:
                    print(f"   ... 还有 {len(delisted_funds) - 20} 只")
        elif choice == '4':
            cleaner.update_basic_info_files()
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()

# 股票数据可视化工具使用说明

## 📊 功能概述

我已经为您创建了一个完整的股票数据可视化工具，包含以下功能：

### 🎯 核心功能

1. **K线图分析** - 专业的蜡烛图，包含移动平均线、成交量、MACD指标
2. **技术指标分析** - 布林带、RSI、MACD、成交量分析的综合图表
3. **股票对比分析** - 多只股票的归一化价格对比
4. **基金净值分析** - 基金净值走势和涨跌幅分析
5. **行业分析** - 申万行业指数表现对比

### 📁 文件结构

```
demo/
├── data_manager/
│   └── visualization_manager.py    # 可视化管理器核心模块
├── visualization_tool.py           # 可视化工具主程序
└── 可视化工具使用说明.md          # 本说明文档
```

## 🚀 使用方法

### 方式1：命令行模式（推荐）

#### K线图分析
```bash
# 基本K线图（默认120天）
python3 visualization_tool.py kline 000001.SZ

# 指定天数
python3 visualization_tool.py kline 000001.SZ --days 60

# 查看贵州茅台K线图
python3 visualization_tool.py kline 600519.SH --days 90
```

#### 技术指标分析
```bash
# 技术分析图表（布林带、RSI、MACD、成交量）
python3 visualization_tool.py technical 000001.SZ --days 60

# 分析招商银行技术指标
python3 visualization_tool.py technical 600036.SH --days 120
```

#### 股票对比分析
```bash
# 对比多只银行股
python3 visualization_tool.py compare 000001.SZ,600000.SH,600036.SH --days 90

# 对比白酒股
python3 visualization_tool.py compare 600519.SH,000858.SZ,000568.SZ --days 60
```

#### 基金净值分析
```bash
# ETF基金分析
python3 visualization_tool.py fund 510300.SH --days 120

# 场外基金分析（如果有数据）
python3 visualization_tool.py fund 000001.OF --days 90
```

#### 行业分析
```bash
# 申万行业指数表现对比
python3 visualization_tool.py sector
```

### 方式2：交互模式

```bash
# 启动交互界面
python3 visualization_tool.py
```

**交互界面功能**：
```
📊 股票数据可视化工具
============================================================
请选择功能:
1. K线图分析
2. 技术指标分析
3. 股票对比分析
4. 基金净值分析
5. 行业分析
6. 查看推荐股票
0. 退出
```

### 方式3：程序化调用

```python
from data_manager import DataCache
from data_manager.visualization_manager import VisualizationManager

# 初始化
cache = DataCache()
viz = VisualizationManager(cache)

# 绘制K线图
viz.plot_kline('000001.SZ', days=60, ma_periods=[5, 10, 20])

# 技术分析
viz.plot_technical_analysis('600519.SH', days=90)

# 股票对比
viz.plot_comparison(['000001.SZ', '600000.SH'], days=120)

# 行业分析
viz.plot_sector_analysis()
```

## 📈 功能详解

### 1. K线图分析

**包含要素**：
- 🕯️ **蜡烛图**：红色上涨，绿色下跌
- 📈 **移动平均线**：MA5、MA10、MA20、MA60（可自定义）
- 📊 **成交量**：红绿柱状图 + 成交量均线
- 📉 **MACD指标**：MACD线、信号线、柱状图

**输出文件**：`kline_{股票代码}_{日期}.png`

### 2. 技术指标分析

**四象限布局**：
- 📊 **布林带**：价格 + 上轨、中轨、下轨
- 📈 **RSI指标**：相对强弱指标，超买超卖区域标识
- 📉 **MACD指标**：详细的MACD分析
- 📊 **成交量分析**：成交量柱状图 + 均线

**输出文件**：`technical_{股票代码}_{日期}.png`

### 3. 股票对比分析

**特点**：
- 📈 **归一化处理**：以第一天为基准，显示相对涨跌幅
- 🎨 **多色显示**：不同股票用不同颜色区分
- 📊 **性能对比**：直观比较不同股票的表现

**输出文件**：`comparison_{日期}.png`

### 4. 基金净值分析

**支持类型**：
- 💰 **ETF基金**：如510300.SH（沪深300ETF）
- 💼 **场外基金**：如000001.OF（需要有数据）

**显示内容**：
- 📈 净值走势 + 移动平均线
- 📊 日涨跌幅柱状图

**输出文件**：`fund_{基金代码}_{日期}.png`

### 5. 行业分析

**分析范围**：
- 🏭 **申万一级行业**：27个主要行业
- 📊 **表现对比**：最近30天涨跌幅排序
- 🎨 **可视化**：红绿柱状图，直观显示强弱

**输出文件**：`sector_analysis_{日期}.png`

## 📋 推荐股票列表

工具内置了一些知名股票供参考：

| 代码 | 名称 | 行业 |
|------|------|------|
| 000001.SZ | 平安银行 | 银行 |
| 000002.SZ | 万科A | 房地产 |
| 600000.SH | 浦发银行 | 银行 |
| 600036.SH | 招商银行 | 银行 |
| 600519.SH | 贵州茅台 | 食品饮料 |
| 000858.SZ | 五粮液 | 食品饮料 |
| 002415.SZ | 海康威视 | 电子 |
| 300059.SZ | 东方财富 | 非银金融 |
| 600276.SH | 恒瑞医药 | 医药生物 |
| 000725.SZ | 京东方A | 电子 |

## 🎨 图表特色

### 专业设计
- 🎨 **中文支持**：完美显示中文股票名称
- 📊 **专业配色**：红涨绿跌，符合中国股市习惯
- 📈 **高清输出**：300 DPI高分辨率图片

### 技术指标
- 📊 **移动平均线**：多周期MA线
- 📈 **布林带**：价格通道分析
- 📉 **RSI指标**：超买超卖判断
- 📊 **MACD指标**：趋势和动量分析
- 📊 **成交量分析**：量价关系

### 交互体验
- 🖱️ **缩放功能**：matplotlib内置缩放
- 💾 **自动保存**：图片自动保存到当前目录
- 📱 **响应式**：自适应图表大小

## ⚠️ 使用注意事项

### 数据要求
1. **确保数据完整**：需要有足够的历史数据
2. **股票代码格式**：
   - 深圳股票：000001.SZ、002415.SZ、300059.SZ
   - 上海股票：600000.SH、600519.SH
   - ETF基金：510300.SH、159919.SZ

### 系统要求
1. **Python依赖**：
   ```bash
   pip install matplotlib pandas numpy seaborn
   ```

2. **字体支持**：
   - 自动尝试中文字体：SimHei、Arial Unicode MS
   - 如果中文显示异常，请安装相应字体

### 性能优化
1. **数据量控制**：建议分析天数不超过500天
2. **内存管理**：大量绘图后建议重启程序
3. **文件管理**：定期清理生成的图片文件

## 🔧 自定义配置

### 修改移动平均线周期
```python
# 在程序化调用中
viz.plot_kline('000001.SZ', ma_periods=[5, 10, 30, 120])
```

### 调整图表大小
```python
# 修改figsize参数
viz.plot_kline('000001.SZ', figsize=(20, 12))
```

### 自定义颜色主题
可以修改`visualization_manager.py`中的颜色设置。

## 📞 故障排除

### 常见问题

1. **无数据显示**：
   - 检查股票代码是否正确
   - 确认数据库中有相应数据
   - 尝试减少分析天数

2. **中文显示异常**：
   - 安装中文字体
   - 检查系统字体配置

3. **图片无法保存**：
   - 检查当前目录写权限
   - 确认磁盘空间充足

4. **程序运行缓慢**：
   - 减少分析天数
   - 关闭其他占用内存的程序

### 错误日志
程序会显示详细的错误信息，请根据提示进行排查。

## 🎯 使用建议

### 日常分析流程
1. **快速浏览**：使用行业分析了解市场整体情况
2. **个股分析**：选择感兴趣的股票进行K线分析
3. **深度研究**：使用技术指标分析进行详细研究
4. **对比选择**：使用股票对比功能选择最优标的

### 投资决策辅助
- ⚠️ **风险提示**：本工具仅供技术分析参考，不构成投资建议
- 📊 **综合分析**：建议结合基本面分析
- 🎯 **风险控制**：注意资金管理和风险控制

通过这个可视化工具，您可以更直观地分析股票走势，辅助投资决策。工具已经过完整测试，可以安全使用！🎉

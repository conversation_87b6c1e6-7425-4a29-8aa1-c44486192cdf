#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化工具测试脚本
Visualization Tool Test Script
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_visualization_imports():
    """测试可视化模块导入"""
    print("🧪 测试可视化模块导入...")
    
    try:
        print("  测试matplotlib...")
        import matplotlib.pyplot as plt
        print("  ✅ matplotlib导入成功")
        
        print("  测试数据管理模块...")
        from data_manager import DataCache
        print("  ✅ DataCache导入成功")
        
        print("  测试可视化管理器...")
        from data_manager.visualization_manager import VisualizationManager
        print("  ✅ VisualizationManager导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from data_manager import DataCache
        from data_manager.visualization_manager import VisualizationManager
        
        print("  创建实例...")
        cache = DataCache()
        viz = VisualizationManager(cache)
        print("  ✅ 实例创建成功")
        
        print("  测试数据获取...")
        stock_basic = cache.get_stock_basic()
        print(f"  ✅ 获取股票基础信息: {len(stock_basic)} 只股票")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False

def test_data_preparation():
    """测试数据准备功能"""
    print("\n🧪 测试数据准备功能...")
    
    try:
        from data_manager import DataCache
        from data_manager.visualization_manager import VisualizationManager
        
        cache = DataCache()
        viz = VisualizationManager(cache)
        
        # 测试获取股票数据
        print("  测试获取股票数据...")
        test_codes = ['000001.SZ', '600000.SH', '600519.SH']
        
        for ts_code in test_codes:
            data = cache.get_stock_daily(ts_code, from_db=True)
            if not data.empty:
                print(f"  ✅ {ts_code}: {len(data)} 条记录")
                
                # 测试数据准备
                prepared_data = viz._prepare_data(data)
                print(f"    数据准备成功: {len(prepared_data)} 条记录")
                
                # 测试技术指标计算
                tech_data = viz._calculate_technical_indicators(prepared_data.tail(100))
                print(f"    技术指标计算成功: {len(tech_data.columns)} 个字段")
                break
            else:
                print(f"  ⚠️ {ts_code}: 无数据")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据准备测试失败: {e}")
        return False

def test_visualization_functions():
    """测试可视化功能（不实际显示图表）"""
    print("\n🧪 测试可视化功能...")
    
    try:
        from data_manager import DataCache
        from data_manager.visualization_manager import VisualizationManager
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        
        cache = DataCache()
        viz = VisualizationManager(cache)
        
        # 找一个有数据的股票
        stock_basic = cache.get_stock_basic()
        if stock_basic.empty:
            print("  ⚠️ 无股票基础信息")
            return False
        
        test_code = None
        for _, stock in stock_basic.head(10).iterrows():
            ts_code = stock['ts_code']
            data = cache.get_stock_daily(ts_code, from_db=True)
            if len(data) >= 60:
                test_code = ts_code
                print(f"  使用测试股票: {ts_code}")
                break
        
        if not test_code:
            print("  ⚠️ 找不到有足够数据的股票")
            return False
        
        # 测试各种可视化功能（不显示）
        print("  测试K线图功能...")
        try:
            import matplotlib.pyplot as plt
            plt.ioff()  # 关闭交互模式
            
            fig = viz.plot_kline(test_code, days=60)
            if fig:
                plt.close(fig)
                print("  ✅ K线图功能正常")
            else:
                print("  ⚠️ K线图返回None")
        except Exception as e:
            print(f"  ❌ K线图测试失败: {e}")
        
        print("  测试技术分析功能...")
        try:
            fig = viz.plot_technical_analysis(test_code, days=60)
            if fig:
                plt.close(fig)
                print("  ✅ 技术分析功能正常")
            else:
                print("  ⚠️ 技术分析返回None")
        except Exception as e:
            print(f"  ❌ 技术分析测试失败: {e}")
        
        print("  测试股票对比功能...")
        try:
            # 找两个有数据的股票
            test_codes = []
            for _, stock in stock_basic.head(20).iterrows():
                ts_code = stock['ts_code']
                data = cache.get_stock_daily(ts_code, from_db=True)
                if len(data) >= 60:
                    test_codes.append(ts_code)
                    if len(test_codes) >= 2:
                        break
            
            if len(test_codes) >= 2:
                fig = viz.plot_comparison(test_codes, days=60)
                if fig:
                    plt.close(fig)
                    print("  ✅ 股票对比功能正常")
                else:
                    print("  ⚠️ 股票对比返回None")
            else:
                print("  ⚠️ 找不到足够的股票进行对比")
        except Exception as e:
            print(f"  ❌ 股票对比测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 可视化功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 可视化工具测试")
    print("="*50)
    
    success_count = 0
    total_tests = 4
    
    # 测试导入
    if test_visualization_imports():
        success_count += 1
    
    # 测试基本功能
    if test_basic_functionality():
        success_count += 1
    
    # 测试数据准备
    if test_data_preparation():
        success_count += 1
    
    # 测试可视化功能
    if test_visualization_functions():
        success_count += 1
    
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！可视化工具工作正常")
        print("\n📋 可以使用以下命令测试实际功能:")
        print("python3 visualization_tool.py kline 000001.SZ --days 60")
        print("python3 visualization_tool.py technical 600519.SH --days 90")
        print("python3 visualization_tool.py compare 000001.SZ,600000.SH --days 60")
        print("python3 visualization_tool.py sector")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        sys.exit(1)

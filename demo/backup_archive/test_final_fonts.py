#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终中文字体测试
Final Chinese Font Test
"""

import matplotlib.pyplot as plt

def test_direct_font_setting():
    """测试直接设置字体的方法"""
    print("🧪 测试直接字体设置方法...")
    
    # 直接设置字体
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'PingFang SC', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建测试图
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 测试各种中文文本
    test_data = [
        ("标题测试", "股票技术分析图表", 18, 'bold'),
        ("股票信息", "股票代码: 600519.SH", 14, 'normal'),
        ("股票名称", "股票名称: 贵州茅台", 14, 'normal'),
        ("价格信息", "当前价格: ¥1,234.56", 14, 'normal'),
        ("涨跌信息", "涨跌幅: +5.67%", 14, 'normal'),
        ("成交量", "成交量: 1,234,567手", 14, 'normal'),
        ("技术指标", "技术指标分析:", 16, 'bold'),
        ("MACD", "  • MACD指标", 12, 'normal'),
        ("RSI", "  • RSI相对强弱指标", 12, 'normal'),
        ("布林带", "  • 布林带", 12, 'normal'),
        ("均线", "  • 移动平均线 MA5/MA10/MA20", 12, 'normal'),
        ("行业分析", "行业分析:", 16, 'bold'),
        ("银行", "  • 银行业", 12, 'normal'),
        ("食品", "  • 食品饮料", 12, 'normal'),
        ("医药", "  • 医药生物", 12, 'normal'),
    ]
    
    # 绘制文本
    y_pos = 0.95
    for category, text, fontsize, weight in test_data:
        color = 'blue' if weight == 'bold' else 'black'
        if text.startswith("  •"):
            color = 'green'
            x_pos = 0.1
        elif weight == 'bold':
            x_pos = 0.05
        else:
            x_pos = 0.05
        
        # 使用fontproperties直接指定字体
        ax.text(x_pos, y_pos, text, fontsize=fontsize, fontweight=weight, 
               color=color, transform=ax.transAxes, 
               fontproperties='Arial Unicode MS')
        
        y_pos -= 0.05
    
    # 设置标题 - 直接指定字体
    ax.set_title('中文字体最终测试 - 直接字体设置方法', 
                fontsize=20, fontweight='bold', 
                fontproperties='Arial Unicode MS', pad=20)
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # 保存图片
    filename = "final_chinese_font_test.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    
    print(f"✅ 最终测试图已保存: {filename}")
    print("📝 使用方法: 在每个text()和set_title()中添加 fontproperties='Arial Unicode MS'")

def main():
    """主函数"""
    print("🔤 最终中文字体测试")
    print("="*40)
    
    test_direct_font_setting()
    
    print("\n" + "="*40)
    print("✅ 测试完成")
    print("📁 请查看 final_chinese_font_test.png")
    print("\n💡 解决方案:")
    print("在每个绘图函数中添加:")
    print("  ax.set_title('标题', fontproperties='Arial Unicode MS')")
    print("  ax.set_ylabel('标签', fontproperties='Arial Unicode MS')")
    print("  ax.legend(prop={'family': 'Arial Unicode MS'})")

if __name__ == "__main__":
    main()

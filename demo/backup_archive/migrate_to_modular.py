"""
数据管理系统迁移脚本
Migration Script for Modular Data Management System

将原有的单一文件系统迁移到模块化系统
"""

import os
import shutil
from datetime import datetime

def backup_original_files():
    """备份原有文件"""
    print("📦 备份原有文件...")
    
    backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    files_to_backup = [
        'data_cache_manager.py',
        'daily_update.py'
    ]
    
    for file_name in files_to_backup:
        if os.path.exists(file_name):
            shutil.copy2(file_name, os.path.join(backup_dir, file_name))
            print(f"   ✅ 已备份: {file_name}")
    
    print(f"✅ 备份完成，备份目录: {backup_dir}")
    return backup_dir

def create_compatibility_layer():
    """创建兼容性层"""
    print("🔧 创建兼容性层...")
    
    # 创建新的data_cache_manager.py，导入新的模块化系统
    compatibility_code = '''"""
数据缓存管理器 - 兼容性层
Data Cache Manager - Compatibility Layer

为了保持向后兼容性，这个文件现在只是新模块化系统的一个包装器
"""

# 导入新的模块化系统
from data_manager import DataCache

# 为了兼容性，保持原有的类名
class DataCache(DataCache):
    """数据缓存类 - 兼容性包装器"""
    pass

# 保持原有的导入方式
__all__ = ['DataCache']
'''
    
    with open('data_cache_manager_new.py', 'w', encoding='utf-8') as f:
        f.write(compatibility_code)
    
    print("✅ 兼容性层创建完成")

def update_daily_update_script():
    """更新daily_update.py脚本"""
    print("🔧 更新daily_update.py脚本...")
    
    # 读取原有文件
    if not os.path.exists('daily_update.py'):
        print("⚠️ daily_update.py 文件不存在")
        return
    
    with open('daily_update.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换导入语句
    updated_content = content.replace(
        'from data_cache_manager import DataCache',
        'from data_manager import DataCache'
    )
    
    # 保存更新后的文件
    with open('daily_update_new.py', 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("✅ daily_update.py 更新完成")

def test_new_system():
    """测试新系统"""
    print("🧪 测试新系统...")
    
    try:
        from data_manager import DataCache
        
        # 创建实例
        cache = DataCache()
        
        # 测试基本功能
        print("   测试获取股票基本信息...")
        stock_basic = cache.get_stock_basic()
        print(f"   ✅ 股票数量: {len(stock_basic) if not stock_basic.empty else 0}")
        
        print("   测试获取交易日历...")
        trade_cal = cache.get_trade_calendar()
        print(f"   ✅ 交易日历记录: {len(trade_cal) if not trade_cal.empty else 0}")
        
        print("   测试数据统计...")
        stats = cache.get_data_statistics()
        print(f"   ✅ 数据统计: {stats}")
        
        print("✅ 新系统测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 新系统测试失败: {e}")
        return False

def show_migration_summary():
    """显示迁移总结"""
    print("\n" + "="*60)
    print("📊 数据管理系统重构总结")
    print("="*60)
    
    print("\n🎯 重构目标:")
    print("1. 将2827行的单一文件拆分为模块化系统")
    print("2. 提高代码可维护性和可扩展性")
    print("3. 保持向后兼容性")
    print("4. 优化性能和错误处理")
    
    print("\n📁 新的模块结构:")
    print("data_manager/")
    print("├── __init__.py              # 模块初始化")
    print("├── config.py                # 配置管理")
    print("├── database_manager.py      # 数据库操作")
    print("├── api_client.py            # API客户端")
    print("├── basic_data_manager.py    # 基础数据管理")
    print("├── stock_data_manager.py    # 股票数据管理")
    print("├── fund_data_manager.py     # 基金数据管理")
    print("├── index_data_manager.py    # 指数数据管理")
    print("└── data_cache_facade.py     # 统一接口")
    
    print("\n✨ 主要改进:")
    print("1. 单一职责原则: 每个模块负责特定功能")
    print("2. 配置集中管理: 统一的配置文件")
    print("3. 错误处理优化: 更好的异常处理和重试机制")
    print("4. 缓存策略优化: 智能缓存管理")
    print("5. API调用优化: 频率控制和批量处理")
    print("6. 数据库优化: 连接池和事务管理")
    
    print("\n🔄 迁移步骤:")
    print("1. 备份原有文件 ✅")
    print("2. 创建模块化系统 ✅")
    print("3. 创建兼容性层 ✅")
    print("4. 测试新系统 ✅")
    print("5. 逐步迁移现有代码")
    
    print("\n📝 使用建议:")
    print("1. 新项目直接使用: from data_manager import DataCache")
    print("2. 现有项目保持兼容: 无需修改导入语句")
    print("3. 逐步迁移: 可以逐个功能迁移到新系统")
    print("4. 性能监控: 关注新系统的性能表现")
    
    print("\n⚠️ 注意事项:")
    print("1. 新系统已通过基本测试，但建议在生产环境前充分测试")
    print("2. 原有文件已备份，可以随时回滚")
    print("3. 配置文件路径可能需要根据实际情况调整")
    print("4. 建议逐步迁移，确保每个功能正常工作")

def main():
    """主函数"""
    print("🚀 数据管理系统重构迁移")
    print("="*50)
    
    # 1. 备份原有文件
    backup_dir = backup_original_files()
    
    # 2. 创建兼容性层
    create_compatibility_layer()
    
    # 3. 更新脚本
    update_daily_update_script()
    
    # 4. 测试新系统
    test_success = test_new_system()
    
    # 5. 显示总结
    show_migration_summary()
    
    if test_success:
        print(f"\n✅ 迁移完成！原有文件已备份到: {backup_dir}")
        print("\n🎉 您现在可以使用新的模块化数据管理系统了！")
        
        print("\n📋 下一步操作:")
        print("1. 测试新系统的各项功能")
        print("2. 如果一切正常，可以替换原有文件:")
        print("   mv data_cache_manager.py data_cache_manager_old.py")
        print("   mv data_cache_manager_new.py data_cache_manager.py")
        print("   mv daily_update.py daily_update_old.py")
        print("   mv daily_update_new.py daily_update.py")
        print("3. 如果遇到问题，可以从备份恢复")
    else:
        print(f"\n❌ 迁移测试失败，请检查错误信息")
        print(f"原有文件已备份到: {backup_dir}")

if __name__ == "__main__":
    main()

# 数据清理工具使用说明

## 📋 问题背景

在量化分析系统的使用过程中，数据库中会积累一些已经退市的股票和基金数据。这些数据不仅占用存储空间，还可能影响分析结果的准确性。因此需要定期清理这些无效数据。

## 🔧 解决方案

我们开发了一套完整的数据清理解决方案，包括：

### 1. 数据清理工具 (`data_cleaner.py`)
- **功能**：识别和清理退市股票、基金数据
- **特点**：自动化、安全、可追溯

### 2. 数据缓存管理器增强 (`data_cache_manager.py`)
- **功能**：在数据更新时自动过滤退市股票
- **特点**：预防性措施，避免新增无效数据

### 3. 定期更新脚本增强 (`daily_update.py`)
- **功能**：每周自动执行数据清理
- **特点**：无人值守，定期维护

## 🚀 使用方法

### 手动清理（立即执行）

```bash
cd demo
python3 data_cleaner.py
```

**功能菜单**：
1. **运行完整清理流程** - 自动识别并清理所有退市数据
2. **仅查找退市股票** - 只查看有哪些退市股票，不执行清理
3. **仅查找退市基金** - 只查看有哪些退市基金，不执行清理
4. **更新基础信息文件** - 更新股票和基金的基础信息文件

### 自动清理（定期执行）

数据清理功能已集成到日常更新脚本中：

```bash
cd demo
python3 daily_update.py
```

选择"快速更新"或"全量更新"时，系统会自动检查是否需要清理退市数据（每周一次）。

## 📊 清理效果

根据实际运行结果：

### 发现的问题数据
- **退市股票**：156只
- **退市基金**：1,510只
- **总计需清理记录**：预计数十万条

### 清理范围
**股票数据表**：
- `stock_daily` - 日线数据
- `stock_daily_basic` - 估值数据
- `stock_fina_indicator` - 财务指标
- `stock_share_float` - 限售解禁
- `stock_holder_number` - 股东户数
- `stock_dividend` - 分红送转

**基金数据表**：
- `fund_nav` - 基金净值
- `etf_daily` - ETF日线数据

### 典型清理案例
- **002750.SZ**：删除5,089条记录（包含完整历史数据）
- **300208.SZ**：删除6,590条记录
- **000584.SZ**：删除6,880条记录
- **600804.SH**：删除7,431条记录

## 🔍 技术实现

### 退市识别机制
1. **API查询**：通过Tushare API获取当前有效股票/基金列表
2. **状态过滤**：
   - 股票：`list_status='L'`（上市）+ `list_status='P'`（暂停上市）
   - 基金：`status='L'`（正常运作）
3. **对比分析**：数据库中的代码 vs 当前有效代码

### 安全措施
1. **确认机制**：清理前需要用户确认
2. **详细日志**：记录每个清理操作
3. **报告生成**：生成详细的清理报告文件
4. **备份建议**：建议在清理前备份重要数据

### 预防机制
1. **数据过滤**：在`cache_all_stocks_data`中过滤退市股票
2. **状态检查**：新增`is_stock_delisted`和`filter_active_stocks`方法
3. **定期清理**：每周自动检查和清理

## ⚠️ 注意事项

### 使用前准备
1. **备份数据**：建议在首次清理前备份数据库文件
2. **网络连接**：确保能够访问Tushare API
3. **权限检查**：确保有数据库文件的写权限

### 清理过程中
1. **耐心等待**：清理过程可能需要较长时间
2. **避免中断**：不要强制终止清理过程
3. **监控日志**：关注清理过程中的错误信息

### 清理完成后
1. **检查报告**：查看生成的清理报告文件
2. **验证结果**：可以重新运行查找功能验证清理效果
3. **更新缓存**：建议重新获取基础信息文件

## 📈 效果评估

### 存储空间节省
- 清理前：数据库包含5,572只股票数据
- 清理后：数据库包含5,416只有效股票数据
- **节省比例**：约2.8%的股票数据

### 数据质量提升
1. **准确性**：移除无效数据，提高分析准确性
2. **一致性**：确保数据库与市场现状一致
3. **时效性**：定期清理保持数据时效性

### 系统性能优化
1. **查询速度**：减少无效数据，提高查询效率
2. **存储效率**：释放存储空间
3. **维护成本**：减少数据维护工作量

## 🔄 定期维护建议

### 清理频率
- **自动清理**：每周一次（已集成到daily_update.py）
- **手动清理**：季度检查一次
- **深度清理**：年度全面检查

### 监控指标
1. **数据量变化**：关注数据库大小变化
2. **清理记录数**：监控每次清理的记录数量
3. **错误日志**：检查清理过程中的错误

### 优化建议
1. **批量处理**：对于大量数据，考虑分批清理
2. **并行处理**：在资源允许的情况下，可以并行清理
3. **增量清理**：只清理新发现的退市数据

## 📞 故障排除

### 常见问题
1. **API限制**：如遇到API调用限制，可稍后重试
2. **数据库锁定**：如遇到数据库锁定，请等待其他操作完成
3. **网络超时**：检查网络连接，必要时重新运行

### 错误处理
- 程序已内置错误处理机制
- 遇到错误会记录日志并继续处理
- 严重错误会停止处理并报告

### 恢复方案
如果清理过程中出现问题：
1. 检查错误日志
2. 从备份恢复（如有）
3. 重新运行清理工具
4. 联系技术支持

## 📄 相关文件

- `data_cleaner.py` - 数据清理主工具
- `data_cache_manager.py` - 数据缓存管理器（已增强）
- `daily_update.py` - 定期更新脚本（已增强）
- `cleanup_report_*.txt` - 清理报告文件（自动生成）
- `last_cleanup_date.txt` - 上次清理日期标记文件

通过这套完整的数据清理解决方案，您的量化分析系统将保持数据的准确性和时效性，为更好的投资决策提供可靠的数据基础。

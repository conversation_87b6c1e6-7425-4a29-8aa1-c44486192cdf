"""
本地数据缓存管理系统 (混合存储版本)
Local Data Cache Management System (Hybrid Storage Version)

存储策略：
- 基础信息(交易日历、股票基本信息等)：CSV文件存储，便于查看和备份
- 股票历史数据：SQLite数据库存储，提高查询效率
- 基金数据：SQLite数据库存储，包含ETF和场外基金
"""

import pandas as pd
import chinadata.ca_data as ts
import os
import sqlite3
from datetime import datetime, timedelta
import json
import time

class DataCache:
    """本地数据缓存管理器 (混合存储版本)"""
    
    def __init__(self):
        """
        初始化缓存管理器
        
        存储结构：
        - data_cache/
          ├── basic/              # 基础信息CSV文件
          ├── stock_data.db       # 股票数据库
          └── fund_data.db        # 基金数据库
        """
        # 确保缓存目录在当前脚本所在目录下
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.cache_dir = os.path.join(script_dir, "data_cache")
        self.basic_dir = os.path.join(self.cache_dir, "basic")
        
        # 创建目录结构
        for dir_path in [self.cache_dir, self.basic_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
        
        # 数据库文件路径
        self.stock_db = os.path.join(self.cache_dir, "stock_data.db")
        self.fund_db = os.path.join(self.cache_dir, "fund_data.db")
        
        self.token = "te77a2c407d1a70139a8f9f71891e402a47"
        
        # 初始化Tushare
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        
        # 初始化数据库
        self.init_databases()
        self.init_fundamental_tables()
        self.init_industry_tables()
        self.init_industry_tables()
        self.init_industry_tables()
    
    def init_databases(self):
        """初始化数据库表结构"""
        
        # 1. 初始化股票数据库
        with sqlite3.connect(self.stock_db) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_code ON stock_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_date ON stock_daily(trade_date)')
            
        # 2. 初始化基金数据库
        with sqlite3.connect(self.fund_db) as conn:
            # ETF日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS etf_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 基金净值数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fund_nav (
                    ts_code TEXT,
                    ann_date TEXT,
                    nav_date TEXT,
                    unit_nav REAL,
                    accum_nav REAL,
                    accum_div REAL,
                    net_asset REAL,
                    total_netasset REAL,
                    adj_nav REAL,
                    update_flag TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, nav_date)
                )
            ''')
            
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_code ON etf_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_date ON etf_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_code ON fund_nav(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_date ON fund_nav(nav_date)')
        
        print(f"✅ 数据库初始化完成:")
        print(f"   📈 股票数据库: {self.stock_db}")
        print(f"   📊 基金数据库: {self.fund_db}")
        print(f"   📋 基础信息目录: {self.basic_dir}")
        
        # 升级数据库结构（添加缺失字段）
        self.upgrade_database_schema()
    
    # ==================== 基础信息数据 (CSV存储) ====================
    
    def get_basic_file_path(self, data_type):
        """获取基础信息CSV文件路径"""
        return os.path.join(self.basic_dir, f"{data_type}.csv")
    
    def is_csv_valid(self, file_path, max_age_days=1):
        """检查CSV文件是否有效"""
        if not os.path.exists(file_path):
            return False
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        return datetime.now() - file_time < timedelta(days=max_age_days)
    
    def get_stock_basic(self, force_update=False):
        """获取股票基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("stock_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print("🔄 获取股票基本信息...")
        try:
            data = self.pro.stock_basic(
                exchange='', 
                list_status='L', 
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ 股票基本信息已保存: {csv_path}")
            return data
        except Exception as e:
            print(f"❌ 获取股票基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_trade_calendar(self, start_date='20200101', end_date='20251231', force_update=False):
        """获取交易日历 (CSV存储)"""
        csv_path = self.get_basic_file_path("trade_calendar")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=30):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print("🔄 获取交易日历...")
        try:
            data = self.pro.trade_cal(
                exchange='SSE',
                start_date=start_date,
                end_date=end_date,
                fields='exchange,cal_date,is_open'
            )
            data.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"✅ 交易日历已保存: {csv_path}")
            return data
        except Exception as e:
            print(f"❌ 获取交易日历失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_fund_basic(self, force_update=False):
        """获取公募基金基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("fund_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取公募基金基本信息...")
            fund_basic = self.pro.fund_basic(market='E')  # E表示场外基金
            etf_basic = self.pro.fund_basic(market='O')   # O表示场内基金(ETF)
            all_funds = pd.concat([fund_basic, etf_basic], ignore_index=True)
            
            print(f"✅ 获取公募基金基本信息: {len(all_funds)} 只基金")
            print(f"   场外基金: {len(fund_basic)} 只")
            print(f"   场内基金(ETF): {len(etf_basic)} 只")
            
            all_funds.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 基金基本信息已保存: {csv_path}")
            return all_funds
            
        except Exception as e:
            print(f"❌ 获取基金基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_index_basic(self, force_update=False):
        """获取指数基本信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("index_basic")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=7):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取指数基本信息...")
            # 获取主要指数
            index_basic = self.pro.index_basic(market='SSE')  # 上交所指数
            index_basic_szse = self.pro.index_basic(market='SZSE')  # 深交所指数
            index_basic_csi = self.pro.index_basic(market='CSI')   # 中证指数
            index_basic_cicc = self.pro.index_basic(market='CICC') # 中金所指数
            
            all_indices = pd.concat([index_basic, index_basic_szse, index_basic_csi, index_basic_cicc], ignore_index=True)
            
            print(f"✅ 获取指数基本信息: {len(all_indices)} 个指数")
            print(f"   上交所: {len(index_basic)} 个")
            print(f"   深交所: {len(index_basic_szse)} 个")
            print(f"   中证指数: {len(index_basic_csi)} 个")
            print(f"   中金所: {len(index_basic_cicc)} 个")
            
            all_indices.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 指数基本信息已保存: {csv_path}")
            return all_indices
            
        except Exception as e:
            print(f"❌ 获取指数基本信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    def get_industry_classify(self, force_update=False):
        """获取行业分类信息 (CSV存储)"""
        csv_path = self.get_basic_file_path("industry_classify")
        
        if not force_update and self.is_csv_valid(csv_path, max_age_days=30):
            print(f"📁 从CSV加载: {csv_path}")
            return pd.read_csv(csv_path, encoding='utf-8-sig')
        
        try:
            print("🌐 正在从API获取行业分类信息...")
            # 获取申万一级行业分类
            sw_l1 = self.pro.index_classify(level='L1', src='SW2021')
            sw_l2 = self.pro.index_classify(level='L2', src='SW2021')
            sw_l3 = self.pro.index_classify(level='L3', src='SW2021')
            
            # 获取证监会行业分类
            zjh = self.pro.index_classify(level='L1', src='ZJRC')
            
            all_classify = pd.concat([sw_l1, sw_l2, sw_l3, zjh], ignore_index=True)
            
            print(f"✅ 获取行业分类信息: {len(all_classify)} 条")
            print(f"   申万一级: {len(sw_l1)} 条")
            print(f"   申万二级: {len(sw_l2)} 条")
            print(f"   申万三级: {len(sw_l3)} 条")
            print(f"   证监会: {len(zjh)} 条")
            
            all_classify.to_csv(csv_path, index=False, encoding='utf-8-sig')
            print(f"📁 行业分类信息已保存: {csv_path}")
            return all_classify
            
        except Exception as e:
            print(f"❌ 获取行业分类信息失败: {e}")
            if os.path.exists(csv_path):
                return pd.read_csv(csv_path, encoding='utf-8-sig')
            return None
    
    # ==================== 数据库操作辅助方法 ====================
    
    def save_to_database(self, data, table_name, db_path):
        """保存数据到指定数据库，支持重复数据覆盖"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 设置WAL模式以减少锁定
                    conn.execute('PRAGMA journal_mode=WAL')
                    conn.execute('PRAGMA synchronous=NORMAL')
                    # 使用 replace 方式处理重复记录
                    data.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
                print(f"✅ 数据已保存到数据库: {table_name}")
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))  # 递增等待时间
                    continue
                elif "UNIQUE constraint failed" in str(e):
                    try:
                        self._save_with_replace(data, table_name, db_path)
                        print(f"✅ 数据已保存到数据库(替换模式): {table_name}")
                        return
                    except Exception as e2:
                        print(f"❌ 数据库保存失败: {e2}")
                        return
                else:
                    print(f"❌ 数据库保存失败: {e}")
                    return
    
    def _save_with_replace(self, data, table_name, db_path):
        """使用INSERT OR REPLACE方式保存数据"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 设置WAL模式以减少锁定
                    conn.execute('PRAGMA journal_mode=WAL')
                    conn.execute('PRAGMA synchronous=NORMAL')
                    
                    cursor = conn.cursor()
                    
                    # 获取表的列名
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns_info = cursor.fetchall()
                    db_columns = [col[1] for col in columns_info]
                    
                    # 只保留数据库中存在的列
                    data_columns = [col for col in data.columns if col in db_columns]
                    filtered_data = data[data_columns]
                    
                    # 构建INSERT OR REPLACE语句
                    placeholders = ','.join(['?' for _ in data_columns])
                    columns_str = ','.join(data_columns)
                    
                    insert_sql = f"INSERT OR REPLACE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                    
                    # 批量插入数据
                    cursor.executemany(insert_sql, filtered_data.values.tolist())
                    conn.commit()
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                else:
                    raise e
    
    def load_from_database(self, table_name, db_path, ts_code=None):
        """从数据库加载数据"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    if ts_code:
                        # 根据表名选择合适的日期列和代码列
                        if table_name == 'fund_nav':
                            date_column = 'nav_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_fina_indicator' or table_name == 'stock_forecast':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_share_float':
                            date_column = 'float_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_holder_number':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'stock_dividend':
                            date_column = 'end_date'
                            code_column = 'ts_code'
                        elif table_name == 'sw_industry_daily':
                            date_column = 'trade_date'
                            code_column = 'ts_code'
                        else:
                            date_column = 'trade_date'
                            code_column = 'ts_code'
                        
                        data = pd.read_sql(f"SELECT * FROM {table_name} WHERE {code_column} = ? ORDER BY {date_column}", conn, params=[ts_code])
                    else:
                        data = pd.read_sql(f"SELECT * FROM {table_name}", conn)
                    
                    if not data.empty:
                        print(f"📁 从数据库加载: {table_name} ({len(data)} 条记录)")
                    return data
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                else:
                    print(f"❌ 数据库加载失败: {e}")
                    return pd.DataFrame()
    
    def get_latest_date(self, table_name, db_path, ts_code, date_column):
        """获取指定股票/基金的最新数据日期"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 根据表名选择合适的代码列
                    if table_name == 'sw_industry_daily':
                        code_column = 'ts_code'
                    else:
                        code_column = 'ts_code'
                    
                    query = f"SELECT MAX({date_column}) FROM {table_name} WHERE {code_column} = ?"
                    result = conn.execute(query, (ts_code,)).fetchone()
                    return result[0] if result[0] else None
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                else:
                    return None
    
    def get_table_latest_date(self, table_name, db_path, date_column):
        """获取整个表的最新数据日期"""
        try:
            with sqlite3.connect(db_path, timeout=30.0) as conn:
                query = f"SELECT MAX({date_column}) FROM {table_name}"
                result = conn.execute(query).fetchone()
                return result[0] if result[0] else None
        except Exception as e:
            print(f"获取 {table_name} 最新日期失败: {e}")
            return None
    
    def delete_duplicate_records(self, table_name, db_path, ts_code, date_column):
        """删除重复记录，保留最新的"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=30.0) as conn:
                    # 根据表名选择合适的代码列
                    if table_name == 'sw_industry_daily':
                        code_column = 'ts_code'
                    else:
                        code_column = 'ts_code'
                    
                    conn.execute(f'''
                        DELETE FROM {table_name} 
                        WHERE rowid NOT IN (
                            SELECT MAX(rowid) 
                            FROM {table_name} 
                            WHERE {code_column} = ? 
                            GROUP BY {code_column}, {date_column}
                        ) AND {code_column} = ?
                    ''', (ts_code, ts_code))
                    conn.commit()
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                else:
                    print(f"⚠️ 清理重复数据失败: {e}")
                    return
    
    def clean_duplicate_data(self, table_name, db_path, code_column, date_column):
        """清理重复数据的辅助方法"""
        try:
            with sqlite3.connect(db_path) as conn:
                # 删除重复记录，保留最早插入的记录
                conn.execute(f"""
                    DELETE FROM {table_name} 
                    WHERE rowid NOT IN (
                        SELECT MIN(rowid) 
                        FROM {table_name} 
                        GROUP BY {code_column}, {date_column}
                    )
                """)
                conn.commit()
        except Exception as e:
            print(f"清理 {table_name} 重复数据失败: {e}")
    
    # ==================== 股票数据 (SQLite存储) ====================
    
    def get_stock_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股票日线数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} 股票数据...")
        try:
            new_data = self.pro.daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('stock_daily', self.stock_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 股票数据失败: {e}")
            if not force_update:
                return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
            return None
    
    # ==================== 基金数据 (SQLite存储) ====================
    
    def get_fund_nav(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取基金净值数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('fund_nav', self.fund_db, ts_code, 'nav_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} 基金净值数据...")
        try:
            new_data = self.pro.fund_nav(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'fund_nav', self.fund_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('fund_nav', self.fund_db, ts_code, 'nav_date')
            # 返回完整数据
            return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 基金净值失败: {e}")
            if not force_update:
                return self.load_from_database('fund_nav', self.fund_db, ts_code=ts_code)
            return None
    
    def get_etf_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取ETF日线数据 (SQLite存储)"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('etf_daily', self.fund_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code}")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
                        print(f"🔄 增量更新 {ts_code} 从 {start_date}")
        
        print(f"🔄 获取 {ts_code} ETF数据...")
        try:
            new_data = self.pro.fund_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无新数据")
                if not force_update:
                    return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'etf_daily', self.fund_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('etf_daily', self.fund_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} ETF数据失败: {e}")
            if not force_update:
                return self.load_from_database('etf_daily', self.fund_db, ts_code=ts_code)
            return None
    
    # ==================== 股票基本面数据 (SQLite存储) ====================
    
    def init_fundamental_tables(self):
        """初始化股票基本面数据表"""
        with sqlite3.connect(self.stock_db) as conn:
            # 财务指标表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_fina_indicator (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    eps REAL,
                    dt_eps REAL,
                    total_revenue_ps REAL,
                    revenue_ps REAL,
                    capital_rese_ps REAL,
                    surplus_rese_ps REAL,
                    undist_profit_ps REAL,
                    extra_item REAL,
                    profit_dedt REAL,
                    gross_margin REAL,
                    current_ratio REAL,
                    quick_ratio REAL,
                    cash_ratio REAL,
                    invturn_days REAL,
                    arturn_days REAL,
                    inv_turn REAL,
                    ar_turn REAL,
                    ca_turn REAL,
                    fa_turn REAL,
                    assets_turn REAL,
                    op_income REAL,
                    valuechange_income REAL,
                    interst_income REAL,
                    daa REAL,
                    ebit REAL,
                    ebitda REAL,
                    fcff REAL,
                    fcfe REAL,
                    current_exint REAL,
                    noncurrent_exint REAL,
                    interestdebt REAL,
                    netdebt REAL,
                    tangible_asset REAL,
                    working_capital REAL,
                    networking_capital REAL,
                    invest_capital REAL,
                    retained_earnings REAL,
                    diluted2_eps REAL,
                    bps REAL,
                    ocfps REAL,
                    retainedps REAL,
                    cfps REAL,
                    ebit_ps REAL,
                    fcff_ps REAL,
                    fcfe_ps REAL,
                    netprofit_margin REAL,
                    grossprofit_margin REAL,
                    cogs_of_sales REAL,
                    expense_of_sales REAL,
                    profit_to_gr REAL,
                    saleexp_to_gr REAL,
                    adminexp_of_gr REAL,
                    finaexp_of_gr REAL,
                    impai_ttm REAL,
                    gc_of_gr REAL,
                    op_of_gr REAL,
                    ebit_of_gr REAL,
                    roe REAL,
                    roe_waa REAL,
                    roe_dt REAL,
                    roa REAL,
                    npta REAL,
                    roic REAL,
                    roe_yearly REAL,
                    roa_yearly REAL,
                    roe_avg REAL,
                    opincome_of_ebt REAL,
                    investincome_of_ebt REAL,
                    n_op_profit_of_ebt REAL,
                    tax_to_ebt REAL,
                    dtprofit_to_profit REAL,
                    salescash_to_or REAL,
                    ocf_to_or REAL,
                    ocf_to_opincome REAL,
                    capitalized_to_da REAL,
                    debt_to_assets REAL,
                    assets_to_eqt REAL,
                    dp_assets_to_eqt REAL,
                    ca_to_assets REAL,
                    nca_to_assets REAL,
                    tbassets_to_totalassets REAL,
                    int_to_talcap REAL,
                    eqt_to_talcapital REAL,
                    currentdebt_to_debt REAL,
                    longdeb_to_debt REAL,
                    ocf_to_shortdebt REAL,
                    debt_to_eqt REAL,
                    eqt_to_debt REAL,
                    eqt_to_interestdebt REAL,
                    tangibleasset_to_debt REAL,
                    tangasset_to_intdebt REAL,
                    tangibleasset_to_netdebt REAL,
                    ocf_to_debt REAL,
                    ocf_to_interestdebt REAL,
                    ocf_to_netdebt REAL,
                    ebit_to_interest REAL,
                    longdebt_to_workingcapital REAL,
                    ebitda_to_debt REAL,
                    turn_days REAL,
                    roa_dp REAL,
                    roa2_yearly REAL,
                    fixed_assets REAL,
                    profit_prefin_exp REAL,
                    non_op_profit REAL,
                    op_to_ebt REAL,
                    nop_to_ebt REAL,
                    ocf_to_profit REAL,
                    cash_to_liqdebt REAL,
                    cash_to_liqdebt_withinterest REAL,
                    op_to_liqdebt REAL,
                    op_to_debt REAL,
                    roic_yearly REAL,
                    total_fa_trun REAL,
                    profit_to_op REAL,
                    q_opincome REAL,
                    q_investincome REAL,
                    q_dtprofit REAL,
                    q_eps REAL,
                    q_netprofit_margin REAL,
                    q_gsprofit_margin REAL,
                    q_exp_to_sales REAL,
                    q_profit_to_gr REAL,
                    q_saleexp_to_gr REAL,
                    q_adminexp_to_gr REAL,
                    q_finaexp_to_gr REAL,
                    q_impair_to_gr_ttm REAL,
                    q_gc_to_gr REAL,
                    q_op_to_gr REAL,
                    q_roe REAL,
                    q_dt_roe REAL,
                    q_npta REAL,
                    q_opincome_to_ebt REAL,
                    q_investincome_to_ebt REAL,
                    q_dtprofit_to_profit REAL,
                    q_salescash_to_or REAL,
                    q_ocf_to_sales REAL,
                    q_ocf_to_or REAL,
                    basic_eps_yoy REAL,
                    dt_eps_yoy REAL,
                    cfps_yoy REAL,
                    op_yoy REAL,
                    ebt_yoy REAL,
                    netprofit_yoy REAL,
                    dt_netprofit_yoy REAL,
                    ocf_yoy REAL,
                    roe_yoy REAL,
                    bps_yoy REAL,
                    assets_yoy REAL,
                    eqt_yoy REAL,
                    tr_yoy REAL,
                    or_yoy REAL,
                    q_gr_yoy REAL,
                    q_gr_qoq REAL,
                    q_sales_yoy REAL,
                    q_sales_qoq REAL,
                    q_op_yoy REAL,
                    q_op_qoq REAL,
                    q_profit_yoy REAL,
                    q_profit_qoq REAL,
                    q_netprofit_yoy REAL,
                    q_netprofit_qoq REAL,
                    equity_yoy REAL,
                    rd_exp REAL,
                    update_flag TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 每日估值指标表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily_basic (
                    ts_code TEXT,
                    trade_date TEXT,
                    close REAL,
                    turnover_rate REAL,
                    turnover_rate_f REAL,
                    volume_ratio REAL,
                    pe REAL,
                    pe_ttm REAL,
                    pb REAL,
                    ps REAL,
                    ps_ttm REAL,
                    dv_ratio REAL,
                    dv_ttm REAL,
                    total_share REAL,
                    float_share REAL,
                    free_share REAL,
                    total_mv REAL,
                    circ_mv REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 业绩预告表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_forecast (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    type TEXT,
                    p_change_min REAL,
                    p_change_max REAL,
                    net_profit_min REAL,
                    net_profit_max REAL,
                    last_parent_net REAL,
                    first_ann_date TEXT,
                    summary TEXT,
                    change_reason TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 股票复权因子表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_adj_factor (
                    ts_code TEXT,
                    trade_date TEXT,
                    adj_factor REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 分红送转表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_dividend (
                    ts_code TEXT,
                    end_date TEXT,
                    ann_date TEXT,
                    div_proc TEXT,
                    stk_div REAL,
                    stk_bo_rate REAL,
                    stk_co_rate REAL,
                    cash_div REAL,
                    cash_div_tax REAL,
                    record_date TEXT,
                    ex_date TEXT,
                    pay_date TEXT,
                    div_listdate TEXT,
                    imp_ann_date TEXT,
                    base_share REAL,
                    base_date TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, end_date, ann_date)
                )
            ''')
            
            # 限售解禁表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_share_float (
                    ts_code TEXT,
                    ann_date TEXT,
                    float_date TEXT,
                    float_share REAL,
                    float_ratio REAL,
                    holder_name TEXT,
                    share_type TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, float_date)
                )
            ''')
            
            # 股东户数表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_holder_number (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    holder_num INTEGER,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 指数日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS index_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 指数成分股权重表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS index_weight (
                    index_code TEXT,
                    con_code TEXT,
                    trade_date TEXT,
                    weight REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (index_code, con_code, trade_date)
                )
            ''')
            
            # 行业分类表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_industry_classify (
                    ts_code TEXT,
                    industry_code TEXT,
                    industry_name TEXT,
                    src TEXT,
                    level TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, industry_code, src)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_code ON stock_fina_indicator(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_date ON stock_fina_indicator(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_daily_basic_code ON stock_daily_basic(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_code ON stock_forecast(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_date ON stock_forecast(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_forecast_date ON stock_forecast(end_date)')
            
            # 新增表的索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_adj_factor_code ON stock_adj_factor(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_adj_factor_date ON stock_adj_factor(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_dividend_code ON stock_dividend(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_dividend_date ON stock_dividend(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_share_float_code ON stock_share_float(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_share_float_date ON stock_share_float(float_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_holder_number_code ON stock_holder_number(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_holder_number_date ON stock_holder_number(end_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_code ON index_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_date ON index_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_code ON index_weight(index_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_con ON index_weight(con_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_weight_date ON index_weight(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_code ON stock_industry_classify(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_name ON stock_industry_classify(industry_code)')
    
    def get_stock_adj_factor(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取复权因子数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_adj_factor', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 复权因子")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 复权因子数据...")
        try:
            new_data = self.pro.adj_factor(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无复权因子数据")
                if not force_update:
                    return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_adj_factor', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 复权因子失败: {e}")
            if not force_update:
                return self.load_from_database('stock_adj_factor', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_dividend(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取分红送转数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_dividend', self.stock_db, ts_code, 'end_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=30):
                        print(f"📁 使用数据库缓存: {ts_code} 分红送转")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 分红送转数据...")
        try:
            new_data = self.pro.dividend(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无分红送转数据")
                if not force_update:
                    return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_dividend', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 分红送转失败: {e}")
            if not force_update:
                return self.load_from_database('stock_dividend', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_share_float(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取限售解禁数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_share_float', self.stock_db, ts_code, 'float_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=30):
                        print(f"📁 使用数据库缓存: {ts_code} 限售解禁")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 限售解禁数据...")
        try:
            new_data = self.pro.share_float(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无限售解禁数据")
                if not force_update:
                    return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_share_float', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 限售解禁失败: {e}")
            if not force_update:
                return self.load_from_database('stock_share_float', self.stock_db, ts_code=ts_code)
            return None
    
    def get_stock_holder_number(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股东户数数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('stock_holder_number', self.stock_db, ts_code, 'end_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=90):
                        print(f"📁 使用数据库缓存: {ts_code} 股东户数")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 股东户数数据...")
        try:
            new_data = self.pro.stk_holdernumber(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无股东户数数据")
                if not force_update:
                    return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_holder_number', self.stock_db)
            # 返回完整数据
            return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 股东户数失败: {e}")
            if not force_update:
                return self.load_from_database('stock_holder_number', self.stock_db, ts_code=ts_code)
            return None
    
    def get_index_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('index_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 指数日线")
                        return existing_data
        
        print(f"🔄 获取 {ts_code} 指数日线数据...")
        try:
            new_data = self.pro.index_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无指数日线数据")
                if not force_update:
                    return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'index_daily', self.stock_db)
            # 返回完整数据
            return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 指数日线失败: {e}")
            if not force_update:
                return self.load_from_database('index_daily', self.stock_db, ts_code=ts_code)
            return None
    
    def get_index_weight(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取指数成分股权重数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 指数成分股权重数据...")
        try:
            new_data = self.pro.index_weight(
                index_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无指数成分股权重数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'index_weight', self.stock_db)
            print(f"✅ {ts_code} 指数成分股权重数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 指数成分股权重失败: {e}")
            return None
    
    def get_stock_fina_indicator(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取财务指标数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 财务指标数据...")
        try:
            new_data = self.pro.fina_indicator(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无财务指标数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_fina_indicator', self.stock_db)
            print(f"✅ {ts_code} 财务指标数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 财务指标失败: {e}")
            return None
    
    def get_stock_forecast(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取业绩预告数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 业绩预告数据...")
        try:
            new_data = self.pro.forecast(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无业绩预告数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_forecast', self.stock_db)
            print(f"✅ {ts_code} 业绩预告数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 业绩预告失败: {e}")
            return None
    
    def get_stock_daily_basic(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取股票每日估值指标数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 估值数据...")
        try:
            new_data = self.pro.daily_basic(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {ts_code} 无估值数据")
                return new_data
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'stock_daily_basic', self.stock_db)
            print(f"✅ {ts_code} 估值数据已保存")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 估值数据失败: {e}")
            return None
    
    def get_comprehensive_dividend_data(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取综合分红送转数据，主要使用dividend接口"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 分红送转数据...")
        
        try:
            # 使用已有的dividend方法
            dividend_data = self.get_stock_dividend(ts_code, start_date, end_date, force_update)
            
            if dividend_data is not None and not dividend_data.empty:
                print(f"✅ {ts_code} 分红送转数据已获取 (共{len(dividend_data)}条)")
                return dividend_data
            else:
                print(f"⚠️ {ts_code} 无分红送转数据")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ 获取 {ts_code} 分红送转数据失败: {e}")
            return pd.DataFrame()
    
    def cache_all_comprehensive_dividend(self, start_date='20100101', end_date=None):
        """批量缓存所有股票的综合分红送转数据 - 使用批量API"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始批量缓存综合分红送转数据...")
        print(f"   时间范围: {start_date} - {end_date}")
        
        # 直接调用扩展数据缓存方法中的分红送转部分
        print("   正在使用批量分红送转数据获取...")
        
        success_count = 0
        current_year = int(start_date[:4])
        end_year = int(end_date[:4])
        
        for year in range(current_year, end_year + 1):
            print(f"\n📊 获取 {year} 年分红送转数据...")
            
            try:
                # 批量获取分红送转数据
                dividend_data = self.pro.dividend(
                    ann_date=f"{year}0101",
                    fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date'
                )
                
                if not dividend_data.empty:
                    # 保存到数据库
                    self.save_to_database(dividend_data, 'stock_dividend', self.stock_db)
                    print(f"✅ 保存 {len(dividend_data):,} 条分红送转数据")
                    success_count += len(dividend_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_dividend', self.stock_db, 'ts_code', 'end_date')
                else:
                    print(f"⚠️ {year} 年无分红送转数据")
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"❌ 获取 {year} 年分红送转数据失败: {e}")
                time.sleep(2.0)
        
        print(f"\n🎉 分红送转数据批量缓存完成！共保存 {success_count:,} 条记录")

    # ==================== 批量缓存方法 ====================
    
    def get_stock_list(self, exclude_delisted=True):
        """获取股票列表"""
        stock_basic = self.get_stock_basic()
        if stock_basic is None or stock_basic.empty:
            print("❌ 无法获取股票基本信息")
            return pd.DataFrame()

        if exclude_delisted:
            # 只返回正常上市和暂停上市的股票，排除退市股票
            # 注意：get_stock_basic已经使用了list_status='L'，所以这里已经排除了退市股票
            print(f"📊 有效股票数量: {len(stock_basic)} 只（已排除退市股票）")

        return stock_basic

    def is_stock_delisted(self, ts_code):
        """检查股票是否已退市"""
        try:
            # 尝试获取股票基本信息
            stock_info = self.pro.stock_basic(ts_code=ts_code, fields='ts_code,list_status')
            if stock_info.empty:
                return True  # 查不到信息，可能已退市

            list_status = stock_info['list_status'].iloc[0]
            return list_status == 'D'  # D表示退市

        except Exception as e:
            print(f"⚠️ 检查 {ts_code} 状态失败: {e}")
            return False

    def filter_active_stocks(self, stock_codes):
        """过滤出仍在交易的股票"""
        print(f"🔍 检查 {len(stock_codes)} 只股票的交易状态...")

        active_stocks = []
        delisted_stocks = []

        # 获取当前所有正常上市的股票
        current_stocks = self.get_stock_basic()
        if current_stocks is not None:
            active_stock_codes = set(current_stocks['ts_code'].tolist())

            for ts_code in stock_codes:
                if ts_code in active_stock_codes:
                    active_stocks.append(ts_code)
                else:
                    delisted_stocks.append(ts_code)
        else:
            # 如果无法获取基本信息，逐个检查
            for ts_code in stock_codes:
                if not self.is_stock_delisted(ts_code):
                    active_stocks.append(ts_code)
                else:
                    delisted_stocks.append(ts_code)

        if delisted_stocks:
            print(f"⚠️ 发现 {len(delisted_stocks)} 只退市股票，已排除")
            print(f"   退市股票: {delisted_stocks[:10]}{'...' if len(delisted_stocks) > 10 else ''}")

        print(f"✅ 有效股票: {len(active_stocks)} 只")
        return active_stocks
    
    def cache_all_stocks_data(self, start_date=None, end_date=None, force_update=False):
        """增量缓存股票日线数据 - 只更新缺失的数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始增量更新股票日线数据...")
        
        # 如果不强制更新，先检查数据库中的最新日期
        if not force_update:
            latest_date = self.get_table_latest_date('stock_daily', self.stock_db, 'trade_date')
            if latest_date:
                print(f"📅 数据库最新日期: {latest_date}")
                # 从最新日期的下一天开始更新
                start_date = (pd.to_datetime(latest_date) + timedelta(days=1)).strftime('%Y%m%d')
                print(f"🔄 从 {start_date} 开始增量更新")
                
                # 如果最新日期已经是今天或更新，则无需更新
                if start_date >= end_date:
                    print("✅ 股票数据已是最新，无需更新")
                    return
            else:
                # 如果没有数据，从默认开始日期更新
                start_date = start_date or '20200101'
                print(f"📊 数据库为空，从 {start_date} 开始全量更新")
        else:
            start_date = start_date or '20200101'
            print(f"🔄 强制更新模式，从 {start_date} 开始")
        
        # 分批获取数据，避免一次性获取过多数据
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        success_count = 0
        
        while current_date <= end_dt:
            # 每次获取30天的数据
            batch_end = min(current_date + timedelta(days=30), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"\n📊 获取日期范围: {batch_start} - {batch_end_str}")
            
            try:
                # 使用批量API获取所有股票的日线数据
                daily_data = self.pro.daily(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not daily_data.empty:
                    # 过滤掉退市股票的数据
                    current_stocks = self.get_stock_basic()
                    if current_stocks is not None:
                        active_stock_codes = set(current_stocks['ts_code'].tolist())
                        before_count = len(daily_data)
                        daily_data = daily_data[daily_data['ts_code'].isin(active_stock_codes)]
                        after_count = len(daily_data)

                        if before_count > after_count:
                            print(f"   🔍 过滤退市股票数据: {before_count - after_count} 条")

                    if not daily_data.empty:
                        # 保存到数据库
                        self.save_to_database(daily_data, 'stock_daily', self.stock_db)
                        print(f"✅ 保存 {len(daily_data):,} 条股票日线数据")
                        success_count += len(daily_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_daily', self.stock_db, 'ts_code', 'trade_date')
                else:
                    print(f"⚠️ 该日期范围无数据")
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"❌ 获取 {batch_start}-{batch_end_str} 数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        print(f"\n🎉 股票日线数据增量更新完成！共保存 {success_count:,} 条记录")
    
    def cache_all_daily_basic(self, start_date=None, end_date=None):
        """批量缓存市场估值数据 - 使用批量API"""
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f"🔄 开始批量缓存市场估值数据 ({start_date} - {end_date})...")
        
        # 分批获取数据
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        success_count = 0
        
        while current_date <= end_dt:
            # 每次获取15天的数据（daily_basic接口数据量较大）
            batch_end = min(current_date + timedelta(days=15), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"\n📊 获取估值数据: {batch_start} - {batch_end_str}")
            
            try:
                # 使用批量API获取市场估值数据
                basic_data = self.pro.daily_basic(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not basic_data.empty:
                    # 保存到数据库
                    self.save_to_database(basic_data, 'stock_daily_basic', self.stock_db)
                    print(f"✅ 保存 {len(basic_data):,} 条估值数据")
                    success_count += len(basic_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_daily_basic', self.stock_db, 'ts_code', 'trade_date')
                else:
                    print(f"⚠️ 该日期范围无数据")
                
                # 控制请求频率
                time.sleep(1.5)
                
            except Exception as e:
                print(f"❌ 获取 {batch_start}-{batch_end_str} 估值数据失败: {e}")
                time.sleep(3.0)
            
            current_date = batch_end + timedelta(days=1)
        
        print(f"\n🎉 市场估值数据批量缓存完成！共保存 {success_count:,} 条记录")
    
    def cache_all_funds_data(self, start_date=None, end_date=None, force_update=False):
        """增量缓存基金数据 - 只更新缺失的数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始增量更新基金数据...")
        
        success_count = 0
        
        # 1. 处理ETF数据
        print("\n📈 增量更新ETF数据...")
        if not force_update:
            latest_etf_date = self.get_table_latest_date('etf_daily', self.fund_db, 'trade_date')
            if latest_etf_date:
                print(f"📅 ETF数据最新日期: {latest_etf_date}")
                etf_start_date = (pd.to_datetime(latest_etf_date) + timedelta(days=1)).strftime('%Y%m%d')
                print(f"🔄 从 {etf_start_date} 开始更新ETF数据")
                
                if etf_start_date >= end_date:
                    print("✅ ETF数据已是最新，无需更新")
                else:
                    success_count += self._update_etf_data(etf_start_date, end_date)
            else:
                etf_start_date = start_date or '20200101'
                print(f"📊 ETF数据库为空，从 {etf_start_date} 开始全量更新")
                success_count += self._update_etf_data(etf_start_date, end_date)
        else:
            etf_start_date = start_date or '20200101'
            print(f"� 强制更新ETF数据，从 {etf_start_date} 开始")
            success_count += self._update_etf_data(etf_start_date, end_date)
        
        # 2. 处理基金净值数据
        print("\n📊 增量更新基金净值数据...")
        if not force_update:
            latest_nav_date = self.get_table_latest_date('fund_nav', self.fund_db, 'nav_date')
            if latest_nav_date:
                print(f"📅 基金净值最新日期: {latest_nav_date}")
                nav_start_date = (pd.to_datetime(latest_nav_date) + timedelta(days=1)).strftime('%Y%m%d')
                print(f"🔄 从 {nav_start_date} 开始更新基金净值数据")
                
                if nav_start_date >= end_date:
                    print("✅ 基金净值数据已是最新，无需更新")
                else:
                    success_count += self._update_fund_nav_data(nav_start_date, end_date)
            else:
                nav_start_date = start_date or '20200101'
                print(f"📊 基金净值数据库为空，从 {nav_start_date} 开始全量更新")
                success_count += self._update_fund_nav_data(nav_start_date, end_date)
        else:
            nav_start_date = start_date or '20200101'
            print(f"🔄 强制更新基金净值数据，从 {nav_start_date} 开始")
            success_count += self._update_fund_nav_data(nav_start_date, end_date)
        
        print(f"\n🎉 基金数据增量更新完成！共保存 {success_count:,} 条记录")
    
    def _update_etf_data(self, start_date, end_date):
        """更新ETF数据的辅助方法"""
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        count = 0
        
        while current_date <= end_dt:
            batch_end = min(current_date + timedelta(days=30), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"   获取ETF数据: {batch_start} - {batch_end_str}")
            
            try:
                etf_data = self.pro.fund_daily(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not etf_data.empty:
                    etf_filtered = etf_data[
                        etf_data['ts_code'].str.endswith('.SH') | 
                        etf_data['ts_code'].str.endswith('.SZ')
                    ]
                    
                    if not etf_filtered.empty:
                        self.save_to_database(etf_filtered, 'etf_daily', self.fund_db)
                        print(f"   ✅ 保存 {len(etf_filtered):,} 条ETF数据")
                        count += len(etf_filtered)
                        self.clean_duplicate_data('etf_daily', self.fund_db, 'ts_code', 'trade_date')
                
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取ETF数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        return count
    
    def _update_fund_nav_data(self, start_date, end_date):
        """更新基金净值数据的辅助方法"""
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        count = 0
        
        while current_date <= end_dt:
            batch_end = min(current_date + timedelta(days=30), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"   获取基金净值: {batch_start} - {batch_end_str}")
            
            try:
                nav_data = self.pro.fund_nav(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not nav_data.empty:
                    self.save_to_database(nav_data, 'fund_nav', self.fund_db)
                    print(f"   ✅ 保存 {len(nav_data):,} 条基金净值数据")
                    count += len(nav_data)
                    self.clean_duplicate_data('fund_nav', self.fund_db, 'ts_code', 'nav_date')
                
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取基金净值数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        return count
    
    def cache_all_fundamental_data(self, start_date='20100101', end_date=None):
        """批量缓存所有股票的基本面数据 - 使用批量API"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始批量缓存股票基本面数据...")
        print(f"   时间范围: {start_date} - {end_date}")
        
        success_count = 0
        
        # 1. 批量获取财务指标数据
        print("\n📊 批量获取财务指标数据...")
        # 财务指标按季度获取，分批处理
        current_year = int(start_date[:4])
        end_year = int(end_date[:4])
        
        for year in range(current_year, end_year + 1):
            for period in ['0331', '0630', '0930', '1231']:
                end_date_period = f"{year}{period}"
                if end_date_period > end_date:
                    break
                    
                print(f"   获取 {year} 年 {period[:2]}/{period[2:]} 财务指标...")
                
                try:
                    # 批量获取财务指标
                    fina_data = self.pro.fina_indicator(
                        period=end_date_period,
                        fields='ts_code,end_date,eps,bps,roe,roa,netprofit_margin,grossprofit_margin,current_ratio,debt_to_assets,roe_waa,roe_dt,roa2_yearly'
                    )
                    
                    if not fina_data.empty:
                        # 保存到数据库
                        self.save_to_database(fina_data, 'stock_fina_indicator', self.stock_db)
                        print(f"   ✅ 保存 {len(fina_data):,} 条财务指标数据")
                        success_count += len(fina_data)
                        
                        # 清理重复数据
                        self.clean_duplicate_data('stock_fina_indicator', self.stock_db, 'ts_code', 'end_date')
                    
                    # 控制请求频率
                    time.sleep(1.0)
                    
                except Exception as e:
                    print(f"   ❌ 获取 {year}{period} 财务指标失败: {e}")
                    time.sleep(2.0)
        
        # 2. 批量获取业绩预告数据
        print("\n📊 批量获取业绩预告数据...")
        # 业绩预告按年度获取
        for year in range(current_year, end_year + 1):
            print(f"   获取 {year} 年业绩预告...")
            
            try:
                # 批量获取业绩预告
                forecast_data = self.pro.forecast(
                    ann_date=f"{year}0101",
                    fields='ts_code,ann_date,end_date,type,p_change_min,p_change_max,net_profit_min,net_profit_max,summary,change_reason'
                )
                
                if not forecast_data.empty:
                    # 保存到数据库
                    self.save_to_database(forecast_data, 'stock_forecast', self.stock_db)
                    print(f"   ✅ 保存 {len(forecast_data):,} 条业绩预告数据")
                    success_count += len(forecast_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_forecast', self.stock_db, 'ts_code', 'ann_date')
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取 {year} 年业绩预告失败: {e}")
                time.sleep(2.0)
        
        print(f"\n🎉 基本面数据批量缓存完成！共保存 {success_count:,} 条记录")
    
    def cache_all_extended_data(self, start_date='20100101', end_date=None):
        """批量缓存扩展数据(复权、分红、解禁、股东户数) - 使用批量API"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始批量缓存扩展数据...")
        print(f"   时间范围: {start_date} - {end_date}")
        
        success_count = 0
        
        # 1. 批量获取复权因子数据
        print("\n📊 批量获取复权因子数据...")
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        while current_date <= end_dt:
            # 每次获取60天的数据
            batch_end = min(current_date + timedelta(days=60), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"   获取复权因子: {batch_start} - {batch_end_str}")
            
            try:
                # 批量获取复权因子
                adj_data = self.pro.adj_factor(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not adj_data.empty:
                    # 保存到数据库
                    self.save_to_database(adj_data, 'stock_adj_factor', self.stock_db)
                    print(f"   ✅ 保存 {len(adj_data):,} 条复权因子数据")
                    success_count += len(adj_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_adj_factor', self.stock_db, 'ts_code', 'trade_date')
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取复权因子数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        # 2. 批量获取分红送转数据
        print("\n📊 批量获取分红送转数据...")
        current_year = int(start_date[:4])
        end_year = int(end_date[:4])
        
        for year in range(current_year, end_year + 1):
            print(f"   获取 {year} 年分红送转数据...")
            
            try:
                # 批量获取分红送转数据
                dividend_data = self.pro.dividend(
                    ann_date=f"{year}0101",
                    fields='ts_code,end_date,ann_date,div_proc,stk_div,stk_bo_rate,stk_co_rate,cash_div,cash_div_tax,record_date,ex_date,pay_date'
                )
                
                if not dividend_data.empty:
                    # 保存到数据库
                    self.save_to_database(dividend_data, 'stock_dividend', self.stock_db)
                    print(f"   ✅ 保存 {len(dividend_data):,} 条分红送转数据")
                    success_count += len(dividend_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_dividend', self.stock_db, 'ts_code', 'end_date')
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取 {year} 年分红送转数据失败: {e}")
                time.sleep(2.0)
        
        # 3. 批量获取限售解禁数据
        print("\n📊 批量获取限售解禁数据...")
        for year in range(current_year, end_year + 1):
            print(f"   获取 {year} 年限售解禁数据...")
            
            try:
                # 批量获取限售解禁数据
                float_data = self.pro.share_float(
                    ann_date=f"{year}0101",
                    fields='ts_code,ann_date,float_date,float_share,float_ratio,holder_name,share_type'
                )
                
                if not float_data.empty:
                    # 保存到数据库
                    self.save_to_database(float_data, 'stock_share_float', self.stock_db)
                    print(f"   ✅ 保存 {len(float_data):,} 条限售解禁数据")
                    success_count += len(float_data)
                    
                    # 清理重复数据
                    self.clean_duplicate_data('stock_share_float', self.stock_db, 'ts_code', 'float_date')
                
                # 控制请求频率
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取 {year} 年限售解禁数据失败: {e}")
                time.sleep(2.0)
        
        # 4. 批量获取股东户数数据
        print("\n📊 批量获取股东户数数据...")
        for year in range(current_year, end_year + 1):
            for period in ['0331', '0630', '0930', '1231']:
                end_date_period = f"{year}{period}"
                if end_date_period > end_date:
                    break
                    
                print(f"   获取 {year} 年 {period[:2]}/{period[2:]} 股东户数...")
                
                try:
                    # 批量获取股东户数数据
                    holder_data = self.pro.stk_holdernumber(
                        end_date=end_date_period,
                        fields='ts_code,ann_date,end_date,holder_num'
                    )
                    
                    if not holder_data.empty:
                        # 保存到数据库
                        self.save_to_database(holder_data, 'stock_holder_number', self.stock_db)
                        print(f"   ✅ 保存 {len(holder_data):,} 条股东户数数据")
                        success_count += len(holder_data)
                        
                        # 清理重复数据
                        self.clean_duplicate_data('stock_holder_number', self.stock_db, 'ts_code', 'end_date')
                    
                    # 控制请求频率
                    time.sleep(1.0)
                    
                except Exception as e:
                    print(f"   ❌ 获取股东户数数据失败: {e}")
                    time.sleep(2.0)
        
        print(f"\n🎉 扩展数据批量缓存完成！共保存 {success_count:,} 条记录")
    
    def cache_all_index_data(self, start_date=None, end_date=None, force_update=False):
        """增量缓存指数数据 - 只更新缺失的数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始增量更新指数数据...")
        
        success_count = 0
        
        # 1. 更新指数日线数据
        print("\n📊 增量更新指数日线数据...")
        if not force_update:
            latest_date = self.get_table_latest_date('index_daily', self.stock_db, 'trade_date')
            if latest_date:
                print(f"📅 指数数据最新日期: {latest_date}")
                index_start_date = (pd.to_datetime(latest_date) + timedelta(days=1)).strftime('%Y%m%d')
                print(f"🔄 从 {index_start_date} 开始更新指数数据")
                
                if index_start_date >= end_date:
                    print("✅ 指数数据已是最新，无需更新")
                else:
                    success_count += self._update_index_daily_data(index_start_date, end_date)
            else:
                index_start_date = start_date or '20200101'
                print(f"📊 指数数据库为空，从 {index_start_date} 开始全量更新")
                success_count += self._update_index_daily_data(index_start_date, end_date)
        else:
            index_start_date = start_date or '20200101'
            print(f"🔄 强制更新指数数据，从 {index_start_date} 开始")
            success_count += self._update_index_daily_data(index_start_date, end_date)
        
        print(f"\n🎉 指数数据增量更新完成！共保存 {success_count:,} 条记录")
    
    def _update_index_daily_data(self, start_date, end_date):
        """更新指数日线数据的辅助方法"""
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        count = 0
        
        while current_date <= end_dt:
            batch_end = min(current_date + timedelta(days=30), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"   获取指数日线: {batch_start} - {batch_end_str}")
            
            try:
                index_data = self.pro.index_daily(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not index_data.empty:
                    self.save_to_database(index_data, 'index_daily', self.stock_db)
                    print(f"   ✅ 保存 {len(index_data):,} 条指数日线数据")
                    count += len(index_data)
                    self.clean_duplicate_data('index_daily', self.stock_db, 'ts_code', 'trade_date')
                
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取指数日线数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        return count
    
    def cache_all_industry_data(self, start_date=None, end_date=None, force_update=False):
        """增量缓存行业板块数据 - 只更新缺失的数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 开始增量更新行业板块数据...")
        
        success_count = 0
        
        # 更新申万行业指数日线数据
        print("\n📊 增量更新申万行业指数数据...")
        if not force_update:
            latest_date = self.get_table_latest_date('sw_industry_daily', self.stock_db, 'trade_date')
            if latest_date:
                print(f"📅 申万行业数据最新日期: {latest_date}")
                sw_start_date = (pd.to_datetime(latest_date) + timedelta(days=1)).strftime('%Y%m%d')
                print(f"🔄 从 {sw_start_date} 开始更新申万行业数据")
                
                if sw_start_date >= end_date:
                    print("✅ 申万行业数据已是最新，无需更新")
                else:
                    success_count += self._update_sw_industry_data(sw_start_date, end_date)
            else:
                sw_start_date = start_date or '20200101'
                print(f"📊 申万行业数据库为空，从 {sw_start_date} 开始全量更新")
                success_count += self._update_sw_industry_data(sw_start_date, end_date)
        else:
            sw_start_date = start_date or '20200101'
            print(f"🔄 强制更新申万行业数据，从 {sw_start_date} 开始")
            success_count += self._update_sw_industry_data(sw_start_date, end_date)
        
        print(f"\n🎉 行业板块数据增量更新完成！共保存 {success_count:,} 条记录")
    
    def _update_sw_industry_data(self, start_date, end_date):
        """更新申万行业数据的辅助方法"""
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        count = 0
        
        while current_date <= end_dt:
            batch_end = min(current_date + timedelta(days=30), end_dt)
            batch_start = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"   获取申万行业数据: {batch_start} - {batch_end_str}")
            
            try:
                sw_data = self.pro.sw_daily(
                    start_date=batch_start,
                    end_date=batch_end_str
                )
                
                if not sw_data.empty:
                    self._ensure_sw_industry_columns(sw_data.columns)
                    self.save_to_database(sw_data, 'sw_industry_daily', self.stock_db)
                    print(f"   ✅ 保存 {len(sw_data):,} 条申万行业数据")
                    count += len(sw_data)
                    self.clean_duplicate_data('sw_industry_daily', self.stock_db, 'ts_code', 'trade_date')
                
                time.sleep(1.0)
                
            except Exception as e:
                print(f"   ❌ 获取申万行业数据失败: {e}")
                time.sleep(2.0)
            
            current_date = batch_end + timedelta(days=1)
        
        return count
    
    def cache_main_industry_indices(self, start_date='20100101', end_date=None):
        """缓存主要行业指数数据 - 使用批量API"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print("🔄 缓存主要行业指数数据...")
        print(f"   时间范围: {start_date} - {end_date}")
        
        # 直接调用批量行业数据缓存方法
        self.cache_all_industry_data(start_date, end_date)
    
    def get_industry_summary(self):
        """获取行业板块数据统计"""
        print("📊 行业板块数据统计")
        print("=" * 40)
        
        if os.path.exists(self.stock_db):
            with sqlite3.connect(self.stock_db) as conn:
                try:
                    # 行业成分股统计
                    industry_count = conn.execute("SELECT COUNT(DISTINCT industry_code) FROM industry_stock").fetchone()[0]
                    stock_count = conn.execute("SELECT COUNT(DISTINCT con_code) FROM industry_stock").fetchone()[0]
                    print(f"📈 行业成分股:")
                    print(f"   行业数量: {industry_count}")
                    print(f"   股票数量: {stock_count}")
                except:
                    print(f"📈 行业成分股: 暂无数据")
                
                try:
                    # 行业指数统计
                    industry_daily_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM industry_daily").fetchone()[0]
                    industry_daily_records = conn.execute("SELECT COUNT(*) FROM industry_daily").fetchone()[0]
                    print(f"\n📊 行业指数日线:")
                    print(f"   行业指数: {industry_daily_count} 个")
                    print(f"   总记录数: {industry_daily_records:,} 条")
                except:
                    print(f"\n📊 行业指数日线: 暂无数据")
                
                try:
                    # 申万行业指数统计
                    sw_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM sw_industry_daily").fetchone()[0]
                    sw_records = conn.execute("SELECT COUNT(*) FROM sw_industry_daily").fetchone()[0]
                    print(f"\n📊 申万行业指数:")
                    print(f"   指数数量: {sw_count} 个")
                    print(f"   总记录数: {sw_records:,} 条")
                except:
                    print(f"\n📊 申万行业指数: 暂无数据")
                
                try:
                    # 资金流向统计
                    money_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM industry_money_flow").fetchone()[0]
                    money_records = conn.execute("SELECT COUNT(*) FROM industry_money_flow").fetchone()[0]
                    print(f"\n💰 行业资金流向:")
                    print(f"   行业数量: {money_count} 个")
                    print(f"   总记录数: {money_records:,} 条")
                except:
                    print(f"\n💰 行业资金流向: 暂无数据")
        else:
            print("❌ 数据库文件不存在")
    
    def upgrade_database_schema(self):
        """升级数据库结构，添加缺失的字段"""
        try:
            # 升级基金数据库
            with sqlite3.connect(self.fund_db) as conn:
                cursor = conn.cursor()
                
                # 检查并添加 ann_date 字段
                cursor.execute("PRAGMA table_info(fund_nav)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'ann_date' not in columns:
                    print("添加 ann_date 字段到 fund_nav 表...")
                    cursor.execute("ALTER TABLE fund_nav ADD COLUMN ann_date TEXT")
                    
                if 'update_flag' not in columns:
                    print("添加 update_flag 字段到 fund_nav 表...")
                    cursor.execute("ALTER TABLE fund_nav ADD COLUMN update_flag TEXT")
                
                conn.commit()
            
            # 升级股票数据库 - 检查财务指标表
            with sqlite3.connect(self.stock_db) as conn:
                cursor = conn.cursor()
                
                # 检查财务指标表是否存在 roa2_yearly 字段
                try:
                    cursor.execute("PRAGMA table_info(stock_fina_indicator)")
                    fina_columns = [column[1] for column in cursor.fetchall()]
                    
                    if 'roa2_yearly' not in fina_columns:
                        print("添加 roa2_yearly 字段到 stock_fina_indicator 表...")
                        cursor.execute("ALTER TABLE stock_fina_indicator ADD COLUMN roa2_yearly REAL")
                except:
                    # 如果表不存在，什么都不做
                    pass
                
                conn.commit()
                
            print("数据库结构升级完成")
                
        except Exception as e:
            print(f"数据库升级出错: {e}")
    
    def get_stock_fundamental_summary(self, ts_code):
        """获取股票基本面数据汇总"""
        print(f"📊 {ts_code} 基本面数据汇总")
        print("=" * 40)
        
        # 获取最新财务指标
        fina_data = self.load_from_database('stock_fina_indicator', self.stock_db, ts_code=ts_code)
        if not fina_data.empty:
            latest_fina = fina_data.iloc[-1]
            print(f"📈 最新财务指标 ({latest_fina['end_date']}):")
            print(f"   每股收益(EPS): {latest_fina.get('eps', 'N/A')}")
            print(f"   每股净资产(BPS): {latest_fina.get('bps', 'N/A')}")
            print(f"   净资产收益率(ROE): {latest_fina.get('roe', 'N/A')}%")
            print(f"   总资产收益率(ROA): {latest_fina.get('roa', 'N/A')}%")
            print(f"   净利润率: {latest_fina.get('netprofit_margin', 'N/A')}%")
            print(f"   毛利润率: {latest_fina.get('grossprofit_margin', 'N/A')}%")
            print(f"   流动比率: {latest_fina.get('current_ratio', 'N/A')}")
            print(f"   资产负债率: {latest_fina.get('debt_to_assets', 'N/A')}%")
        else:
            print("   暂无财务指标数据")
        
        # 获取最新估值数据
        basic_data = self.load_from_database('stock_daily_basic', self.stock_db, ts_code=ts_code)
        if not basic_data.empty:
            latest_basic = basic_data.iloc[-1]
            print(f"\n💰 最新估值指标 ({latest_basic['trade_date']}):")
            print(f"   市盈率(PE): {latest_basic.get('pe', 'N/A')}")
            print(f"   市盈率TTM: {latest_basic.get('pe_ttm', 'N/A')}")
            print(f"   市净率(PB): {latest_basic.get('pb', 'N/A')}")
            print(f"   市销率(PS): {latest_basic.get('ps', 'N/A')}")
            print(f"   总市值(万元): {latest_basic.get('total_mv', 'N/A')}")
            print(f"   流通市值(万元): {latest_basic.get('circ_mv', 'N/A')}")
            print(f"   换手率: {latest_basic.get('turnover_rate', 'N/A')}%")
        else:
            print("\n   暂无估值数据")
        
        # 获取业绩预告
        forecast_data = self.load_from_database('stock_forecast', self.stock_db, ts_code=ts_code)
        if not forecast_data.empty:
            latest_forecast = forecast_data.iloc[-1]
            print(f"\n📋 最新业绩预告 ({latest_forecast['end_date']}):")
            print(f"   变动类型: {latest_forecast.get('type', 'N/A')}")
            print(f"   净利润变动: {latest_forecast.get('p_change_min', 'N/A')}% ~ {latest_forecast.get('p_change_max', 'N/A')}%")
            print(f"   业绩说明: {latest_forecast.get('summary', 'N/A')[:100]}...")
        else:
            print("\n   暂无业绩预告")
        
        return {
            'fina_data': fina_data,
            'basic_data': basic_data,
            'forecast_data': forecast_data
        }
    
    # ==================== 行业板块数据 (SQLite存储) ====================
    
    def init_industry_tables(self):
        """初始化行业板块数据表"""
        with sqlite3.connect(self.stock_db) as conn:
            # 行业成分股表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_stock (
                    industry_code TEXT,
                    con_code TEXT,
                    con_name TEXT,
                    trade_date TEXT,
                    weight REAL,
                    src TEXT,
                    level TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (industry_code, con_code, trade_date, src)
                )
            ''')
            
            # 行业指数日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    turnover_rate REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 申万行业分类历史数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sw_industry_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    name TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_change REAL,
                    vol REAL,
                    amount REAL,
                    pe REAL,
                    pb REAL,
                    ps REAL,
                    pcf REAL,
                    market_cap REAL,
                    turnover_rate REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 行业资金流向表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS industry_money_flow (
                    ts_code TEXT,
                    trade_date TEXT,
                    buy_sm_amount REAL,
                    buy_md_amount REAL,
                    buy_lg_amount REAL,
                    buy_elg_amount REAL,
                    sell_sm_amount REAL,
                    sell_md_amount REAL,
                    sell_lg_amount REAL,
                    sell_elg_amount REAL,
                    net_mf_amount REAL,
                    net_mf_vol REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_code ON industry_stock(industry_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_con ON industry_stock(con_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_stock_date ON industry_stock(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_daily_code ON industry_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_daily_date ON industry_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sw_industry_code ON sw_industry_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sw_industry_date ON sw_industry_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_money_code ON industry_money_flow(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_industry_money_date ON industry_money_flow(trade_date)')
    
    def get_industry_stocks(self, industry_code, src='SW2021', level='L1', force_update=False):
        """获取行业成分股数据"""
        print(f"🔄 获取行业成分股: {industry_code} ({src} {level})")
        try:
            # 尝试不同的Tushare API方法
            new_data = None
            
            # 方法1: 使用hs_const接口获取成分股
            try:
                new_data = self.pro.hs_const(
                    hs_type=industry_code.replace('.SI', '')
                )
                if not new_data.empty:
                    print(f"   ✓ 通过hs_const获取到数据")
            except:
                pass
            
            # 方法2: 如果是申万行业，尝试sw_member接口
            if (new_data is None or new_data.empty) and 'SW' in src:
                try:
                    new_data = self.pro.sw_member(
                        index_code=industry_code
                    )
                    if not new_data.empty:
                        print(f"   ✓ 通过sw_member获取到数据")
                except:
                    pass
            
            if new_data is None or new_data.empty:
                print(f"⚠️ {industry_code} 无成分股数据")
                return pd.DataFrame()
            
            # 添加来源信息
            new_data['src'] = src
            new_data['level'] = level
            if 'trade_date' not in new_data.columns:
                new_data['trade_date'] = datetime.now().strftime('%Y%m%d')
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'industry_stock', self.stock_db)
            print(f"✅ {industry_code} 成分股数据已保存 ({len(new_data)} 条)")
            return new_data
            
        except Exception as e:
            print(f"❌ 获取 {industry_code} 成分股失败: {e}")
            return pd.DataFrame()
    
    def get_industry_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
        """获取行业指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('industry_daily', self.stock_db, ts_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {ts_code} 行业指数")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
        
        print(f"🔄 获取 {ts_code} 行业指数数据...")
        try:
            # 尝试使用不同的接口获取行业指数数据
            new_data = None
            
            # 首先尝试通过指数日线接口
            try:
                new_data = self.pro.index_daily(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date
                )
                if not new_data.empty:
                    print(f"   ✓ 通过index_daily获取到数据")
            except:
                pass
            
            # 如果失败，尝试申万行业指数接口
            if new_data is None or new_data.empty:
                try:
                    new_data = self.pro.sw_daily(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    if not new_data.empty:
                        print(f"   ✓ 通过sw_daily获取到数据")
                except:
                    pass
            
            if new_data is None or new_data.empty:
                print(f"⚠️ {ts_code} 无行业指数数据")
                if not force_update:
                    return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
                return pd.DataFrame()
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'industry_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('industry_daily', self.stock_db, ts_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 行业指数失败: {e}")
            if not force_update:
                return self.load_from_database('industry_daily', self.stock_db, ts_code=ts_code)
            return None
    
    def get_sw_industry_daily(self, index_code, start_date='20100101', end_date=None, force_update=False):
        """获取申万行业指数日线数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        # 检查是否需要增量更新
        if not force_update:
            existing_data = self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            
            if not existing_data.empty:
                latest_date = self.get_latest_date('sw_industry_daily', self.stock_db, index_code, 'trade_date')
                
                if latest_date:
                    latest_dt = pd.to_datetime(latest_date)
                    end_dt = pd.to_datetime(end_date)
                    
                    if latest_dt >= end_dt - timedelta(days=3):
                        print(f"📁 使用数据库缓存: {index_code} 申万行业")
                        return existing_data
                    else:
                        start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
        
        print(f"🔄 获取 {index_code} 申万行业指数数据...")
        try:
            new_data = self.pro.sw_daily(
                ts_code=index_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if new_data.empty:
                print(f"⚠️ {index_code} 无申万行业数据")
                if not force_update:
                    return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
                return new_data
            
            # 检查并添加缺失的字段到表结构
            self._ensure_sw_industry_columns(new_data.columns)
            
            # 保存新数据到数据库
            self.save_to_database(new_data, 'sw_industry_daily', self.stock_db)
            # 清理可能的重复数据
            self.delete_duplicate_records('sw_industry_daily', self.stock_db, index_code, 'trade_date')
            # 返回完整数据
            return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            
        except Exception as e:
            print(f"❌ 获取 {index_code} 申万行业失败: {e}")
            if not force_update:
                return self.load_from_database('sw_industry_daily', self.stock_db, ts_code=index_code)
            return None
    
    def _ensure_sw_industry_columns(self, api_columns):
        """确保sw_industry_daily表包含API返回的所有字段"""
        try:
            with sqlite3.connect(self.stock_db) as conn:
                # 获取当前表结构
                cursor = conn.execute("PRAGMA table_info(sw_industry_daily)")
                existing_columns = [row[1] for row in cursor.fetchall()]
                
                # 检查缺失的字段
                missing_columns = []
                for col in api_columns:
                    if col not in existing_columns and col != 'updated_at':
                        missing_columns.append(col)
                
                # 添加缺失的字段
                for col in missing_columns:
                    try:
                        # 根据字段名猜测数据类型
                        if col in ['ts_code', 'trade_date', 'name']:
                            col_type = 'TEXT'
                        else:
                            col_type = 'REAL'
                        
                        alter_sql = f"ALTER TABLE sw_industry_daily ADD COLUMN {col} {col_type}"
                        conn.execute(alter_sql)
                        print(f"✅ 添加字段: {col} ({col_type})")
                    except Exception as e:
                        print(f"⚠️ 添加字段 {col} 失败: {e}")
                
                if missing_columns:
                    conn.commit()
                    
        except Exception as e:
            print(f"⚠️ 检查表结构失败: {e}")
    
    def get_industry_money_flow(self, ts_code, start_date='20190101', end_date=None, force_update=False):
        """获取行业资金流向数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        print(f" 获取 {ts_code} 行业资金流向数据...")
        try:
            # 由于Tushare可能没有直接的行业资金流向接口，我们跳过这个功能
            print(f"⚠️ {ts_code} 行业资金流向功能暂不可用")
            return pd.DataFrame()
            
        except Exception as e:
            print(f"❌ 获取 {ts_code} 资金流向失败: {e}")
            return pd.DataFrame()
    
    def get_database_summary(self):
        """获取数据库统计信息"""
        print("📊 数据库统计信息")
        print("=" * 50)
        
        # 股票数据库统计
        print("📈 股票数据库:")
        if os.path.exists(self.stock_db):
            with sqlite3.connect(self.stock_db) as conn:
                try:
                    # 股票日线数据
                    stock_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily").fetchone()[0]
                    stock_records = conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()[0]
                    print(f"   股票日线: {stock_count} 只股票, {stock_records:,} 条记录")
                except:
                    print(f"   股票日线: 暂无数据")
                
                try:
                    # 估值数据
                    basic_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily_basic").fetchone()[0]
                    basic_records = conn.execute("SELECT COUNT(*) FROM stock_daily_basic").fetchone()[0]
                    print(f"   估值数据: {basic_count} 只股票, {basic_records:,} 条记录")
                except:
                    print(f"   估值数据: 暂无数据")
                
                try:
                    # 财务指标
                    fina_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_fina_indicator").fetchone()[0]
                    fina_records = conn.execute("SELECT COUNT(*) FROM stock_fina_indicator").fetchone()[0]
                    print(f"   财务指标: {fina_count} 只股票, {fina_records:,} 条记录")
                except:
                    print(f"   财务指标: 暂无数据")
                
                try:
                    # 指数数据
                    index_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM index_daily").fetchone()[0]
                    index_records = conn.execute("SELECT COUNT(*) FROM index_daily").fetchone()[0]
                    print(f"   指数日线: {index_count} 个指数, {index_records:,} 条记录")
                except:
                    print(f"   指数日线: 暂无数据")
        else:
            print("   数据库文件不存在")
        
        # 基金数据库统计
        print("\n📊 基金数据库:")
        if os.path.exists(self.fund_db):
            with sqlite3.connect(self.fund_db) as conn:
                try:
                    # ETF数据
                    etf_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM etf_daily").fetchone()[0]
                    etf_records = conn.execute("SELECT COUNT(*) FROM etf_daily").fetchone()[0]
                    print(f"   ETF日线: {etf_count} 只ETF, {etf_records:,} 条记录")
                except:
                    print(f"   ETF日线: 暂无数据")
                
                try:
                    # 基金净值
                    nav_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM fund_nav").fetchone()[0]
                    nav_records = conn.execute("SELECT COUNT(*) FROM fund_nav").fetchone()[0]
                    print(f"   基金净值: {nav_count} 只基金, {nav_records:,} 条记录")
                except:
                    print(f"   基金净值: 暂无数据")
        else:
            print("   数据库文件不存在")
        
        # 基础信息统计
        print("\n📋 基础信息(CSV):")
        basic_files = ['stock_basic.csv', 'fund_basic.csv', 'industry_classify.csv']
        for file in basic_files:
            file_path = os.path.join(self.basic_dir, file)
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path)
                    print(f"   {file}: {len(df)} 条记录")
                except:
                    print(f"   {file}: 读取失败")
            else:
                print(f"   {file}: 文件不存在")
    
    def get_fund_storage_info(self):
        """获取基金存储详情"""
        print("📊 基金数据存储详情")
        print("=" * 40)
        
        if os.path.exists(self.fund_db):
            with sqlite3.connect(self.fund_db) as conn:
                try:
                    # ETF详情
                    etf_info = conn.execute("""
                        SELECT 
                            COUNT(DISTINCT ts_code) as etf_count,
                            MIN(trade_date) as min_date,
                            MAX(trade_date) as max_date,
                            COUNT(*) as total_records
                        FROM etf_daily
                    """).fetchone()
                    
                    print(f"📈 ETF数据:")
                    print(f"   ETF数量: {etf_info[0]}")
                    print(f"   数据时间: {etf_info[1]} ~ {etf_info[2]}")
                    print(f"   总记录数: {etf_info[3]:,}")
                except:
                    print(f"📈 ETF数据: 暂无数据")
                
                try:
                    # 基金净值详情
                    nav_info = conn.execute("""
                        SELECT 
                            COUNT(DISTINCT ts_code) as fund_count,
                            MIN(nav_date) as min_date,
                            MAX(nav_date) as max_date,
                            COUNT(*) as total_records
                        FROM fund_nav
                    """).fetchone()
                    
                    print(f"\n📊 基金净值:")
                    print(f"   基金数量: {nav_info[0]}")
                    print(f"   数据时间: {nav_info[1]} ~ {nav_info[2]}")
                    print(f"   总记录数: {nav_info[3]:,}")
                except:
                    print(f"\n📊 基金净值: 暂无数据")
        else:
            print("❌ 基金数据库不存在")
    
    def preview_fund_classification(self):
        """预览基金分类"""
        print("📊 基金分类预览")
        print("=" * 30)
        
        fund_basic = self.get_fund_basic()
        if fund_basic is None or fund_basic.empty:
            print("❌ 无法获取基金基本信息")
            return
        
        # 按市场分类
        print("📈 按市场分类:")
        market_count = fund_basic['market'].value_counts()
        for market, count in market_count.items():
            market_name = {'E': '场外基金', 'O': '场内基金'}.get(market, f'未知({market})')
            print(f"   {market_name}: {count} 只")
        
        # 按基金类型分类
        print("\n📊 按类型分类:")
        type_count = fund_basic['fund_type'].value_counts().head(10)
        for fund_type, count in type_count.items():
            print(f"   {fund_type}: {count} 只")
        
        # ETF统计
        etf_funds = fund_basic[
            (fund_basic['market'] == 'O') & 
            (fund_basic['ts_code'].str.endswith('.SH') | fund_basic['ts_code'].str.endswith('.SZ'))
        ]
        print(f"\n📈 ETF基金: {len(etf_funds)} 只")
        
        # 场外基金统计
        nav_funds = fund_basic[
            (fund_basic['market'] == 'E') | 
            (fund_basic['ts_code'].str.endswith('.OF'))
        ]
        print(f"📊 场外基金: {len(nav_funds)} 只")
    
    def clean_cache(self):
        """清理过期缓存"""
        print("🧹 清理过期缓存")
        print("=" * 30)
        
        cleaned_count = 0
        
        # 清理股票数据库中的重复记录
        if os.path.exists(self.stock_db):
            print("🔄 清理股票数据库重复记录...")
            with sqlite3.connect(self.stock_db) as conn:
                tables = ['stock_daily', 'stock_daily_basic', 'index_daily']
                for table in tables:
                    try:
                        before_count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                        # 这里可以添加具体的清理逻辑
                        after_count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                        removed = before_count - after_count
                        if removed > 0:
                            print(f"   {table}: 清理 {removed} 条重复记录")
                            cleaned_count += removed
                    except:
                        pass
        
        # 清理基金数据库中的重复记录
        if os.path.exists(self.fund_db):
            print("🔄 清理基金数据库重复记录...")
            with sqlite3.connect(self.fund_db) as conn:
                tables = ['fund_nav', 'etf_daily']
                for table in tables:
                    try:
                        before_count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                        # 这里可以添加具体的清理逻辑
                        after_count = conn.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                        removed = before_count - after_count
                        if removed > 0:
                            print(f"   {table}: 清理 {removed} 条重复记录")
                            cleaned_count += removed
                    except:
                        pass
        
        if cleaned_count > 0:
            print(f"✅ 缓存清理完成，共清理 {cleaned_count} 条记录")
        else:
            print("✅ 缓存无需清理")

# ==================== 主入口 ====================

def main():
    """混合存储版主入口，支持SQLite数据库和CSV基础信息"""
    print("🗄️ 本地数据缓存系统 (混合存储版)")
    print("=" * 50)
    cache = DataCache()
    
    print("\n请选择操作模式:")
    print("1. 缓存/更新所有股票数据")
    print("2. 缓存/更新所有基金数据")
    print("3. 缓存/更新股票基本面数据")
    print("4. 缓存市场估值数据(近30天)")
    print("5. 缓存/更新扩展数据(复权、分红、解禁、股东户数)")
    print("6. 缓存/更新指数数据(指数日线、成分股权重)")
    print("7. 缓存/更新分红送转数据(dividend接口)")
    print("8. 缓存/更新行业板块数据(成分股、指数、资金流向)")
    print("9. 缓存主要行业指数数据")
    print("10. 显示数据库统计")
    print("11. 显示基金存储详情")
    print("12. 显示行业板块统计")
    print("13. 预览基金分类")
    print("14. 清理过期缓存")
    
    choice = input("\n请输入选择(1-14): ").strip()
    
    if choice == "1":
        cache.cache_all_stocks_data()
    elif choice == "2":
        cache.cache_all_funds_data()
    elif choice == "3":
        cache.cache_all_fundamental_data()
    elif choice == "4":
        cache.cache_all_daily_basic()
    elif choice == "5":
        cache.cache_all_extended_data()
    elif choice == "6":
        cache.cache_all_index_data()
    elif choice == "7":
        cache.cache_all_comprehensive_dividend()
    elif choice == "8":
        cache.cache_all_industry_data()
    elif choice == "9":
        cache.cache_main_industry_indices()
    elif choice == "10":
        cache.get_database_summary()
    elif choice == "11":
        cache.get_fund_storage_info()
    elif choice == "12":
        cache.get_industry_summary()
    elif choice == "13":
        cache.preview_fund_classification()
    elif choice == "14":
        cache.clean_cache()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()

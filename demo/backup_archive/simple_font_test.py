#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单中文字体测试
Simple Chinese Font Test
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 直接设置中文字体
plt.rcParams['font.sans-serif'] = [
    'Arial Unicode MS',     # Mac通用中文字体
    'PingFang SC',          # Mac系统字体
    'Hiragino Sans GB',     # Mac中文字体
    'Microsoft YaHei',      # Windows中文字体
    'SimHei',               # Windows黑体
    'sans-serif'            # 备用字体
]
plt.rcParams['axes.unicode_minus'] = False

def test_chinese_display():
    """测试中文显示"""
    print("🧪 测试中文字体显示...")
    
    # 创建测试图
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # 测试各种中文文本
    test_texts = [
        "股票代码: 000001.SZ",
        "股票名称: 平安银行", 
        "当前价格: ¥12.34",
        "涨跌幅: +2.56%",
        "成交量: 1,234,567手",
        "技术指标分析:",
        "  • MACD指标",
        "  • RSI相对强弱指标", 
        "  • 布林带",
        "  • 移动平均线 MA5/MA10/MA20",
        "",
        "行业分析:",
        "  • 银行业",
        "  • 食品饮料",
        "  • 医药生物",
        "  • 电子科技"
    ]
    
    # 绘制文本
    for i, text in enumerate(test_texts):
        y_pos = 0.95 - i * 0.05
        if text.startswith("  •"):
            ax.text(0.1, y_pos, text, fontsize=12, transform=ax.transAxes, color='blue')
        elif text.endswith(":"):
            ax.text(0.05, y_pos, text, fontsize=14, fontweight='bold', transform=ax.transAxes)
        elif text == "":
            continue
        else:
            ax.text(0.05, y_pos, text, fontsize=13, transform=ax.transAxes)
    
    ax.set_title('中文字体显示测试', fontsize=18, fontweight='bold', pad=20)
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # 显示当前字体信息
    current_font = plt.rcParams['font.sans-serif'][0]
    ax.text(0.05, 0.02, f"当前字体: {current_font}", fontsize=10, 
           color='gray', transform=ax.transAxes)
    
    # 保存图片
    filename = "simple_chinese_test.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    
    print(f"✅ 测试图片已保存: {filename}")
    print(f"📝 当前使用字体: {current_font}")

def check_available_fonts():
    """检查可用字体"""
    print("\n🔍 检查系统可用的中文字体...")
    
    # 获取所有字体
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 检查我们设置的字体
    target_fonts = [
        'Arial Unicode MS',
        'PingFang SC', 
        'Hiragino Sans GB',
        'Microsoft YaHei',
        'SimHei'
    ]
    
    print("📋 字体检查结果:")
    for font in target_fonts:
        status = "✅ 可用" if font in all_fonts else "❌ 不可用"
        print(f"  {font}: {status}")
    
    # 找到第一个可用的字体
    available_font = None
    for font in target_fonts:
        if font in all_fonts:
            available_font = font
            break
    
    if available_font:
        print(f"\n🎯 推荐使用: {available_font}")
    else:
        print("\n⚠️ 未找到推荐的中文字体")

def main():
    """主函数"""
    print("🔤 简单中文字体测试")
    print("="*40)
    
    # 检查可用字体
    check_available_fonts()
    
    # 测试中文显示
    test_chinese_display()
    
    print("\n" + "="*40)
    print("✅ 测试完成")
    print("📁 请查看 simple_chinese_test.png 确认中文显示效果")

if __name__ == "__main__":
    main()

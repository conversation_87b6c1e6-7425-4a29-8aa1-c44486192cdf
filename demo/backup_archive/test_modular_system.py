#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块化系统测试脚本
Test Script for Modular System
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        print("  测试配置模块...")
        from data_manager.config import config
        print(f"  ✅ 配置模块导入成功: {config.base_dir}")
        
        print("  测试API客户端...")
        from data_manager.api_client import TushareClient
        print("  ✅ API客户端导入成功")
        
        print("  测试数据库管理器...")
        from data_manager.database_manager import DatabaseManager
        print("  ✅ 数据库管理器导入成功")
        
        print("  测试基础数据管理器...")
        from data_manager.basic_data_manager import BasicDataManager
        print("  ✅ 基础数据管理器导入成功")
        
        print("  测试更新管理器...")
        from data_manager.update_manager import UpdateManager
        print("  ✅ 更新管理器导入成功")
        
        print("  测试统一接口...")
        from data_manager import DataCache
        print("  ✅ 统一接口导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from data_manager import DataCache
        
        print("  创建DataCache实例...")
        cache = DataCache()
        print("  ✅ DataCache实例创建成功")
        
        print("  测试数据统计...")
        stats = cache.get_data_statistics()
        print(f"  ✅ 数据统计获取成功: {stats}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 基本功能测试失败: {e}")
        return False

def test_update_manager():
    """测试更新管理器"""
    print("\n🧪 测试更新管理器...")
    
    try:
        from data_manager import UpdateManager
        
        print("  创建UpdateManager实例...")
        update_manager = UpdateManager()
        print("  ✅ UpdateManager实例创建成功")
        
        print("  测试统计功能...")
        stats = update_manager.show_summary()
        print("  ✅ 统计功能测试成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 更新管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 模块化系统测试")
    print("="*50)
    
    success_count = 0
    total_tests = 3
    
    # 测试模块导入
    if test_imports():
        success_count += 1
    
    # 测试基本功能
    if test_basic_functionality():
        success_count += 1
    
    # 测试更新管理器
    if test_update_manager():
        success_count += 1
    
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过！模块化系统工作正常")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        sys.exit(1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac中文字体配置模块
Mac Chinese Font Configuration Module

专门为Mac系统优化的中文字体配置
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

def setup_mac_chinese_fonts():
    """为Mac系统设置最佳中文字体"""
    
    if platform.system() != 'Darwin':
        print("⚠️ 此配置专为Mac系统设计")
        return False
    
    # Mac系统中文字体优先级（按推荐程度排序）
    mac_font_priority = [
        'PingFang SC',          # macOS Monterey+ 默认中文字体
        'PingFang HK',          # 繁体中文版本
        'Hiragino Sans GB',     # 经典苹果中文字体，清晰易读
        'Hiragino Sans',        # 日文字体，也支持中文
        'STHeiti',              # 华文黑体，兼容性好
        'Heiti TC',             # 繁体中文黑体
        'Kaiti SC',             # 楷体，适合标题
        'Arial Unicode MS',     # 支持中文的Arial
        'Apple LiGothic',       # 苹果丽黑
        'Apple LiSung',         # 苹果丽宋
    ]
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 找到最佳可用字体
    selected_font = None
    for font in mac_font_priority:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        # 设置matplotlib字体
        plt.rcParams['font.sans-serif'] = [selected_font] + mac_font_priority
        plt.rcParams['axes.unicode_minus'] = False
        
        print(f"✅ Mac中文字体已设置: {selected_font}")
        return selected_font
    else:
        print("❌ 未找到合适的Mac中文字体")
        return None

def get_font_info():
    """获取当前字体信息"""
    current_font = plt.rcParams['font.sans-serif'][0]
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    info = {
        'current_font': current_font,
        'is_available': current_font in available_fonts,
        'system': platform.system(),
        'total_fonts': len(available_fonts)
    }
    
    return info

def test_chinese_display():
    """测试中文显示效果"""
    import matplotlib.pyplot as plt
    
    # 创建测试图
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试文本
    test_texts = [
        '股票代码: 000001.SZ',
        '股票名称: 平安银行',
        '当前价格: ¥12.34',
        '涨跌幅: +2.56%',
        '成交量: 1,234,567手',
        '技术指标: MACD、RSI、布林带',
        '移动平均线: MA5、MA10、MA20、MA60'
    ]
    
    # 绘制测试文本
    for i, text in enumerate(test_texts):
        ax.text(0.05, 0.9 - i * 0.12, text, fontsize=14, 
               transform=ax.transAxes, fontweight='normal')
    
    # 设置标题
    ax.set_title('Mac中文字体显示测试', fontsize=18, fontweight='bold', pad=20)
    
    # 添加字体信息
    font_info = get_font_info()
    info_text = f"当前字体: {font_info['current_font']}\n系统: {font_info['system']}"
    ax.text(0.05, 0.05, info_text, fontsize=10, color='gray', 
           transform=ax.transAxes)
    
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    
    # 保存测试图
    filename = "mac_chinese_font_test.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    
    print(f"✅ 中文字体测试图已保存: {filename}")
    return filename

def create_font_sample():
    """创建字体样本图"""
    import matplotlib.pyplot as plt
    
    # 获取可用的Mac中文字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    mac_fonts = ['PingFang SC', 'PingFang HK', 'Hiragino Sans GB', 
                 'STHeiti', 'Heiti TC', 'Kaiti SC', 'Arial Unicode MS']
    
    usable_fonts = [font for font in mac_fonts if font in available_fonts]
    
    if not usable_fonts:
        print("❌ 没有找到Mac中文字体")
        return None
    
    # 创建样本图
    fig, axes = plt.subplots(len(usable_fonts), 1, 
                            figsize=(12, 2.5 * len(usable_fonts)))
    if len(usable_fonts) == 1:
        axes = [axes]
    
    sample_text = "股票技术分析 - 贵州茅台(600519.SH) K线图 价格¥1,234.56 涨幅+5.67%"
    
    for i, font in enumerate(usable_fonts):
        ax = axes[i]
        
        # 主要文本
        ax.text(0.05, 0.6, sample_text, fontsize=16, 
               fontfamily=font, transform=ax.transAxes)
        
        # 字体名称
        ax.text(0.05, 0.3, f"字体: {font}", fontsize=12, 
               color='#666666', transform=ax.transAxes)
        
        # 字体特点
        font_features = {
            'PingFang SC': '现代简洁，macOS默认',
            'PingFang HK': '繁体中文版本',
            'Hiragino Sans GB': '经典清晰，易读性好',
            'STHeiti': '华文黑体，兼容性强',
            'Heiti TC': '繁体中文黑体',
            'Kaiti SC': '楷体风格，适合标题',
            'Arial Unicode MS': '西文字体，支持中文'
        }
        
        feature = font_features.get(font, '支持中文显示')
        ax.text(0.05, 0.1, f"特点: {feature}", fontsize=10, 
               color='#999999', transform=ax.transAxes)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 添加分隔线
        if i < len(usable_fonts) - 1:
            ax.axhline(y=0, color='lightgray', linewidth=0.5)
    
    plt.suptitle('Mac中文字体样本对比', fontsize=20, fontweight='bold', y=0.98)
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    
    filename = "mac_font_samples.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    
    print(f"✅ Mac字体样本图已保存: {filename}")
    return filename

def main():
    """主函数 - 设置Mac中文字体"""
    print("🍎 Mac中文字体配置工具")
    print("="*40)
    
    # 1. 设置字体
    selected_font = setup_mac_chinese_fonts()
    
    if selected_font:
        # 2. 显示字体信息
        font_info = get_font_info()
        print(f"📊 字体信息:")
        print(f"   当前字体: {font_info['current_font']}")
        print(f"   系统类型: {font_info['system']}")
        print(f"   总字体数: {font_info['total_fonts']}")
        
        # 3. 创建测试图
        test_chinese_display()
        
        # 4. 创建样本图
        create_font_sample()
        
        print("\n✅ Mac中文字体配置完成！")
        print("📁 请查看生成的图片确认显示效果")
        
    else:
        print("\n❌ Mac中文字体配置失败")
        print("💡 建议:")
        print("1. 检查系统是否为macOS")
        print("2. 确认已安装中文字体")
        print("3. 重启Python程序")

if __name__ == "__main__":
    main()

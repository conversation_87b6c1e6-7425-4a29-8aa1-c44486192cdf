# -*- coding: utf-8 -*-
"""
使用本地缓存数据进行分析
Using Local Cached Data for Analysis
"""

import pandas as pd
import numpy as np
from data_cache_manager import DataCache
import matplotlib.pyplot as plt
import warnings
import sys
import io
import os
import sqlite3
from datetime import datetime, timedelta

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置标准输出编码为UTF-8
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

warnings.filterwarnings('ignore')

def analyze_cached_data():
    """混合存储版数据分析工具"""
    print("📊 缓存数据分析工具 (混合存储版 v2.0)")
    print("=" * 60)
    
    # 初始化缓存管理器
    cache = DataCache()
    
    # 扩展菜单选择
    print("\n请选择分析模式:")
    print("1. 股票数据分析")
    print("2. 基金数据分析") 
    print("3. 股票基本面数据分析")
    print("4. 扩展数据分析")
    print("5. 指数数据分析")
    print("6. 市场概览与统计")
    print("7. 数据质量检查")
    print("8. 数据库详细统计")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-8): ").strip()
    
    if choice == "1":
        analyze_stock_data(cache)
    elif choice == "2":
        analyze_fund_data(cache)
    elif choice == "3":
        analyze_fundamentals(cache)
    elif choice == "4":
        analyze_extended_data(cache)
    elif choice == "5":
        analyze_index_data(cache)
    elif choice == "6":
        market_overview(cache)
    elif choice == "7":
        data_quality_check(cache)
    elif choice == "8":
        cache.get_database_summary()
    elif choice == "0":
        print("👋 再见！")
        return
    else:
        print("❌ 无效选择")
        
    # 提供继续分析选项
    if input("\n是否继续分析其他数据? (y/n): ").strip().lower() == 'y':
        analyze_cached_data()


def analyze_stock_data(cache):
    """增强版股票数据分析"""
    print("\n📈 股票数据分析")
    print("=" * 50)
    
    # 1. 股票基本信息分析
    stock_basic = cache.get_stock_basic()
    if stock_basic is not None and not stock_basic.empty:
        print(f"📊 总股票数量: {len(stock_basic):,}")
        
        # 市场分布
        if 'market' in stock_basic.columns:
            market_stats = stock_basic['market'].value_counts()
            print("\n💼 市场分布:")
            for market, count in market_stats.items():
                market_name = {"主板": "主板", "创业板": "创业板", "科创板": "科创板", "北交所": "北交所"}.get(market, market)
                print(f"  {market_name}: {count:,}只")
        
        # 行业分布TOP10
        if 'industry' in stock_basic.columns:
            industry_count = stock_basic['industry'].value_counts().head(10)
            print("\n🏭 行业分布TOP10:")
            for industry, count in industry_count.items():
                print(f"  {industry}: {count:,}只")
        
        # 地区分布TOP10
        if 'area' in stock_basic.columns:
            area_count = stock_basic['area'].value_counts().head(10)
            print("\n🌍 地区分布TOP10:")
            for area, count in area_count.items():
                print(f"  {area}: {count:,}只")
    else:
        print("❌ 股票基本信息数据缺失")
    
    # 2. 股票价格数据统计
    print_stock_price_stats(cache)
    
    # 3. 数据覆盖度检查
    check_stock_coverage(cache)


def print_stock_price_stats(cache):
    """股票价格数据统计"""
    if os.path.exists(cache.stock_db):
        db_size = os.path.getsize(cache.stock_db) / 1024 / 1024
        print(f"\n💾 股票数据库大小: {db_size:.2f} MB")
        
        with sqlite3.connect(cache.stock_db) as conn:
            try:
                # 基础统计
                stock_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily").fetchone()[0]
                record_count = conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()[0]
                print(f"📊 数据库中股票数量: {stock_count:,} 只")
                print(f"� 总价格记录数: {record_count:,} 条")
                
                # 日期范围
                date_range = conn.execute("""
                    SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date 
                    FROM stock_daily
                """).fetchone()
                if date_range[0]:
                    print(f"� 数据时间范围: {date_range[0]} ~ {date_range[1]}")
                
                # 最近更新的股票
                recent_updates = conn.execute("""
                    SELECT ts_code, MAX(trade_date) as last_date
                    FROM stock_daily 
                    GROUP BY ts_code 
                    ORDER BY last_date DESC 
                    LIMIT 5
                """).fetchall()
                
                if recent_updates:
                    print("\n📆 最近更新的股票(TOP5):")
                    for ts_code, last_date in recent_updates:
                        print(f"  {ts_code}: {last_date}")
                        
            except Exception as e:
                print(f"❌ 股票价格数据统计失败: {e}")
    else:
        print("\n💾 股票数据库文件不存在")


def check_stock_coverage(cache):
    """检查股票数据覆盖度"""
    print("\n🔍 数据覆盖度检查:")
    
    # 抽样检查主要指数成分股
    sample_stocks = {
        '000001.SZ': '平安银行',
        '000002.SZ': '万科A', 
        '600000.SH': '浦发银行',
        '600036.SH': '招商银行',
        '000858.SZ': '五粮液',
        '600519.SH': '贵州茅台'
    }
    
    for ts_code, name in sample_stocks.items():
        try:
            data = cache.get_stock_daily(ts_code)
            if data is not None and not data.empty:
                latest_date = data['trade_date'].max()
                total_records = len(data)
                print(f"  ✅ {ts_code}({name}): {total_records:,}条数据, 最新:{latest_date}")
            else:
                print(f"  ❌ {ts_code}({name}): 无数据")
        except Exception as e:
            print(f"  ❌ {ts_code}({name}): 错误 - {e}")


def analyze_fund_data(cache):
    """增强版基金数据分析"""
    print("\n🏦 基金数据分析")
    print("=" * 50)
    
    # 1. 基金基本信息分析
    fund_basic = cache.get_fund_basic()
    if fund_basic is not None and not fund_basic.empty:
        print(f"📊 总基金数量: {len(fund_basic):,}")
        
        # 市场分类统计
        if 'market' in fund_basic.columns:
            market_stats = fund_basic['market'].value_counts()
            print("\n💼 市场分类:")
            for market, count in market_stats.items():
                market_name = "场内基金(ETF)" if market == 'O' else "场外基金"
                print(f"  {market_name}: {count:,} 只")
        
        # 基金类型分布
        if 'fund_type' in fund_basic.columns:
            type_stats = fund_basic['fund_type'].value_counts().head(10)
            print("\n🔖 基金类型TOP10:")
            for fund_type, count in type_stats.items():
                print(f"  {fund_type}: {count:,}只")
        
        # 基金公司分布
        if 'management' in fund_basic.columns:
            mgmt_stats = fund_basic['management'].value_counts().head(10)
            print("\n🏢 基金公司TOP10:")
            for mgmt, count in mgmt_stats.items():
                print(f"  {mgmt}: {count:,}只")
    else:
        print("❌ 基金基本信息数据缺失")
    
    # 2. 基金价格数据统计
    print_fund_price_stats(cache)
    
    # 3. 基金分类存储详情
    cache.get_fund_storage_info()


def print_fund_price_stats(cache):
    """基金价格数据统计"""
    if os.path.exists(cache.fund_db):
        db_size = os.path.getsize(cache.fund_db) / 1024 / 1024
        print(f"\n💾 基金数据库大小: {db_size:.2f} MB")
        
        with sqlite3.connect(cache.fund_db) as conn:
            # ETF数据统计
            try:
                etf_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM etf_daily").fetchone()[0]
                etf_records = conn.execute("SELECT COUNT(*) FROM etf_daily").fetchone()[0]
                print(f"\n📊 ETF日度数据: {etf_count:,} 只 ({etf_records:,} 条记录)")
                
                # ETF日期范围
                etf_range = conn.execute("""
                    SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date 
                    FROM etf_daily
                """).fetchone()
                if etf_range[0]:
                    print(f"📅 ETF数据时间范围: {etf_range[0]} ~ {etf_range[1]}")
                    
            except:
                print("\n📊 数据库中暂无ETF数据")
            
            # 基金净值统计
            try:
                nav_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM fund_nav").fetchone()[0]
                nav_records = conn.execute("SELECT COUNT(*) FROM fund_nav").fetchone()[0]
                print(f"\n📊 基金净值数据: {nav_count:,} 只 ({nav_records:,} 条记录)")
                
                # 净值日期范围
                nav_range = conn.execute("""
                    SELECT MIN(nav_date) as min_date, MAX(nav_date) as max_date 
                    FROM fund_nav
                """).fetchone()
                if nav_range[0]:
                    print(f"📅 净值数据时间范围: {nav_range[0]} ~ {nav_range[1]}")
                    
                # 最近更新的基金
                recent_nav = conn.execute("""
                    SELECT ts_code, MAX(nav_date) as last_date
                    FROM fund_nav 
                    GROUP BY ts_code 
                    ORDER BY last_date DESC 
                    LIMIT 5
                """).fetchall()
                
                if recent_nav:
                    print("\n📆 最近更新的基金净值(TOP5):")
                    for ts_code, last_date in recent_nav:
                        print(f"  {ts_code}: {last_date}")
                        
            except:
                print("\n📊 数据库中暂无基金净值数据")
    else:
        print("\n💾 基金数据库文件不存在")
    
    # 3. 抽样数据质量检查
    print("\n🔍 数据质量抽查:")
    
    # ETF抽查
    sample_etfs = [
        ('510300.SH', '沪深300ETF'),
        ('159919.SZ', '沪深300ETF'),
        ('512100.SH', '中证1000ETF'),
        ('159995.SZ', '芯片ETF'),
        ('515050.SH', '5GETF')
    ]
    
    print("  ETF数据:")
    for ts_code, name in sample_etfs:
        try:
            data = cache.get_etf_daily(ts_code)
            if data is not None and not data.empty:
                latest_date = data['trade_date'].max()
                print(f"    ✅ {ts_code}({name}): {len(data):,}条, 最新:{latest_date}")
            else:
                print(f"    ❌ {ts_code}({name}): 无数据")
        except Exception as e:
            print(f"    ❌ {ts_code}({name}): 错误 - {e}")
    
    # 基金净值抽查
    sample_funds = [
        ('000001.OF', '华夏成长'),
        ('110022.OF', '易方达消费'),
        ('161725.OF', '招商中证白酒'),
        ('001594.OF', '天弘创业板'),
        ('270050.OF', '广发沪深300')
    ]
    
    print("\n  基金净值数据:")
    for ts_code, name in sample_funds:
        try:
            data = cache.get_fund_nav(ts_code)
            if data is not None and not data.empty:
                latest_date = data['nav_date'].max()
                print(f"    ✅ {ts_code}({name}): {len(data):,}条, 最新:{latest_date}")
            else:
                print(f"    ❌ {ts_code}({name}): 无数据")
        except Exception as e:
            print(f"    ❌ {ts_code}({name}): 错误 - {e}")


def analyze_fundamentals(cache):
    """股票基本面数据分析"""
    print("\n💼 股票基本面数据分析")
    print("=" * 50)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        # 1. 财务指标数据统计
        print("📊 财务指标数据:")
        try:
            fina_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(end_date) as min_date,
                    MAX(end_date) as max_date,
                    COUNT(DISTINCT end_date) as period_count
                FROM stock_fina_indicator
            """).fetchone()
            
            if fina_stats[0] > 0:
                print(f"  📈 股票数量: {fina_stats[0]:,} 只")
                print(f"  📊 记录总数: {fina_stats[1]:,} 条")
                print(f"  📅 时间范围: {fina_stats[2]} ~ {fina_stats[3]}")
                print(f"  🗓️ 报告期数: {fina_stats[4]:,} 个")
                
                # 最新财务指标的股票
                recent_fina = conn.execute("""
                    SELECT ts_code, MAX(end_date) as latest_date
                    FROM stock_fina_indicator 
                    GROUP BY ts_code 
                    ORDER BY latest_date DESC 
                    LIMIT 5
                """).fetchall()
                
                if recent_fina:
                    print("  📆 最新财务指标(TOP5):")
                    for ts_code, latest_date in recent_fina:
                        print(f"    {ts_code}: {latest_date}")
            else:
                print("  ❌ 无财务指标数据")
        except Exception as e:
            print(f"  ❌ 财务指标统计失败: {e}")
        
        # 2. 每日估值数据统计
        print("\n📊 每日估值数据:")
        try:
            basic_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM stock_daily_basic
            """).fetchone()
            
            if basic_stats[0] > 0:
                print(f"  📈 股票数量: {basic_stats[0]:,} 只")
                print(f"  📊 记录总数: {basic_stats[1]:,} 条")
                print(f"  📅 时间范围: {basic_stats[2]} ~ {basic_stats[3]}")
                
                # 最新估值数据
                recent_basic = conn.execute("""
                    SELECT trade_date, COUNT(DISTINCT ts_code) as count
                    FROM stock_daily_basic 
                    GROUP BY trade_date 
                    ORDER BY trade_date DESC 
                    LIMIT 5
                """).fetchall()
                
                if recent_basic:
                    print("  📆 最新估值数据:")
                    for trade_date, count in recent_basic:
                        print(f"    {trade_date}: {count:,}只股票")
            else:
                print("  ❌ 无估值数据")
        except Exception as e:
            print(f"  ❌ 估值数据统计失败: {e}")
        
        # 3. 业绩预告数据统计
        print("\n📊 业绩预告数据:")
        try:
            forecast_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(end_date) as min_date,
                    MAX(end_date) as max_date
                FROM stock_forecast
            """).fetchone()
            
            if forecast_stats[0] > 0:
                print(f"  📈 股票数量: {forecast_stats[0]:,} 只")
                print(f"  📊 记录总数: {forecast_stats[1]:,} 条")
                print(f"  📅 时间范围: {forecast_stats[2]} ~ {forecast_stats[3]}")
                
                # 业绩预告类型分布
                forecast_types = conn.execute("""
                    SELECT type, COUNT(*) as count
                    FROM stock_forecast 
                    WHERE type IS NOT NULL
                    GROUP BY type 
                    ORDER BY count DESC
                """).fetchall()
                
                if forecast_types:
                    print("  📋 预告类型分布:")
                    for type_name, count in forecast_types:
                        print(f"    {type_name}: {count:,}条")
            else:
                print("  ❌ 无业绩预告数据")
        except Exception as e:
            print(f"  ❌ 业绩预告统计失败: {e}")
    
    # 4. 基本面数据抽样检查
    check_fundamentals_sample(cache)


def check_fundamentals_sample(cache):
    """基本面数据抽样检查"""
    print("\n🔍 基本面数据抽样检查:")
    
    sample_stocks = [
        ('000001.SZ', '平安银行'),
        ('600036.SH', '招商银行'),
        ('000858.SZ', '五粮液'),
        ('600519.SH', '贵州茅台'),
        ('000002.SZ', '万科A')
    ]
    
    for ts_code, name in sample_stocks:
        print(f"\n  📊 {ts_code}({name}):")
        
        try:
            # 检查财务指标
            fina_data = cache.get_stock_fina_indicator(ts_code)
            if fina_data is not None and not fina_data.empty:
                latest_fina = fina_data['end_date'].max()
                fina_count = len(fina_data)
                print(f"    ✅ 财务指标: {fina_count}条, 最新:{latest_fina}")
            else:
                print(f"    ❌ 财务指标: 无数据")
        except Exception as e:
            print(f"    ❌ 财务指标: 错误 - {e}")
        
        try:
            # 检查估值数据(近30天)
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            basic_data = cache.get_stock_daily_basic(ts_code=ts_code, start_date=start_date, end_date=end_date)
            if basic_data is not None and not basic_data.empty:
                latest_basic = basic_data['trade_date'].max()
                basic_count = len(basic_data)
                print(f"    ✅ 估值数据(近30天): {basic_count}条, 最新:{latest_basic}")
            else:
                print(f"    ❌ 估值数据: 无数据")
        except Exception as e:
            print(f"    ❌ 估值数据: 错误 - {e}")
        
        try:
            # 检查业绩预告
            forecast_data = cache.get_stock_forecast(ts_code)
            if forecast_data is not None and not forecast_data.empty:
                latest_forecast = forecast_data['end_date'].max()
                forecast_count = len(forecast_data)
                print(f"    ✅ 业绩预告: {forecast_count}条, 最新:{latest_forecast}")
            else:
                print(f"    ❌ 业绩预告: 无数据")
        except Exception as e:
            print(f"    ❌ 业绩预告: 错误 - {e}")


def market_overview(cache):
    """市场概览与统计"""
    print("\n🌐 市场概览与统计")
    print("=" * 50)
    
    # 1. 整体数据概览
    print("📊 整体数据概览:")
    
    # 股票基本信息
    stock_basic = cache.get_stock_basic()
    if stock_basic is not None and not stock_basic.empty:
        print(f"  📈 A股总数: {len(stock_basic):,} 只")
    
    # 基金基本信息
    fund_basic = cache.get_fund_basic()
    if fund_basic is not None and not fund_basic.empty:
        print(f"  🏦 基金总数: {len(fund_basic):,} 只")
    
    # 2. 数据库文件统计
    print("\n💾 数据库文件统计:")
    
    total_size = 0
    if os.path.exists(cache.stock_db):
        stock_size = os.path.getsize(cache.stock_db) / 1024 / 1024
        total_size += stock_size
        print(f"  📈 股票数据库: {stock_size:.2f} MB")
    
    if os.path.exists(cache.fund_db):
        fund_size = os.path.getsize(cache.fund_db) / 1024 / 1024
        total_size += fund_size
        print(f"  🏦 基金数据库: {fund_size:.2f} MB")
    
    # 统计CSV文件
    basic_dir = os.path.join(cache.cache_dir, 'basic')
    if os.path.exists(basic_dir):
        csv_size = sum(os.path.getsize(os.path.join(basic_dir, f)) 
                      for f in os.listdir(basic_dir) if f.endswith('.csv')) / 1024 / 1024
        total_size += csv_size
        print(f"  📄 基础数据(CSV): {csv_size:.2f} MB")
    
    print(f"  💾 总数据大小: {total_size:.2f} MB")
    
    # 3. 数据时效性检查
    print("\n⏰ 数据时效性检查:")
    check_data_freshness(cache)


def check_data_freshness(cache):
    """检查数据时效性"""
    current_date = datetime.now().strftime('%Y%m%d')
    
    # 检查股票数据
    if os.path.exists(cache.stock_db):
        with sqlite3.connect(cache.stock_db) as conn:
            try:
                latest_stock = conn.execute("""
                    SELECT MAX(trade_date) as latest_date,
                           COUNT(DISTINCT ts_code) as stock_count
                    FROM stock_daily
                """).fetchone()
                
                if latest_stock[0]:
                    days_ago = (datetime.now() - datetime.strptime(latest_stock[0], '%Y%m%d')).days
                    status = "🟢" if days_ago <= 3 else "🟡" if days_ago <= 7 else "🔴"
                    print(f"  📈 股票价格数据: {latest_stock[0]} ({days_ago}天前) {status}")
                    print(f"      包含 {latest_stock[1]:,} 只股票")
            except:
                pass
            
            try:
                latest_basic = conn.execute("""
                    SELECT MAX(trade_date) as latest_date,
                           COUNT(DISTINCT ts_code) as stock_count
                    FROM stock_daily_basic
                """).fetchone()
                
                if latest_basic[0]:
                    days_ago = (datetime.now() - datetime.strptime(latest_basic[0], '%Y%m%d')).days
                    status = "🟢" if days_ago <= 3 else "🟡" if days_ago <= 7 else "🔴"
                    print(f"  💰 股票估值数据: {latest_basic[0]} ({days_ago}天前) {status}")
            except:
                pass
    
    # 检查基金数据
    if os.path.exists(cache.fund_db):
        with sqlite3.connect(cache.fund_db) as conn:
            try:
                latest_etf = conn.execute("""
                    SELECT MAX(trade_date) as latest_date,
                           COUNT(DISTINCT ts_code) as etf_count
                    FROM etf_daily
                """).fetchone()
                
                if latest_etf[0]:
                    days_ago = (datetime.now() - datetime.strptime(latest_etf[0], '%Y%m%d')).days
                    status = "🟢" if days_ago <= 3 else "🟡" if days_ago <= 7 else "🔴"
                    print(f"  🏦 ETF价格数据: {latest_etf[0]} ({days_ago}天前) {status}")
            except:
                pass
            
            try:
                latest_nav = conn.execute("""
                    SELECT MAX(nav_date) as latest_date,
                           COUNT(DISTINCT ts_code) as fund_count
                    FROM fund_nav
                """).fetchone()
                
                if latest_nav[0]:
                    days_ago = (datetime.now() - datetime.strptime(latest_nav[0], '%Y%m%d')).days
                    status = "🟢" if days_ago <= 3 else "🟡" if days_ago <= 7 else "🔴"
                    print(f"  💎 基金净值数据: {latest_nav[0]} ({days_ago}天前) {status}")
            except:
                pass


def data_quality_check(cache):
    """数据质量全面检查"""
    print("\n🔍 数据质量全面检查")
    print("=" * 50)
    
    # 1. 基础数据完整性检查
    print("📊 基础数据完整性:")
    
    # 检查股票基本信息
    stock_basic = cache.get_stock_basic()
    if stock_basic is not None and not stock_basic.empty:
        missing_fields = []
        key_fields = ['ts_code', 'symbol', 'name', 'industry', 'market']
        for field in key_fields:
            if field in stock_basic.columns:
                null_count = stock_basic[field].isnull().sum()
                if null_count > 0:
                    missing_fields.append(f"{field}({null_count})")
        
        if missing_fields:
            print(f"  ⚠️ 股票基本信息缺失字段: {', '.join(missing_fields)}")
        else:
            print(f"  ✅ 股票基本信息完整 ({len(stock_basic):,}只)")
    else:
        print("  ❌ 股票基本信息缺失")
    
    # 检查基金基本信息
    fund_basic = cache.get_fund_basic()
    if fund_basic is not None and not fund_basic.empty:
        missing_fields = []
        key_fields = ['ts_code', 'name', 'fund_type', 'market']
        for field in key_fields:
            if field in fund_basic.columns:
                null_count = fund_basic[field].isnull().sum()
                if null_count > 0:
                    missing_fields.append(f"{field}({null_count})")
        
        if missing_fields:
            print(f"  ⚠️ 基金基本信息缺失字段: {', '.join(missing_fields)}")
        else:
            print(f"  ✅ 基金基本信息完整 ({len(fund_basic):,}只)")
    else:
        print("  ❌ 基金基本信息缺失")
    
    # 2. 时序数据连续性检查
    print("\n📈 时序数据连续性检查:")
    check_data_continuity(cache)
    
    # 3. 数据异常值检查
    print("\n⚠️ 数据异常值检查:")
    check_data_anomalies(cache)


def check_data_continuity(cache):
    """检查时序数据连续性"""
    # 检查主要股票的数据连续性
    major_stocks = ['000001.SZ', '600036.SH', '000858.SZ']
    
    for ts_code in major_stocks:
        try:
            data = cache.get_stock_daily(ts_code, start_date='20240101')
            if data is not None and not data.empty:
                # 计算交易日连续性
                date_range = pd.date_range(
                    start=pd.to_datetime(data['trade_date'].min(), format='%Y%m%d'),
                    end=pd.to_datetime(data['trade_date'].max(), format='%Y%m%d'),
                    freq='B'  # 工作日
                )
                expected_days = len(date_range)
                actual_days = len(data)
                continuity = actual_days / expected_days * 100
                
                status = "✅" if continuity > 90 else "⚠️" if continuity > 80 else "❌"
                print(f"  {status} {ts_code}: {continuity:.1f}% 连续性 ({actual_days}/{expected_days})")
            else:
                print(f"  ❌ {ts_code}: 无数据")
        except Exception as e:
            print(f"  ❌ {ts_code}: 错误 - {e}")


def check_data_anomalies(cache):
    """检查数据异常值"""
    # 检查价格异常的股票
    if os.path.exists(cache.stock_db):
        with sqlite3.connect(cache.stock_db) as conn:
            try:
                # 查找价格异常的记录
                anomalies = conn.execute("""
                    SELECT ts_code, trade_date, close, 
                           LAG(close) OVER (PARTITION BY ts_code ORDER BY trade_date) as prev_close,
                           ROUND((close / LAG(close) OVER (PARTITION BY ts_code ORDER BY trade_date) - 1) * 100, 2) as change_pct
                    FROM stock_daily 
                    WHERE trade_date >= '20240101'
                    HAVING ABS(change_pct) > 20 AND prev_close IS NOT NULL
                    ORDER BY ABS(change_pct) DESC
                    LIMIT 10
                """).fetchall()
                
                if anomalies:
                    print("  📊 价格异常变动(TOP10):")
                    for ts_code, trade_date, close, prev_close, change_pct in anomalies:
                        print(f"    {ts_code} {trade_date}: {change_pct:+.1f}% ({prev_close}→{close})")
                else:
                    print("  ✅ 未发现明显价格异常")
            except Exception as e:
                print(f"  ❌ 价格异常检查失败: {e}")
    
    # 检查基金净值异常
    if os.path.exists(cache.fund_db):
        with sqlite3.connect(cache.fund_db) as conn:
            try:
                nav_anomalies = conn.execute("""
                    SELECT ts_code, nav_date, unit_nav,
                           LAG(unit_nav) OVER (PARTITION BY ts_code ORDER BY nav_date) as prev_nav,
                           ROUND((unit_nav / LAG(unit_nav) OVER (PARTITION BY ts_code ORDER BY nav_date) - 1) * 100, 2) as change_pct
                    FROM fund_nav 
                    WHERE nav_date >= '20240101'
                    HAVING ABS(change_pct) > 10 AND prev_nav IS NOT NULL
                    ORDER BY ABS(change_pct) DESC
                    LIMIT 5
                """).fetchall()
                
                if nav_anomalies:
                    print("  📊 基金净值异常变动(TOP5):")
                    for ts_code, nav_date, unit_nav, prev_nav, change_pct in nav_anomalies:
                        print(f"    {ts_code} {nav_date}: {change_pct:+.1f}% ({prev_nav}→{unit_nav})")
                else:
                    print("  ✅ 基金净值数据正常")
            except Exception as e:
                print(f"  ❌ 基金净值检查失败: {e}")


def analyze_extended_data(cache):
    """扩展数据分析"""
    print("\n🔍 扩展数据分析")
    print("=" * 50)
    
    # 子菜单
    print("\n请选择扩展数据类型:")
    print("1. 复权因子数据")
    print("2. 分红送转数据")
    print("3. 限售解禁数据")
    print("4. 股东户数数据")
    print("5. 行业分类数据")
    print("6. 综合分析")
    print("0. 返回主菜单")
    
    choice = input("\n请输入选择 (0-6): ").strip()
    
    if choice == "1":
        analyze_adj_factor(cache)
    elif choice == "2":
        analyze_dividend_data(cache)
    elif choice == "3":
        analyze_share_float(cache)
    elif choice == "4":
        analyze_holder_number(cache)
    elif choice == "5":
        analyze_industry_classify(cache)
    elif choice == "6":
        analyze_comprehensive_extended(cache)
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")


def analyze_adj_factor(cache):
    """复权因子数据分析"""
    print("\n📊 复权因子数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            adj_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM stock_adj_factor
            """).fetchone()
            
            if adj_stats[0] > 0:
                print(f"📈 股票数量: {adj_stats[0]:,} 只")
                print(f"📊 记录总数: {adj_stats[1]:,} 条")
                print(f"📅 时间范围: {adj_stats[2]} ~ {adj_stats[3]}")
                
                # 复权因子统计
                factor_stats = conn.execute("""
                    WITH factor_calc AS (
                        SELECT 
                            adj_factor,
                            AVG(adj_factor) OVER () as avg_factor
                        FROM stock_adj_factor
                    )
                    SELECT 
                        avg_factor,
                        MIN(adj_factor) as min_factor,
                        MAX(adj_factor) as max_factor,
                        SQRT(AVG((adj_factor - avg_factor) * (adj_factor - avg_factor))) as std_factor
                    FROM factor_calc
                """).fetchone()
                
                print(f"\n📊 复权因子统计:")
                print(f"  平均值: {factor_stats[0]:.4f}")
                print(f"  最小值: {factor_stats[1]:.4f}")
                print(f"  最大值: {factor_stats[2]:.4f}")
                print(f"  标准差: {factor_stats[3]:.4f}")
                
                # 复权因子变化最大的股票
                big_changes = conn.execute("""
                    SELECT ts_code, trade_date, adj_factor,
                           LAG(adj_factor) OVER (PARTITION BY ts_code ORDER BY trade_date) as prev_factor
                    FROM stock_adj_factor
                    ORDER BY ts_code, trade_date
                """).fetchall()
                
                # 计算复权因子变化幅度
                changes = []
                for ts_code, trade_date, factor, prev_factor in big_changes:
                    if prev_factor is not None and prev_factor > 0:
                        change_pct = abs(factor / prev_factor - 1) * 100
                        if change_pct > 5:  # 变化超过5%
                            changes.append((ts_code, trade_date, change_pct, prev_factor, factor))
                
                if changes:
                    changes.sort(key=lambda x: x[2], reverse=True)
                    print(f"\n📊 复权因子大幅变化事件(TOP10):")
                    for i, (ts_code, trade_date, change_pct, prev_factor, factor) in enumerate(changes[:10]):
                        print(f"  {i+1}. {ts_code} {trade_date}: {change_pct:.2f}% ({prev_factor:.4f}→{factor:.4f})")
                
                # 最新复权因子
                latest_factors = conn.execute("""
                    SELECT ts_code, MAX(trade_date) as latest_date, adj_factor
                    FROM stock_adj_factor 
                    GROUP BY ts_code 
                    ORDER BY adj_factor DESC 
                    LIMIT 5
                """).fetchall()
                
                if latest_factors:
                    print(f"\n📆 复权因子最高的股票(TOP5):")
                    for ts_code, latest_date, adj_factor in latest_factors:
                        print(f"  {ts_code}: {adj_factor:.4f} ({latest_date})")
                        
            else:
                print("❌ 无复权因子数据")
        except Exception as e:
            print(f"❌ 复权因子分析失败: {e}")


def analyze_dividend_data(cache):
    """分红送转数据分析"""
    print("\n💰 分红送转数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            div_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(ann_date) as min_date,
                    MAX(ann_date) as max_date
                FROM stock_dividend
            """).fetchone()
            
            if div_stats[0] > 0:
                print(f"📈 分红股票数量: {div_stats[0]:,} 只")
                print(f"📊 分红记录总数: {div_stats[1]:,} 条")
                print(f"📅 公告时间范围: {div_stats[2]} ~ {div_stats[3]}")
                
                # 分红类型统计
                div_types = conn.execute("""
                    SELECT 
                        COUNT(CASE WHEN cash_div > 0 THEN 1 END) as cash_count,
                        COUNT(CASE WHEN stk_div > 0 THEN 1 END) as stock_count,
                        COUNT(CASE WHEN stk_bo_rate > 0 THEN 1 END) as bonus_count
                    FROM stock_dividend
                """).fetchone()
                
                print(f"\n💰 分红类型统计:")
                print(f"  现金分红: {div_types[0]:,} 次")
                print(f"  送股: {div_types[1]:,} 次")
                print(f"  转股: {div_types[2]:,} 次")
                
                # 现金分红统计
                cash_stats = conn.execute("""
                    SELECT 
                        AVG(cash_div) as avg_cash,
                        MAX(cash_div) as max_cash,
                        SUM(cash_div) as total_cash
                    FROM stock_dividend
                    WHERE cash_div > 0
                """).fetchone()
                
                if cash_stats[0]:
                    print(f"\n💵 现金分红统计:")
                    print(f"  平均分红: {cash_stats[0]:.4f} 元/股")
                    print(f"  最高分红: {cash_stats[1]:.4f} 元/股")
                    print(f"  累计分红: {cash_stats[2]:.2f} 元/股")
                
                # 高额分红股票
                high_div = conn.execute("""
                    SELECT ts_code, ann_date, cash_div, ex_date
                    FROM stock_dividend
                    WHERE cash_div > 1.0
                    ORDER BY cash_div DESC
                    LIMIT 10
                """).fetchall()
                
                if high_div:
                    print(f"\n🏆 高额现金分红(TOP10):")
                    for ts_code, ann_date, cash_div, ex_date in high_div:
                        print(f"  {ts_code}: {cash_div:.4f}元/股 (公告:{ann_date}, 除权:{ex_date})")
                
                # 近期分红
                recent_div = conn.execute("""
                    SELECT ts_code, ann_date, cash_div, stk_div, stk_bo_rate
                    FROM stock_dividend
                    WHERE ann_date >= date('now', '-90 days')
                    ORDER BY ann_date DESC
                    LIMIT 10
                """).fetchall()
                
                if recent_div:
                    print(f"\n📅 近期分红公告(90天内):")
                    for ts_code, ann_date, cash_div, stk_div, stk_bo_rate in recent_div:
                        div_info = []
                        if cash_div > 0:
                            div_info.append(f"现金{cash_div:.3f}元")
                        if stk_div > 0:
                            div_info.append(f"送股{stk_div:.3f}")
                        if stk_bo_rate > 0:
                            div_info.append(f"转股{stk_bo_rate:.3f}")
                        print(f"  {ts_code} {ann_date}: {', '.join(div_info) if div_info else '无分红'}")
                        
            else:
                print("❌ 无分红送转数据")
        except Exception as e:
            print(f"❌ 分红送转分析失败: {e}")


def analyze_share_float(cache):
    """限售解禁数据分析"""
    print("\n🔓 限售解禁数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            float_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(ann_date) as min_date,
                    MAX(ann_date) as max_date
                FROM stock_share_float
            """).fetchone()
            
            if float_stats[0] > 0:
                print(f"📈 涉及股票数量: {float_stats[0]:,} 只")
                print(f"📊 解禁记录总数: {float_stats[1]:,} 条")
                print(f"📅 公告时间范围: {float_stats[2]} ~ {float_stats[3]}")
                
                # 解禁规模统计
                float_amount = conn.execute("""
                    SELECT 
                        SUM(float_share) as total_float,
                        AVG(float_share) as avg_float,
                        MAX(float_share) as max_float
                    FROM stock_share_float
                    WHERE float_share > 0
                """).fetchone()
                
                if float_amount[0]:
                    print(f"\n🔢 解禁规模统计:")
                    print(f"  累计解禁: {float_amount[0]/10000:.2f} 万股")
                    print(f"  平均解禁: {float_amount[1]/10000:.2f} 万股")
                    print(f"  最大解禁: {float_amount[2]/10000:.2f} 万股")
                
                # 大规模解禁
                big_float = conn.execute("""
                    SELECT ts_code, ann_date, float_date, float_share, float_ratio
                    FROM stock_share_float
                    WHERE float_share > 100000000  -- 1亿股以上
                    ORDER BY float_share DESC
                    LIMIT 10
                """).fetchall()
                
                if big_float:
                    print(f"\n🏆 大规模解禁事件(TOP10):")
                    for ts_code, ann_date, float_date, float_share, float_ratio in big_float:
                        print(f"  {ts_code}: {float_share/10000:.0f}万股 ({float_ratio:.2f}%) 解禁日:{float_date}")
                
                # 即将解禁
                upcoming_float = conn.execute("""
                    SELECT ts_code, float_date, float_share, float_ratio
                    FROM stock_share_float
                    WHERE float_date >= date('now') AND float_date <= date('now', '+30 days')
                    ORDER BY float_date, float_share DESC
                    LIMIT 10
                """).fetchall()
                
                if upcoming_float:
                    print(f"\n📅 即将解禁(30天内):")
                    for ts_code, float_date, float_share, float_ratio in upcoming_float:
                        print(f"  {ts_code} {float_date}: {float_share/10000:.0f}万股 ({float_ratio:.2f}%)")
                        
            else:
                print("❌ 无限售解禁数据")
        except Exception as e:
            print(f"❌ 限售解禁分析失败: {e}")


def analyze_holder_number(cache):
    """股东户数数据分析"""
    print("\n👥 股东户数数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            holder_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(ann_date) as min_date,
                    MAX(ann_date) as max_date
                FROM stock_holder_number
            """).fetchone()
            
            if holder_stats[0] > 0:
                print(f"📈 涉及股票数量: {holder_stats[0]:,} 只")
                print(f"📊 记录总数: {holder_stats[1]:,} 条")
                print(f"📅 公告时间范围: {holder_stats[2]} ~ {holder_stats[3]}")
                
                # 股东户数统计
                holder_num_stats = conn.execute("""
                    SELECT 
                        AVG(holder_num) as avg_holders,
                        MIN(holder_num) as min_holders,
                        MAX(holder_num) as max_holders
                    FROM stock_holder_number
                    WHERE holder_num > 0
                """).fetchone()
                
                if holder_num_stats[0]:
                    print(f"\n👥 股东户数统计:")
                    print(f"  平均户数: {holder_num_stats[0]:,.0f} 户")
                    print(f"  最少户数: {holder_num_stats[1]:,.0f} 户")
                    print(f"  最多户数: {holder_num_stats[2]:,.0f} 户")
                
                # 股东户数最多的股票
                max_holders = conn.execute("""
                    SELECT ts_code, end_date, holder_num
                    FROM stock_holder_number
                    WHERE holder_num > 0
                    ORDER BY holder_num DESC
                    LIMIT 10
                """).fetchall()
                
                if max_holders:
                    print(f"\n🏆 股东户数最多(TOP10):")
                    for ts_code, end_date, holder_num in max_holders:
                        print(f"  {ts_code}: {holder_num:,} 户 ({end_date})")
                
                # 股东户数变化分析
                holder_changes = conn.execute("""
                    SELECT ts_code, end_date, holder_num,
                           LAG(holder_num) OVER (PARTITION BY ts_code ORDER BY end_date) as prev_holders
                    FROM stock_holder_number
                    WHERE holder_num > 0
                    ORDER BY ts_code, end_date
                """).fetchall()
                
                # 计算股东户数变化
                big_changes = []
                for ts_code, end_date, holders, prev_holders in holder_changes:
                    if prev_holders is not None and prev_holders > 0:
                        change_pct = (holders / prev_holders - 1) * 100
                        if abs(change_pct) > 20:  # 变化超过20%
                            big_changes.append((ts_code, end_date, change_pct, prev_holders, holders))
                
                if big_changes:
                    big_changes.sort(key=lambda x: abs(x[2]), reverse=True)
                    print(f"\n📊 股东户数大幅变化(TOP10):")
                    for i, (ts_code, end_date, change_pct, prev_holders, holders) in enumerate(big_changes[:10]):
                        print(f"  {i+1}. {ts_code} {end_date}: {change_pct:+.1f}% ({prev_holders:,}→{holders:,})")
                        
            else:
                print("❌ 无股东户数数据")
        except Exception as e:
            print(f"❌ 股东户数分析失败: {e}")


def analyze_industry_classify(cache):
    """行业分类数据分析"""
    print("\n🏭 行业分类数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            industry_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as stock_count,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT industry_code) as industry_count
                FROM stock_industry_classify
            """).fetchone()
            
            if industry_stats[0] > 0:
                print(f"📈 涉及股票数量: {industry_stats[0]:,} 只")
                print(f"📊 分类记录总数: {industry_stats[1]:,} 条")
                print(f"🏭 行业类别数量: {industry_stats[2]:,} 个")
                
                # 行业分布统计
                industry_dist = conn.execute("""
                    SELECT industry_name, COUNT(DISTINCT ts_code) as stock_count
                    FROM stock_industry_classify
                    GROUP BY industry_name
                    ORDER BY stock_count DESC
                    LIMIT 15
                """).fetchall()
                
                if industry_dist:
                    print(f"\n🏭 行业分布(TOP15):")
                    for industry_name, stock_count in industry_dist:
                        print(f"  {industry_name}: {stock_count:,} 只")
                
                # 按分类级别统计
                level_stats = conn.execute("""
                    SELECT level, COUNT(DISTINCT ts_code) as stock_count, COUNT(DISTINCT industry_code) as industry_count
                    FROM stock_industry_classify
                    GROUP BY level
                    ORDER BY level
                """).fetchall()
                
                if level_stats:
                    print(f"\n📊 分类级别统计:")
                    level_names = {'L1': '一级行业', 'L2': '二级行业', 'L3': '三级行业'}
                    for level, stock_count, industry_count in level_stats:
                        level_name = level_names.get(level, level)
                        print(f"  {level_name}: {industry_count} 个行业, {stock_count:,} 只股票")
                
                # 查找单一行业股票较少的情况
                small_industries = conn.execute("""
                    SELECT industry_name, COUNT(DISTINCT ts_code) as stock_count
                    FROM stock_industry_classify
                    WHERE level = 'L2'
                    GROUP BY industry_name
                    HAVING stock_count <= 5
                    ORDER BY stock_count
                """).fetchall()
                
                if small_industries:
                    print(f"\n🔍 股票数量较少的行业(≤5只):")
                    for industry_name, stock_count in small_industries[:10]:
                        print(f"  {industry_name}: {stock_count} 只")
                        
            else:
                print("❌ 无行业分类数据")
        except Exception as e:
            print(f"❌ 行业分类分析失败: {e}")


def analyze_comprehensive_extended(cache):
    """扩展数据综合分析"""
    print("\n📊 扩展数据综合分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            print("📋 扩展数据表概况:")
            
            # 各表数据统计
            tables = [
                ('stock_adj_factor', '复权因子'),
                ('stock_dividend', '分红送转'),
                ('stock_share_float', '限售解禁'),
                ('stock_holder_number', '股东户数'),
                ('stock_industry_classify', '行业分类')
            ]
            
            for table_name, table_desc in tables:
                try:
                    stats = conn.execute(f"""
                        SELECT 
                            COUNT(DISTINCT ts_code) as stock_count,
                            COUNT(*) as record_count
                        FROM {table_name}
                    """).fetchone()
                    
                    print(f"  {table_desc}: {stats[0]:,} 只股票, {stats[1]:,} 条记录")
                except:
                    print(f"  {table_desc}: 表不存在或无数据")
            
            # 数据完整性分析
            print(f"\n🔍 数据完整性分析:")
            
            # 找出有完整扩展数据的股票
            complete_stocks = conn.execute("""
                SELECT DISTINCT bd.ts_code
                FROM (SELECT DISTINCT ts_code FROM stock_daily) bd
                WHERE EXISTS (SELECT 1 FROM stock_adj_factor af WHERE af.ts_code = bd.ts_code)
                  AND EXISTS (SELECT 1 FROM stock_dividend div WHERE div.ts_code = bd.ts_code)
                  AND EXISTS (SELECT 1 FROM stock_industry_classify ic WHERE ic.ts_code = bd.ts_code)
            """).fetchall()
            
            print(f"  具备完整扩展数据的股票: {len(complete_stocks):,} 只")
            
            # 最近有分红的活跃股票
            active_dividend = conn.execute("""
                SELECT ts_code, COUNT(*) as div_count, MAX(ann_date) as latest_div
                FROM stock_dividend
                WHERE ann_date >= date('now', '-1 year')
                GROUP BY ts_code
                HAVING div_count >= 2
                ORDER BY div_count DESC, latest_div DESC
                LIMIT 10
            """).fetchall()
            
            if active_dividend:
                print(f"\n💰 活跃分红股票(近1年≥2次):")
                for ts_code, div_count, latest_div in active_dividend:
                    print(f"  {ts_code}: {div_count} 次分红, 最近:{latest_div}")
            
            # 即将有大额解禁的股票
            upcoming_big_float = conn.execute("""
                SELECT ts_code, float_date, float_share, float_ratio
                FROM stock_share_float
                WHERE float_date >= date('now') 
                  AND float_date <= date('now', '+90 days')
                  AND float_share > 50000000  -- 5000万股以上
                ORDER BY float_share DESC
                LIMIT 5
            """).fetchall()
            
            if upcoming_big_float:
                print(f"\n🔓 即将大额解禁(90天内):")
                for ts_code, float_date, float_share, float_ratio in upcoming_big_float:
                    print(f"  {ts_code} {float_date}: {float_share/10000:.0f}万股 ({float_ratio:.1f}%)")
                    
        except Exception as e:
            print(f"❌ 综合分析失败: {e}")


def analyze_index_data(cache):
    """指数数据分析"""
    print("\n📈 指数数据分析")
    print("=" * 50)
    
    # 子菜单
    print("\n请选择指数数据类型:")
    print("1. 指数日线数据")
    print("2. 指数成分股权重")
    print("3. 申万行业指数数据")
    print("4. 指数综合分析")
    print("0. 返回主菜单")
    
    choice = input("\n请输入选择 (0-4): ").strip()
    
    if choice == "1":
        analyze_index_daily(cache)
    elif choice == "2":
        analyze_index_weight(cache)
    elif choice == "3":
        analyze_sw_industry_daily(cache)
    elif choice == "4":
        analyze_comprehensive_index(cache)
    elif choice == "0":
        return
    else:
        print("❌ 无效选择")


def analyze_index_daily(cache):
    """指数日线数据分析"""
    print("\n📊 指数日线数据分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            index_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as index_count,
                    COUNT(*) as record_count,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM index_daily
            """).fetchone()
            
            if index_stats[0] > 0:
                print(f"📈 指数数量: {index_stats[0]:,} 个")
                print(f"📊 记录总数: {index_stats[1]:,} 条")
                print(f"📅 时间范围: {index_stats[2]} ~ {index_stats[3]}")
                
                # 主要指数列表
                major_indexes = conn.execute("""
                    SELECT ts_code, COUNT(*) as day_count, 
                           MIN(trade_date) as start_date, MAX(trade_date) as end_date
                    FROM index_daily
                    GROUP BY ts_code
                    ORDER BY day_count DESC
                    LIMIT 10
                """).fetchall()
                
                if major_indexes:
                    print(f"\n📊 主要指数数据量(TOP10):")
                    for ts_code, day_count, start_date, end_date in major_indexes:
                        print(f"  {ts_code}: {day_count:,} 天 ({start_date}~{end_date})")
                
                # 最新指数表现
                latest_performance = conn.execute("""
                    SELECT ts_code, trade_date, close, pct_chg
                    FROM index_daily
                    WHERE trade_date = (SELECT MAX(trade_date) FROM index_daily)
                    ORDER BY ABS(pct_chg) DESC
                    LIMIT 10
                """).fetchall()
                
                if latest_performance:
                    latest_date = latest_performance[0][1]
                    print(f"\n📈 最新指数表现 ({latest_date}):")
                    for ts_code, trade_date, close, pct_chg in latest_performance:
                        print(f"  {ts_code}: {close:.2f} ({pct_chg:+.2f}%)")
                
                # 年度表现分析
                yearly_performance = conn.execute("""
                    SELECT ts_code,
                           substr(trade_date, 1, 4) as year,
                           MIN(close) as year_low,
                           MAX(close) as year_high,
                           (MAX(close) / MIN(close) - 1) * 100 as year_range
                    FROM index_daily
                    WHERE substr(trade_date, 1, 4) >= '2023'
                    GROUP BY ts_code, substr(trade_date, 1, 4)
                    HAVING COUNT(*) > 200  -- 有足够交易日数据
                    ORDER BY year_range DESC
                    LIMIT 10
                """).fetchall()
                
                if yearly_performance:
                    print(f"\n📈 年度波动幅度较大的指数(2023年以来):")
                    for ts_code, year, year_low, year_high, year_range in yearly_performance:
                        print(f"  {ts_code} {year}年: {year_range:.1f}% ({year_low:.2f}~{year_high:.2f})")
                        
            else:
                print("❌ 无指数日线数据")
        except Exception as e:
            print(f"❌ 指数日线分析失败: {e}")


def analyze_index_weight(cache):
    """指数成分股权重分析"""
    print("\n⚖️ 指数成分股权重分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            weight_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT index_code) as index_count,
                    COUNT(DISTINCT con_code) as stock_count,
                    COUNT(*) as record_count,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM index_weight
            """).fetchone()
            
            if weight_stats[0] > 0:
                print(f"📈 指数数量: {weight_stats[0]:,} 个")
                print(f"📊 成分股数量: {weight_stats[1]:,} 只")
                print(f"📊 权重记录总数: {weight_stats[2]:,} 条")
                print(f"📅 时间范围: {weight_stats[3]} ~ {weight_stats[4]}")
                
                # 各指数成分股数量
                index_stocks = conn.execute("""
                    SELECT index_code, COUNT(DISTINCT con_code) as stock_count, MAX(trade_date) as latest_date
                    FROM index_weight
                    GROUP BY index_code
                    ORDER BY stock_count DESC
                    LIMIT 10
                """).fetchall()
                
                if index_stocks:
                    print(f"\n📊 各指数成分股数量(TOP10):")
                    for index_code, stock_count, latest_date in index_stocks:
                        print(f"  {index_code}: {stock_count:,} 只成分股 (最新:{latest_date})")
                
                # 权重最高的成分股
                top_weights = conn.execute("""
                    SELECT index_code, con_code, weight, trade_date
                    FROM index_weight
                    WHERE weight > 0
                    ORDER BY weight DESC
                    LIMIT 15
                """).fetchall()
                
                if top_weights:
                    print(f"\n🏆 权重最高的成分股(TOP15):")
                    for index_code, con_code, weight, trade_date in top_weights:
                        print(f"  {con_code} 在 {index_code}: {weight:.3f}% ({trade_date})")
                
                # 在多个指数中的股票
                multi_index_stocks = conn.execute("""
                    SELECT con_code, COUNT(DISTINCT index_code) as index_count,
                           GROUP_CONCAT(DISTINCT index_code) as indexes
                    FROM index_weight
                    GROUP BY con_code
                    HAVING index_count >= 3
                    ORDER BY index_count DESC
                    LIMIT 10
                """).fetchall()
                
                if multi_index_stocks:
                    print(f"\n🔗 多指数成分股(≥3个指数):")
                    for con_code, index_count, indexes in multi_index_stocks:
                        index_list = indexes.split(',')[:5]  # 只显示前5个
                        print(f"  {con_code}: {index_count} 个指数 ({', '.join(index_list)}...)")
                
                # 权重变化分析
                weight_changes = conn.execute("""
                    SELECT index_code, con_code, trade_date, weight,
                           LAG(weight) OVER (PARTITION BY index_code, con_code ORDER BY trade_date) as prev_weight
                    FROM index_weight
                    WHERE weight > 0
                    ORDER BY index_code, con_code, trade_date
                """).fetchall()
                
                # 计算权重大幅变化
                big_weight_changes = []
                for index_code, con_code, trade_date, weight, prev_weight in weight_changes:
                    if prev_weight is not None and prev_weight > 0:
                        change_pct = abs(weight / prev_weight - 1) * 100
                        if change_pct > 20:  # 变化超过20%
                            big_weight_changes.append((index_code, con_code, trade_date, change_pct, prev_weight, weight))
                
                if big_weight_changes:
                    big_weight_changes.sort(key=lambda x: x[3], reverse=True)
                    print(f"\n📊 权重大幅变化事件(TOP10):")
                    for i, (index_code, con_code, trade_date, change_pct, prev_weight, weight) in enumerate(big_weight_changes[:10]):
                        print(f"  {i+1}. {con_code} 在 {index_code} {trade_date}: {change_pct:.1f}% ({prev_weight:.3f}%→{weight:.3f}%)")
                        
            else:
                print("❌ 无指数权重数据")
        except Exception as e:
            print(f"❌ 指数权重分析失败: {e}")


def analyze_comprehensive_index(cache):
    """指数综合分析"""
    print("\n📊 指数综合分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            print("📋 指数数据表概况:")
            
            # 指数数据统计
            index_tables = [
                ('index_daily', '指数日线'),
                ('index_weight', '成分股权重')
            ]
            
            for table_name, table_desc in index_tables:
                try:
                    if table_name == 'index_daily':
                        stats = conn.execute(f"""
                            SELECT 
                                COUNT(DISTINCT ts_code) as count,
                                COUNT(*) as record_count
                            FROM {table_name}
                        """).fetchone()
                        print(f"  {table_desc}: {stats[0]:,} 个指数, {stats[1]:,} 条记录")
                    else:
                        stats = conn.execute(f"""
                            SELECT 
                                COUNT(DISTINCT index_code) as index_count,
                                COUNT(DISTINCT con_code) as stock_count,
                                COUNT(*) as record_count
                            FROM {table_name}
                        """).fetchone()
                        print(f"  {table_desc}: {stats[0]:,} 个指数, {stats[1]:,} 只成分股, {stats[2]:,} 条记录")
                except:
                    print(f"  {table_desc}: 表不存在或无数据")
            
            # 找出有完整数据的指数
            complete_indexes = conn.execute("""
                SELECT DISTINCT id_.ts_code
                FROM index_daily id_
                WHERE EXISTS (
                    SELECT 1 FROM index_weight iw 
                    WHERE iw.index_code = id_.ts_code
                )
            """).fetchall()
            
            print(f"\n🔍 数据完整性分析:")
            print(f"  同时具备日线和权重数据的指数: {len(complete_indexes):,} 个")
            
            # 主要指数的最新表现
            major_indexes = ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH', '000905.SH']
            print(f"\n📈 主要指数最新表现:")
            
            for index_code in major_indexes:
                try:
                    # 获取最新价格数据
                    latest_data = conn.execute("""
                        SELECT trade_date, close, pct_chg
                        FROM index_daily
                        WHERE ts_code = ?
                        ORDER BY trade_date DESC
                        LIMIT 1
                    """, (index_code,)).fetchone()
                    
                    if latest_data:
                        trade_date, close, pct_chg = latest_data
                        index_name = {
                            '000001.SH': '上证指数',
                            '399001.SZ': '深证成指',
                            '399006.SZ': '创业板指',
                            '000300.SH': '沪深300',
                            '000905.SH': '中证500'
                        }.get(index_code, index_code)
                        
                        print(f"  {index_name}({index_code}): {close:.2f} ({pct_chg:+.2f}%) [{trade_date}]")
                        
                        # 获取成分股数量
                        component_count = conn.execute("""
                            SELECT COUNT(DISTINCT con_code)
                            FROM index_weight
                            WHERE index_code = ?
                        """, (index_code,)).fetchone()
                        
                        if component_count[0]:
                            print(f"    成分股数量: {component_count[0]:,} 只")
                    else:
                        print(f"  {index_code}: 无数据")
                except Exception as e:
                    print(f"  {index_code}: 查询失败 - {e}")
                    
        except Exception as e:
            print(f"❌ 指数综合分析失败: {e}")


def analyze_sw_industry_daily(cache):
    """申万行业指数日线数据分析"""
    print("\n🏭 申万行业指数分析")
    print("-" * 40)
    
    if not os.path.exists(cache.stock_db):
        print("❌ 股票数据库文件不存在")
        return
    
    with sqlite3.connect(cache.stock_db) as conn:
        try:
            # 基础统计
            sw_stats = conn.execute("""
                SELECT 
                    COUNT(DISTINCT ts_code) as index_count,
                    COUNT(*) as record_count,
                    MIN(trade_date) as min_date,
                    MAX(trade_date) as max_date
                FROM sw_industry_daily
            """).fetchone()
            
            if sw_stats[0] > 0:
                print(f"📈 申万行业指数数量: {sw_stats[0]:,} 个")
                print(f"📊 记录总数: {sw_stats[1]:,} 条")
                print(f"📅 时间范围: {sw_stats[2]} ~ {sw_stats[3]}")
                
                # 获取表结构信息
                columns_info = conn.execute("PRAGMA table_info(sw_industry_daily)").fetchall()
                available_columns = [col[1] for col in columns_info]
                print(f"📋 可用字段: {', '.join(available_columns)}")
                
                # 行业列表和基本信息
                industry_list = conn.execute("""
                    SELECT DISTINCT ts_code, name
                    FROM sw_industry_daily
                    WHERE name IS NOT NULL
                    ORDER BY ts_code
                """).fetchall()
                
                if industry_list:
                    print(f"\n🏭 申万行业指数列表 (共{len(industry_list)}个):")
                    for i, (ts_code, name) in enumerate(industry_list[:20]):  # 显示前20个
                        print(f"  {ts_code}: {name or '未知'}")
                    if len(industry_list) > 20:
                        print(f"  ... 还有 {len(industry_list) - 20} 个行业")
                
                # 最新行业表现（涨跌幅）
                if 'pct_change' in available_columns:
                    latest_performance = conn.execute("""
                        SELECT ts_code, name, trade_date, close, pct_change
                        FROM sw_industry_daily
                        WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
                          AND pct_change IS NOT NULL
                        ORDER BY pct_change DESC
                        LIMIT 10
                    """).fetchall()
                    
                    if latest_performance:
                        latest_date = latest_performance[0][2]
                        print(f"\n📈 最新行业表现 ({latest_date}) TOP10:")
                        for ts_code, name, trade_date, close, pct_change in latest_performance:
                            print(f"  {name or ts_code}: {close:.2f} ({pct_change:+.2f}%)")
                
                # 估值分析（PE、PB、PS）
                valuation_fields = [field for field in ['pe', 'pb', 'ps'] if field in available_columns]
                if valuation_fields:
                    print(f"\n💰 估值分析 (最新数据):")
                    
                    for field in valuation_fields:
                        field_name = {'pe': 'PE市盈率', 'pb': 'PB市净率', 'ps': 'PS市销率'}[field]
                        
                        valuation_stats = conn.execute(f"""
                            SELECT 
                                AVG({field}) as avg_val,
                                MIN({field}) as min_val,
                                MAX({field}) as max_val,
                                COUNT({field}) as valid_count
                            FROM sw_industry_daily
                            WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
                              AND {field} IS NOT NULL AND {field} > 0
                        """).fetchone()
                        
                        if valuation_stats[3] > 0:  # 有有效数据
                            print(f"  {field_name}: 平均{valuation_stats[0]:.2f}, 范围{valuation_stats[1]:.2f}~{valuation_stats[2]:.2f} (有效数据{valuation_stats[3]}个)")
                            
                            # 估值极端值
                            extreme_vals = conn.execute(f"""
                                SELECT ts_code, name, {field}
                                FROM sw_industry_daily
                                WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
                                  AND {field} IS NOT NULL AND {field} > 0
                                ORDER BY {field}
                            """).fetchall()
                            
                            if len(extreme_vals) >= 2:
                                lowest = extreme_vals[0]
                                highest = extreme_vals[-1]
                                print(f"    最低: {lowest[1] or lowest[0]} ({lowest[2]:.2f})")
                                print(f"    最高: {highest[1] or highest[0]} ({highest[2]:.2f})")
                
                # 成交量和市值分析
                volume_fields = [field for field in ['vol', 'amount', 'market_cap', 'turnover_rate'] if field in available_columns]
                if volume_fields:
                    print(f"\n📊 成交量与市值分析:")
                    
                    for field in volume_fields:
                        field_name = {
                            'vol': '成交量(万股)', 
                            'amount': '成交金额(万元)', 
                            'market_cap': '总市值(万元)',
                            'turnover_rate': '换手率(%)'
                        }.get(field, field)
                        
                        vol_stats = conn.execute(f"""
                            SELECT 
                                AVG({field}) as avg_val,
                                SUM({field}) as total_val,
                                COUNT({field}) as valid_count
                            FROM sw_industry_daily
                            WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
                              AND {field} IS NOT NULL AND {field} > 0
                        """).fetchone()
                        
                        if vol_stats[2] > 0:
                            if field == 'turnover_rate':
                                print(f"  {field_name}: 平均{vol_stats[0]:.3f}% (有效数据{vol_stats[2]}个)")
                            else:
                                print(f"  {field_name}: 平均{vol_stats[0]:,.0f}, 总计{vol_stats[1]:,.0f} (有效数据{vol_stats[2]}个)")
                
                # 历史波动性分析
                if 'pct_change' in available_columns:
                    print(f"\n📈 历史波动性分析 (近30个交易日):")
                    
                    # SQLite不支持STDDEV，需要手动计算标准差
                    volatility_query = """
                        WITH recent_data AS (
                            SELECT 
                                ts_code,
                                name,
                                pct_change,
                                AVG(pct_change) OVER (PARTITION BY ts_code) as avg_pct
                            FROM sw_industry_daily
                            WHERE trade_date >= (
                                SELECT date(MAX(trade_date), '-30 days')
                                FROM sw_industry_daily
                            )
                            AND pct_change IS NOT NULL
                        ),
                        variance_calc AS (
                            SELECT 
                                ts_code,
                                name,
                                pct_change,
                                avg_pct,
                                (pct_change - avg_pct) * (pct_change - avg_pct) as squared_diff
                            FROM recent_data
                        )
                        SELECT 
                            ts_code,
                            MAX(name) as name,
                            AVG(pct_change) as avg_return,
                            SQRT(AVG(squared_diff)) as volatility,
                            MIN(pct_change) as min_return,
                            MAX(pct_change) as max_return,
                            COUNT(*) as days_count
                        FROM variance_calc
                        GROUP BY ts_code
                        HAVING COUNT(*) >= 20
                        ORDER BY volatility DESC
                        LIMIT 10
                    """
                    
                    volatility_stats = conn.execute(volatility_query).fetchall()
                    
                    if volatility_stats:
                        print("  波动性最高的行业 TOP10:")
                        for ts_code, name, avg_return, volatility, min_return, max_return, days in volatility_stats:
                            print(f"    {name or ts_code}: 波动率{volatility:.2f}%, 平均收益{avg_return:+.2f}% (范围{min_return:.2f}%~{max_return:.2f}%)")
                
                # 提供详细分析选项
                print(f"\n🔍 详细分析选项:")
                print("1. 查看特定行业详情")
                print("2. 行业相关性分析")
                print("3. 估值历史趋势")
                print("4. 导出分析数据")
                print("0. 返回")
                
                detail_choice = input("\n请输入选择 (0-4): ").strip()
                
                if detail_choice == "1":
                    analyze_specific_sw_industry(cache, conn)
                elif detail_choice == "2":
                    analyze_sw_industry_correlation(cache, conn)
                elif detail_choice == "3":
                    analyze_sw_valuation_trend(cache, conn)
                elif detail_choice == "4":
                    export_sw_industry_analysis(cache, conn)
                
            else:
                print("❌ 无申万行业指数数据")
                
        except Exception as e:
            print(f"❌ 申万行业指数分析失败: {e}")
            import traceback
            traceback.print_exc()


def analyze_specific_sw_industry(cache, conn):
    """分析特定申万行业"""
    print("\n🔍 特定行业详细分析")
    print("-" * 40)
    
    # 显示可选行业列表
    industries = conn.execute("""
        SELECT DISTINCT ts_code, name
        FROM sw_industry_daily
        WHERE name IS NOT NULL
        ORDER BY ts_code
    """).fetchall()
    
    if not industries:
        print("❌ 无可用行业数据")
        return
    
    print("可选行业:")
    for i, (ts_code, name) in enumerate(industries[:20], 1):
        print(f"  {i}. {ts_code}: {name}")
    if len(industries) > 20:
        print(f"  ... 还有 {len(industries) - 20} 个行业")
    
    # 用户选择
    choice_input = input(f"\n请输入行业编号(1-{min(20, len(industries))})或直接输入行业代码: ").strip()
    
    try:
        if choice_input.isdigit() and 1 <= int(choice_input) <= min(20, len(industries)):
            selected_code = industries[int(choice_input) - 1][0]
        else:
            selected_code = choice_input.upper()
            
        # 验证代码是否存在
        industry_info = conn.execute("""
            SELECT DISTINCT ts_code, name
            FROM sw_industry_daily
            WHERE ts_code = ?
        """, (selected_code,)).fetchone()
        
        if not industry_info:
            print(f"❌ 未找到行业代码: {selected_code}")
            return
            
        ts_code, name = industry_info
        print(f"\n📊 {name} ({ts_code}) 详细分析")
        print("=" * 50)
        
        # 基础统计
        basic_stats = conn.execute("""
            SELECT 
                COUNT(*) as record_count,
                MIN(trade_date) as start_date,
                MAX(trade_date) as end_date,
                AVG(close) as avg_close,
                MIN(close) as min_close,
                MAX(close) as max_close
            FROM sw_industry_daily
            WHERE ts_code = ?
        """, (ts_code,)).fetchone()
        
        print(f"📅 数据时间: {basic_stats[1]} ~ {basic_stats[2]} ({basic_stats[0]}个交易日)")
        print(f"💰 价格统计: 平均{basic_stats[3]:.2f}, 范围{basic_stats[4]:.2f}~{basic_stats[5]:.2f}")
        
        # 最新数据
        latest_data = conn.execute("""
            SELECT trade_date, open, high, low, close, vol, amount, pct_change, pe, pb, ps, market_cap, turnover_rate
            FROM sw_industry_daily
            WHERE ts_code = ?
            ORDER BY trade_date DESC
            LIMIT 1
        """, (ts_code,)).fetchone()
        
        if latest_data:
            print(f"\n📈 最新数据 ({latest_data[0]}):")
            print(f"  开盘: {latest_data[1]:.2f}, 最高: {latest_data[2]:.2f}, 最低: {latest_data[3]:.2f}, 收盘: {latest_data[4]:.2f}")
            if latest_data[7] is not None:
                print(f"  涨跌幅: {latest_data[7]:+.2f}%")
            if latest_data[5] is not None:
                print(f"  成交量: {latest_data[5]:,.0f}万股")
            if latest_data[6] is not None:
                print(f"  成交金额: {latest_data[6]:,.0f}万元")
            
            # 估值数据
            valuation_info = []
            if latest_data[8] is not None and latest_data[8] > 0:
                valuation_info.append(f"PE: {latest_data[8]:.2f}")
            if latest_data[9] is not None and latest_data[9] > 0:
                valuation_info.append(f"PB: {latest_data[9]:.2f}")
            if latest_data[10] is not None and latest_data[10] > 0:
                valuation_info.append(f"PS: {latest_data[10]:.2f}")
            if latest_data[11] is not None:
                valuation_info.append(f"市值: {latest_data[11]:,.0f}万元")
            if latest_data[12] is not None:
                valuation_info.append(f"换手率: {latest_data[12]:.3f}%")
                
            if valuation_info:
                print(f"  估值指标: {', '.join(valuation_info)}")
        
        # 近期表现
        recent_performance = conn.execute("""
            SELECT trade_date, close, pct_change
            FROM sw_industry_daily
            WHERE ts_code = ?
            ORDER BY trade_date DESC
            LIMIT 10
        """, (ts_code,)).fetchall()
        
        if len(recent_performance) > 1:
            print(f"\n📊 近期表现 (最近10个交易日):")
            for trade_date, close, pct_change in recent_performance:
                change_str = f"{pct_change:+.2f}%" if pct_change is not None else "N/A"
                print(f"  {trade_date}: {close:.2f} ({change_str})")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


def analyze_sw_industry_correlation(cache, conn):
    """申万行业相关性分析"""
    print("\n🔗 申万行业相关性分析")
    print("-" * 40)
    
    try:
        # 获取最近60个交易日的数据进行相关性分析
        correlation_data = conn.execute("""
            SELECT ts_code, name, trade_date, pct_change
            FROM sw_industry_daily
            WHERE trade_date >= (
                SELECT date(MAX(trade_date), '-60 days')
                FROM sw_industry_daily
            )
            AND pct_change IS NOT NULL
            ORDER BY ts_code, trade_date
        """).fetchall()
        
        if len(correlation_data) < 100:  # 数据量太少
            print("❌ 数据量不足，无法进行相关性分析")
            return
        
        # 转换为DataFrame进行分析
        df = pd.DataFrame(correlation_data, columns=['ts_code', 'name', 'trade_date', 'pct_change'])
        
        # 透视表：行业为列，日期为行
        pivot_df = df.pivot_table(index='trade_date', columns='ts_code', values='pct_change')
        
        # 计算相关性矩阵
        correlation_matrix = pivot_df.corr()
        
        print(f"📊 基于最近60个交易日数据，计算了{len(pivot_df.columns)}个行业的相关性")
        
        # 找出相关性最高的行业对
        correlation_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if not pd.isna(corr_value):
                    industry1 = correlation_matrix.columns[i]
                    industry2 = correlation_matrix.columns[j]
                    correlation_pairs.append((abs(corr_value), corr_value, industry1, industry2))
        
        correlation_pairs.sort(reverse=True)
        
        # 获取行业名称映射
        name_mapping = dict(df[['ts_code', 'name']].drop_duplicates().values)
        
        print(f"\n🔗 相关性最高的行业对 TOP10:")
        for i, (abs_corr, corr, ind1, ind2) in enumerate(correlation_pairs[:10], 1):
            name1 = name_mapping.get(ind1, ind1)
            name2 = name_mapping.get(ind2, ind2)
            print(f"  {i}. {name1} vs {name2}: {corr:.3f}")
        
        print(f"\n📉 负相关性最强的行业对 TOP5:")
        negative_pairs = [pair for pair in correlation_pairs if pair[1] < 0]
        negative_pairs.sort(key=lambda x: x[1])  # 按相关系数升序排列
        
        for i, (abs_corr, corr, ind1, ind2) in enumerate(negative_pairs[:5], 1):
            name1 = name_mapping.get(ind1, ind1)
            name2 = name_mapping.get(ind2, ind2)
            print(f"  {i}. {name1} vs {name2}: {corr:.3f}")
            
    except Exception as e:
        print(f"❌ 相关性分析失败: {e}")


def analyze_sw_valuation_trend(cache, conn):
    """申万行业估值历史趋势分析"""
    print("\n📈 申万行业估值历史趋势")
    print("-" * 40)
    
    try:
        # 检查估值字段可用性
        columns_info = conn.execute("PRAGMA table_info(sw_industry_daily)").fetchall()
        available_columns = [col[1] for col in columns_info]
        valuation_fields = [field for field in ['pe', 'pb', 'ps'] if field in available_columns]
        
        if not valuation_fields:
            print("❌ 无可用估值字段")
            return
        
        print(f"📊 可分析估值指标: {', '.join(valuation_fields)}")
        
        # 选择估值指标
        if len(valuation_fields) == 1:
            selected_field = valuation_fields[0]
        else:
            print("\n请选择估值指标:")
            for i, field in enumerate(valuation_fields, 1):
                field_name = {'pe': 'PE市盈率', 'pb': 'PB市净率', 'ps': 'PS市销率'}[field]
                print(f"  {i}. {field_name} ({field})")
            
            choice = input(f"请输入选择 (1-{len(valuation_fields)}): ").strip()
            try:
                selected_field = valuation_fields[int(choice) - 1]
            except (ValueError, IndexError):
                print("❌ 无效选择")
                return
        
        field_name = {'pe': 'PE市盈率', 'pb': 'PB市净率', 'ps': 'PS市销率'}[selected_field]
        
        # 获取历史估值数据趋势
        trend_data = conn.execute(f"""
            SELECT 
                substr(trade_date, 1, 7) as month,
                AVG({selected_field}) as avg_valuation,
                MIN({selected_field}) as min_valuation,
                MAX({selected_field}) as max_valuation,
                COUNT({selected_field}) as valid_count,
                COUNT(DISTINCT ts_code) as industry_count
            FROM sw_industry_daily
            WHERE {selected_field} IS NOT NULL 
              AND {selected_field} > 0
              AND trade_date >= (
                  SELECT date(MAX(trade_date), '-12 months')
                  FROM sw_industry_daily
              )
            GROUP BY substr(trade_date, 1, 7)
            ORDER BY month
        """, ).fetchall()
        
        if trend_data:
            print(f"\n📈 {field_name}月度趋势 (最近12个月):")
            print("  月份      平均值    最小值    最大值    有效数据/行业数")
            print("-" * 60)
            
            for month, avg_val, min_val, max_val, valid_count, industry_count in trend_data:
                print(f"  {month}    {avg_val:7.2f}   {min_val:7.2f}   {max_val:7.2f}   {valid_count:4d}/{industry_count:2d}")
        
        # 当前估值分位数分析
        current_valuation = conn.execute(f"""
            SELECT 
                ts_code,
                name,
                {selected_field},
                RANK() OVER (ORDER BY {selected_field}) as rank_asc,
                RANK() OVER (ORDER BY {selected_field} DESC) as rank_desc,
                COUNT(*) OVER () as total_count
            FROM sw_industry_daily
            WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
              AND {selected_field} IS NOT NULL 
              AND {selected_field} > 0
            ORDER BY {selected_field}
        """).fetchall()
        
        if current_valuation:
            total_industries = len(current_valuation)
            print(f"\n📊 当前{field_name}分布 (共{total_industries}个行业):")
            
            # 显示估值极端值
            print("  估值最低的5个行业:")
            for ts_code, name, valuation, rank_asc, rank_desc, total in current_valuation[:5]:
                percentile = (rank_asc / total) * 100
                print(f"    {name or ts_code}: {valuation:.2f} (第{percentile:.0f}分位数)")
            
            print("  估值最高的5个行业:")
            for ts_code, name, valuation, rank_asc, rank_desc, total in current_valuation[-5:]:
                percentile = (rank_asc / total) * 100
                print(f"    {name or ts_code}: {valuation:.2f} (第{percentile:.0f}分位数)")
            
            # 估值分布统计
            low_count = sum(1 for _, _, val, _, _, _ in current_valuation if val <= current_valuation[len(current_valuation)//4][2])
            high_count = sum(1 for _, _, val, _, _, _ in current_valuation if val >= current_valuation[len(current_valuation)*3//4][2])
            
            print(f"\n📊 估值分布:")
            print(f"  低估值行业 (≤25分位数): {low_count} 个")
            print(f"  高估值行业 (≥75分位数): {high_count} 个")
            print(f"  中等估值行业: {total_industries - low_count - high_count} 个")
            
    except Exception as e:
        print(f"❌ 估值趋势分析失败: {e}")


def export_sw_industry_analysis(cache, conn):
    """导出申万行业分析数据"""
    print("\n📤 导出申万行业分析数据")
    print("-" * 40)
    
    try:
        # 创建导出目录
        export_dir = "/Users/<USER>/Documents/quant/demo/exports"
        os.makedirs(export_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出最新行业数据
        latest_data = conn.execute("""
            SELECT ts_code, name, trade_date, open, high, low, close, vol, amount, 
                   pct_change, pe, pb, ps, market_cap, turnover_rate
            FROM sw_industry_daily
            WHERE trade_date = (SELECT MAX(trade_date) FROM sw_industry_daily)
            ORDER BY ts_code
        """).fetchall()
        
        if latest_data:
            df_latest = pd.DataFrame(latest_data, columns=[
                'ts_code', 'name', 'trade_date', 'open', 'high', 'low', 'close', 
                'vol', 'amount', 'pct_change', 'pe', 'pb', 'ps', 'market_cap', 'turnover_rate'
            ])
            
            latest_file = f"{export_dir}/sw_industry_latest_{timestamp}.csv"
            df_latest.to_csv(latest_file, index=False, encoding='utf-8-sig')
            print(f"✅ 最新行业数据已导出: {latest_file}")
        
        # 导出近期表现数据
        recent_data = conn.execute("""
            SELECT ts_code, name, trade_date, close, pct_change
            FROM sw_industry_daily
            WHERE trade_date >= (
                SELECT date(MAX(trade_date), '-30 days')
                FROM sw_industry_daily
            )
            ORDER BY ts_code, trade_date
        """).fetchall()
        
        if recent_data:
            df_recent = pd.DataFrame(recent_data, columns=[
                'ts_code', 'name', 'trade_date', 'close', 'pct_change'
            ])
            
            recent_file = f"{export_dir}/sw_industry_recent_{timestamp}.csv"
            df_recent.to_csv(recent_file, index=False, encoding='utf-8-sig')
            print(f"✅ 近期表现数据已导出: {recent_file}")
        
        # 导出估值统计数据
        valuation_stats = conn.execute("""
            SELECT 
                ts_code,
                name,
                AVG(pe) as avg_pe,
                AVG(pb) as avg_pb,
                AVG(ps) as avg_ps,
                AVG(market_cap) as avg_market_cap,
                AVG(turnover_rate) as avg_turnover_rate,
                COUNT(*) as data_count
            FROM sw_industry_daily
            WHERE trade_date >= (
                SELECT date(MAX(trade_date), '-90 days')
                FROM sw_industry_daily
            )
            GROUP BY ts_code, name
            ORDER BY ts_code
        """).fetchall()
        
        if valuation_stats:
            df_valuation = pd.DataFrame(valuation_stats, columns=[
                'ts_code', 'name', 'avg_pe', 'avg_pb', 'avg_ps', 
                'avg_market_cap', 'avg_turnover_rate', 'data_count'
            ])
            
            valuation_file = f"{export_dir}/sw_industry_valuation_stats_{timestamp}.csv"
            df_valuation.to_csv(valuation_file, index=False, encoding='utf-8-sig')
            print(f"✅ 估值统计数据已导出: {valuation_file}")
        
        print(f"\n📂 所有导出文件保存在: {export_dir}")
        
    except Exception as e:
        print(f"❌ 数据导出失败: {e}")


if __name__ == "__main__":
    analyze_cached_data()

# -*- coding: utf-8 -*-
"""
股票信息查看器
Stock Information Viewer

功能：查看单只股票的全部信息，包括：
- 基本信息
- 价格历史数据
- 财务指标
- 估值数据
- 业绩预告
- 数据可视化
"""

import pandas as pd
import numpy as np
from data_cache_manager import DataCache
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime, timedelta
import warnings
import sys
import io
import os

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 尝试导入mplfinance库（如果没有则使用自定义K线图）
try:
    import mplfinance as mpf
    HAS_MPLFINANCE = True
except ImportError:
    HAS_MPLFINANCE = False
    print("📝 提示: 安装 mplfinance 库可获得更好的K线图效果: pip install mplfinance")

# 设置标准输出编码为UTF-8
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

warnings.filterwarnings('ignore')


class StockInfoViewer:
    """股票信息查看器"""
    
    def __init__(self):
        """初始化"""
        self.cache = DataCache()
        
    def get_stock_info(self, ts_code):
        """获取股票完整信息"""
        print(f"📊 正在获取 {ts_code} 的完整信息...")
        print("=" * 80)
        
        # 1. 基本信息
        self.show_basic_info(ts_code)
        
        # 2. 价格数据摘要
        self.show_price_summary(ts_code)
        
        # 3. 基本面数据
        self.show_fundamental_data(ts_code)
        
        # 4. 扩展数据
        self.show_extended_data(ts_code)
        
        # 5. 技术指标摘要
        self.show_technical_summary(ts_code)
        
        # 6. 询问是否进行详细分析
        choice = input(f"\n是否对 {ts_code} 进行详细分析和可视化? (y/n): ").strip().lower()
        if choice == 'y':
            self.detailed_analysis(ts_code)
    
    def show_basic_info(self, ts_code):
        """显示股票基本信息"""
        print("📋 基本信息")
        print("-" * 40)
        
        # 获取股票基本信息
        stock_basic = self.cache.get_stock_basic()
        if stock_basic is not None and not stock_basic.empty:
            stock_info = stock_basic[stock_basic['ts_code'] == ts_code]
            
            if not stock_info.empty:
                info = stock_info.iloc[0]
                print(f"  股票代码: {info.get('ts_code', 'N/A')}")
                print(f"  股票简称: {info.get('name', 'N/A')}")
                print(f"  股票全称: {info.get('symbol', 'N/A')}")
                print(f"  所属行业: {info.get('industry', 'N/A')}")
                print(f"  所属地区: {info.get('area', 'N/A')}")
                print(f"  上市板块: {info.get('market', 'N/A')}")
                print(f"  上市日期: {info.get('list_date', 'N/A')}")
                
                # 计算上市天数
                if pd.notna(info.get('list_date')):
                    list_date = pd.to_datetime(info['list_date'], format='%Y%m%d')
                    days_listed = (datetime.now() - list_date).days
                    years_listed = days_listed / 365.25
                    print(f"  上市时长: {days_listed:,} 天 ({years_listed:.1f} 年)")
            else:
                print(f"  ❌ 未找到 {ts_code} 的基本信息")
        else:
            print("  ❌ 无法获取股票基本信息")
    
    def show_price_summary(self, ts_code):
        """显示价格数据摘要"""
        print(f"\n📈 价格数据摘要")
        print("-" * 40)
        
        # 获取价格数据
        price_data = self.cache.get_stock_daily(ts_code)
        
        if price_data is not None and not price_data.empty:
            price_data['trade_date'] = pd.to_datetime(price_data['trade_date'], format='%Y%m%d')
            price_data = price_data.sort_values('trade_date')
            
            latest = price_data.iloc[-1]
            earliest = price_data.iloc[0]
            
            print(f"  数据时间范围: {earliest['trade_date'].strftime('%Y-%m-%d')} ~ {latest['trade_date'].strftime('%Y-%m-%d')}")
            print(f"  数据记录数: {len(price_data):,} 条")
            
            print(f"\n  最新价格信息 ({latest['trade_date'].strftime('%Y-%m-%d')}):")
            print(f"    收盘价: ¥{latest['close']:.2f}")
            print(f"    涨跌额: {latest['change']:+.2f}")
            print(f"    涨跌幅: {latest['pct_chg']:+.2f}%")
            print(f"    成交量: {latest['vol']:,.0f} 手")
            print(f"    成交额: ¥{latest['amount']:,.0f} 万元")
            
            # 价格统计
            print(f"\n  价格统计:")
            print(f"    历史最高价: ¥{price_data['high'].max():.2f}")
            print(f"    历史最低价: ¥{price_data['low'].min():.2f}")
            print(f"    平均收盘价: ¥{price_data['close'].mean():.2f}")
            print(f"    价格波动率: {price_data['pct_chg'].std():.2f}%")
            
            # 近期表现
            if len(price_data) >= 30:
                recent_30d = price_data.tail(30)
                month_return = (latest['close'] / recent_30d.iloc[0]['close'] - 1) * 100
                print(f"    近30日涨跌幅: {month_return:+.2f}%")
            
            if len(price_data) >= 252:  # 一年大约252个交易日
                recent_1y = price_data.tail(252)
                year_return = (latest['close'] / recent_1y.iloc[0]['close'] - 1) * 100
                print(f"    近1年涨跌幅: {year_return:+.2f}%")
            
        else:
            print(f"  ❌ 未找到 {ts_code} 的价格数据")
    
    def show_fundamental_data(self, ts_code):
        """显示基本面数据"""
        print(f"\n💼 基本面数据")
        print("-" * 40)
        
        # 1. 财务指标
        print("📊 财务指标:")
        fina_data = self.cache.get_stock_fina_indicator(ts_code)
        
        if fina_data is not None and not fina_data.empty:
            fina_data = fina_data.sort_values('end_date')
            latest_fina = fina_data.iloc[-1]
            
            print(f"  最新财务数据 ({latest_fina['end_date']}):")
            print(f"    每股收益(EPS): {latest_fina.get('eps', 'N/A')}")
            print(f"    每股净资产(BPS): {latest_fina.get('bps', 'N/A')}")
            print(f"    净资产收益率(ROE): {latest_fina.get('roe', 'N/A')}%")
            print(f"    总资产收益率(ROA): {latest_fina.get('roa', 'N/A')}%")
            print(f"    净利润率: {latest_fina.get('netprofit_margin', 'N/A')}%")
            print(f"    毛利润率: {latest_fina.get('grossprofit_margin', 'N/A')}%")
            print(f"    流动比率: {latest_fina.get('current_ratio', 'N/A')}")
            print(f"    速动比率: {latest_fina.get('quick_ratio', 'N/A')}")
            print(f"    资产负债率: {latest_fina.get('debt_to_assets', 'N/A')}%")
            
            # 财务数据历史记录数
            print(f"  财务数据记录: {len(fina_data)} 个报告期")
        else:
            print("  ❌ 暂无财务指标数据")
        
        # 2. 估值指标
        print(f"\n💰 估值指标:")
        basic_data = self.cache.get_stock_daily_basic(ts_code=ts_code)
        
        if basic_data is not None and not basic_data.empty:
            basic_data['trade_date'] = pd.to_datetime(basic_data['trade_date'], format='%Y%m%d')
            basic_data = basic_data.sort_values('trade_date')
            latest_basic = basic_data.iloc[-1]
            
            print(f"  最新估值数据 ({latest_basic['trade_date'].strftime('%Y-%m-%d')}):")
            print(f"    市盈率(PE): {latest_basic.get('pe', 'N/A')}")
            print(f"    市盈率TTM: {latest_basic.get('pe_ttm', 'N/A')}")
            print(f"    市净率(PB): {latest_basic.get('pb', 'N/A')}")
            print(f"    市销率(PS): {latest_basic.get('ps', 'N/A')}")
            print(f"    股息率: {latest_basic.get('dv_ratio', 'N/A')}%")
            print(f"    总市值: ¥{latest_basic.get('total_mv', 'N/A'):,.0f} 万元")
            print(f"    流通市值: ¥{latest_basic.get('circ_mv', 'N/A'):,.0f} 万元")
            print(f"    换手率: {latest_basic.get('turnover_rate', 'N/A')}%")
            
            print(f"  估值数据记录: {len(basic_data)} 个交易日")
        else:
            print("  ❌ 暂无估值数据")
        
        # 3. 业绩预告
        print(f"\n📋 业绩预告:")
        forecast_data = self.cache.get_stock_forecast(ts_code)
        
        if forecast_data is not None and not forecast_data.empty:
            forecast_data = forecast_data.sort_values('end_date')
            latest_forecast = forecast_data.iloc[-1]
            
            print(f"  最新业绩预告 ({latest_forecast['end_date']}):")
            print(f"    预告类型: {latest_forecast.get('type', 'N/A')}")
            if pd.notna(latest_forecast.get('p_change_min')) and pd.notna(latest_forecast.get('p_change_max')):
                print(f"    净利润变动: {latest_forecast.get('p_change_min', 'N/A')}% ~ {latest_forecast.get('p_change_max', 'N/A')}%")
            summary = latest_forecast.get('summary', '')
            if summary and len(str(summary)) > 5:
                print(f"    业绩说明: {str(summary)[:150]}...")
            
            print(f"  业绩预告记录: {len(forecast_data)} 条")
        else:
            print("  ❌ 暂无业绩预告数据")
    
    def show_technical_summary(self, ts_code):
        """显示技术指标摘要"""
        print(f"\n📊 技术指标摘要")
        print("-" * 40)
        
        price_data = self.cache.get_stock_daily(ts_code)
        
        if price_data is not None and not price_data.empty:
            price_data['trade_date'] = pd.to_datetime(price_data['trade_date'], format='%Y%m%d')
            price_data = price_data.sort_values('trade_date').reset_index(drop=True)
            
            # 计算移动平均线
            if len(price_data) >= 5:
                price_data['ma5'] = price_data['close'].rolling(window=5).mean()
            if len(price_data) >= 10:
                price_data['ma10'] = price_data['close'].rolling(window=10).mean()
            if len(price_data) >= 20:
                price_data['ma20'] = price_data['close'].rolling(window=20).mean()
            if len(price_data) >= 60:
                price_data['ma60'] = price_data['close'].rolling(window=60).mean()
            
            latest = price_data.iloc[-1]
            
            print(f"  移动平均线:")
            if 'ma5' in price_data.columns:
                print(f"    5日均线: ¥{latest.get('ma5', 'N/A'):.2f}")
            if 'ma10' in price_data.columns:
                print(f"    10日均线: ¥{latest.get('ma10', 'N/A'):.2f}")
            if 'ma20' in price_data.columns:
                print(f"    20日均线: ¥{latest.get('ma20', 'N/A'):.2f}")
            if 'ma60' in price_data.columns:
                print(f"    60日均线: ¥{latest.get('ma60', 'N/A'):.2f}")
            
            # RSI指标 (简化版)
            if len(price_data) >= 14:
                delta = price_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                latest_rsi = rsi.iloc[-1]
                
                print(f"\n  技术指标:")
                print(f"    RSI(14): {latest_rsi:.2f}")
                
                if latest_rsi > 70:
                    print(f"    RSI信号: 超买区域")
                elif latest_rsi < 30:
                    print(f"    RSI信号: 超卖区域")
                else:
                    print(f"    RSI信号: 正常区域")
            
            # 支撑阻力位 (简化版)
            if len(price_data) >= 20:
                recent_data = price_data.tail(20)
                resistance = recent_data['high'].max()
                support = recent_data['low'].min()
                
                print(f"\n  支撑阻力位(近20日):")
                print(f"    阻力位: ¥{resistance:.2f}")
                print(f"    支撑位: ¥{support:.2f}")
                print(f"    当前位置: {((latest['close'] - support) / (resistance - support) * 100):.1f}%")
        else:
            print("  ❌ 暂无价格数据，无法计算技术指标")
    
    def show_extended_data(self, ts_code):
        """显示扩展数据"""
        print(f"\n🔍 扩展数据")
        print("-" * 40)
        
        # 1. 复权因子数据
        print("📊 复权因子:")
        adj_data = self.cache.get_stock_adj_factor(ts_code)
        
        if adj_data is not None and not adj_data.empty:
            adj_data = adj_data.sort_values('trade_date')
            latest_adj = adj_data.iloc[-1]
            earliest_adj = adj_data.iloc[0]
            
            print(f"  数据记录: {len(adj_data)} 个交易日")
            print(f"  时间范围: {earliest_adj['trade_date']} ~ {latest_adj['trade_date']}")
            print(f"  最新复权因子: {latest_adj['adj_factor']:.4f}")
            print(f"  累计复权倍数: {latest_adj['adj_factor'] / earliest_adj['adj_factor']:.2f}倍")
            
            # 复权因子变化较大的日期
            adj_data['factor_change'] = adj_data['adj_factor'].pct_change().abs()
            big_changes = adj_data[adj_data['factor_change'] > 0.05].tail(3)  # 变化超过5%的最近3次
            if not big_changes.empty:
                print("  近期复权因子大幅变化:")
                for _, row in big_changes.iterrows():
                    print(f"    {row['trade_date']}: {row['adj_factor']:.4f} (变化{row['factor_change']*100:.1f}%)")
        else:
            print("  ❌ 暂无复权因子数据")
        
        # 2. 分红送转数据
        print(f"\n💰 分红送转:")
        dividend_data = self.cache.get_stock_dividend(ts_code)
        
        if dividend_data is not None and not dividend_data.empty:
            dividend_data = dividend_data.sort_values('ann_date')
            
            print(f"  分红记录: {len(dividend_data)} 次")
            
            # 统计分红情况
            cash_div_count = len(dividend_data[dividend_data['cash_div'] > 0])
            stock_div_count = len(dividend_data[dividend_data['stk_div'] > 0])
            bonus_count = len(dividend_data[dividend_data['stk_bo_rate'] > 0])
            
            print(f"  现金分红: {cash_div_count} 次")
            print(f"  送股: {stock_div_count} 次")
            print(f"  转股: {bonus_count} 次")
            
            # 最近几次分红
            recent_div = dividend_data.tail(3)
            print("  最近分红记录:")
            for _, row in recent_div.iterrows():
                div_info = []
                if row['cash_div'] > 0:
                    div_info.append(f"现金{row['cash_div']:.3f}元")
                if row['stk_div'] > 0:
                    div_info.append(f"送股{row['stk_div']:.3f}")
                if row['stk_bo_rate'] > 0:
                    div_info.append(f"转股{row['stk_bo_rate']:.3f}")
                
                div_str = ', '.join(div_info) if div_info else '无分红'
                print(f"    {row['ann_date']}: {div_str} (除权日:{row.get('ex_date', 'N/A')})")
        else:
            print("  ❌ 暂无分红送转数据")
        
        # 3. 限售解禁数据
        print(f"\n🔓 限售解禁:")
        float_data = self.cache.get_stock_share_float(ts_code)
        
        if float_data is not None and not float_data.empty:
            float_data = float_data.sort_values('ann_date')
            
            print(f"  解禁记录: {len(float_data)} 次")
            
            # 解禁总规模
            total_float = float_data['float_share'].sum()
            print(f"  累计解禁: {total_float/10000:.0f} 万股")
            
            # 最大解禁事件
            max_float = float_data.loc[float_data['float_share'].idxmax()]
            print(f"  最大解禁: {max_float['float_share']/10000:.0f}万股 ({max_float['float_ratio']:.2f}%) {max_float['float_date']}")
            
            # 即将解禁
            future_float = float_data[float_data['float_date'] >= datetime.now().strftime('%Y-%m-%d')]
            if not future_float.empty:
                upcoming = future_float.head(2)
                print("  即将解禁:")
                for _, row in upcoming.iterrows():
                    print(f"    {row['float_date']}: {row['float_share']/10000:.0f}万股 ({row['float_ratio']:.2f}%)")
        else:
            print("  ❌ 暂无限售解禁数据")
        
        # 4. 股东户数数据
        print(f"\n👥 股东户数:")
        holder_data = self.cache.get_stock_holder_number(ts_code)
        
        if holder_data is not None and not holder_data.empty:
            holder_data = holder_data.sort_values('end_date')
            latest_holder = holder_data.iloc[-1]
            
            print(f"  数据记录: {len(holder_data)} 个报告期")
            print(f"  最新股东户数: {latest_holder['holder_num']:,} 户 ({latest_holder['end_date']})")
            
            if len(holder_data) >= 2:
                prev_holder = holder_data.iloc[-2]
                change = (latest_holder['holder_num'] - prev_holder['holder_num'])
                change_pct = (latest_holder['holder_num'] / prev_holder['holder_num'] - 1) * 100
                print(f"  环比变化: {change:+,} 户 ({change_pct:+.2f}%)")
            
            # 股东户数趋势
            if len(holder_data) >= 4:
                trend_data = holder_data.tail(4)
                trend_values = trend_data['holder_num'].tolist()
                
                increasing = sum(1 for i in range(1, len(trend_values)) if trend_values[i] > trend_values[i-1])
                trend_desc = "上升" if increasing >= 2 else "下降" if increasing <= 1 else "震荡"
                print(f"  近期趋势: {trend_desc}")
        else:
            print("  ❌ 暂无股东户数数据")
        
        # 5. 行业分类数据
        print(f"\n🏭 行业分类:")
        industry_data = self.cache.get_stock_industry_classify(ts_code)
        
        if industry_data is not None and not industry_data.empty:
            print(f"  分类记录: {len(industry_data)} 条")
            
            # 按级别分组显示
            for level in ['L1', 'L2', 'L3']:
                level_data = industry_data[industry_data['level'] == level]
                if not level_data.empty:
                    level_name = {'L1': '一级行业', 'L2': '二级行业', 'L3': '三级行业'}[level]
                    for _, row in level_data.iterrows():
                        print(f"  {level_name}: {row['industry_name']} ({row['industry_code']})")
        else:
            print("  ❌ 暂无行业分类数据")
        
        # 6. 申万行业指数数据 (如果是行业指数代码)
        if ts_code.endswith('.SW'):
            print(f"\n📈 申万行业指数数据:")
            self.show_sw_industry_data(ts_code)
    
    def show_sw_industry_data(self, ts_code):
        """显示申万行业指数扩展数据"""
        try:
            import sqlite3
            
            if not os.path.exists(self.cache.stock_db):
                print("  ❌ 股票数据库文件不存在")
                return
            
            with sqlite3.connect(self.cache.stock_db) as conn:
                # 获取申万行业指数数据
                sw_data = conn.execute("""
                    SELECT trade_date, open, high, low, close, vol, amount, pct_change,
                           pe, pb, ps, market_cap, turnover_rate, name
                    FROM sw_industry_daily
                    WHERE ts_code = ?
                    ORDER BY trade_date DESC
                    LIMIT 10
                """, (ts_code,)).fetchall()
                
                if sw_data:
                    latest = sw_data[0]
                    print(f"  行业名称: {latest[13] or '未知'}")
                    print(f"  数据记录: {len(sw_data)} 个交易日 (显示最近10天)")
                    print(f"  最新数据 ({latest[0]}):")
                    print(f"    价格: 开{latest[1]:.2f} 高{latest[2]:.2f} 低{latest[3]:.2f} 收{latest[4]:.2f}")
                    
                    if latest[7] is not None:
                        print(f"    涨跌幅: {latest[7]:+.2f}%")
                    
                    if latest[5] is not None:
                        print(f"    成交量: {latest[5]:,.0f}万股")
                    
                    if latest[6] is not None:
                        print(f"    成交金额: {latest[6]:,.0f}万元")
                    
                    # 估值指标
                    valuation_info = []
                    if latest[8] is not None and latest[8] > 0:
                        valuation_info.append(f"PE: {latest[8]:.2f}")
                    if latest[9] is not None and latest[9] > 0:
                        valuation_info.append(f"PB: {latest[9]:.2f}")
                    if latest[10] is not None and latest[10] > 0:
                        valuation_info.append(f"PS: {latest[10]:.2f}")
                    if latest[11] is not None:
                        valuation_info.append(f"市值: {latest[11]:,.0f}万元")
                    if latest[12] is not None:
                        valuation_info.append(f"换手率: {latest[12]:.3f}%")
                    
                    if valuation_info:
                        print(f"    估值指标: {', '.join(valuation_info)}")
                    
                    # 显示近期走势
                    if len(sw_data) > 1:
                        print(f"  近期走势:")
                        for i, data in enumerate(sw_data[1:6]):  # 显示后续5天
                            trade_date, _, _, _, close, _, _, pct_change = data[:8]
                            change_str = f"{pct_change:+.2f}%" if pct_change is not None else "N/A"
                            print(f"    {trade_date}: {close:.2f} ({change_str})")
                    
                    # 统计信息
                    total_count = conn.execute("""
                        SELECT COUNT(*) FROM sw_industry_daily WHERE ts_code = ?
                    """, (ts_code,)).fetchone()[0]
                    
                    date_range = conn.execute("""
                        SELECT MIN(trade_date), MAX(trade_date) 
                        FROM sw_industry_daily 
                        WHERE ts_code = ?
                    """, (ts_code,)).fetchone()
                    
                    print(f"  总数据量: {total_count} 个交易日")
                    print(f"  数据时间范围: {date_range[0]} ~ {date_range[1]}")
                    
                else:
                    print("  ❌ 暂无申万行业指数数据")
                    
        except Exception as e:
            print(f"  ❌ 申万行业指数数据获取失败: {e}")
    
    def detailed_analysis(self, ts_code):
        """详细分析和可视化"""
        print(f"\n🔍 {ts_code} 详细分析")
        print("=" * 60)
        
        print("请选择分析类型:")
        print("1. 价格走势图")
        print("2. 财务指标趋势")
        print("3. 估值分析")
        print("4. 扩展数据分析")
        print("5. 综合分析图表")
        print("6. 数据导出")
        print("0. 返回")
        
        choice = input("\n请输入选择 (0-6): ").strip()
        
        if choice == "1":
            self.plot_price_chart(ts_code)
        elif choice == "2":
            self.plot_financial_trends(ts_code)
        elif choice == "3":
            self.plot_valuation_analysis(ts_code)
        elif choice == "4":
            self.analyze_extended_details(ts_code)
        elif choice == "5":
            self.plot_comprehensive_analysis(ts_code)
        elif choice == "6":
            self.export_data(ts_code)
        elif choice == "0":
            return
        else:
            print("❌ 无效选择")
            
        # 询问是否继续分析
        if input("\n是否继续其他分析? (y/n): ").strip().lower() == 'y':
            self.detailed_analysis(ts_code)
    
    def plot_price_chart(self, ts_code):
        """绘制价格走势图（K线图）"""
        print(f"📈 绘制 {ts_code} K线图...")
        
        price_data = self.cache.get_stock_daily(ts_code)
        
        if price_data is None or price_data.empty:
            print("❌ 无价格数据")
            return
        
        price_data['trade_date'] = pd.to_datetime(price_data['trade_date'], format='%Y%m%d')
        price_data = price_data.sort_values('trade_date').reset_index(drop=True)
        
        # 显示时间范围选择菜单
        print("\n请选择时间范围:")
        print("1. 近5年")
        print("2. 近3年") 
        print("3. 近1年")
        print("4. 今年")
        print("5. 全部数据")
        
        time_choice = input("请输入选择 (1-5，默认为3): ").strip()
        if time_choice == "":
            time_choice = "3"  # 默认选择近1年
        
        # 根据选择的时间范围过滤数据
        if time_choice == "1":
            start_date = (datetime.now() - timedelta(days=5*365)).strftime('%Y%m%d')
        elif time_choice == "2":
            start_date = (datetime.now() - timedelta(days=3*365)).strftime('%Y%m%d')
        elif time_choice == "3":
            start_date = (datetime.now() - timedelta(days=1*365)).strftime('%Y%m%d')
        elif time_choice == "4":
            start_date = datetime.now().strftime('%Y%m%d')
        elif time_choice == "5":
            start_date = "19000101"  # 从最早数据开始
        else:
            print("❌ 无效的时间范围选择")
            return
        
        # 筛选时间范围内的数据
        price_data = price_data[price_data['trade_date'] >= pd.to_datetime(start_date, format='%Y%m%d')]
        
        if price_data.empty:
            print("❌ 选择的时间范围内没有可用的价格数据")
            return
        
        # 绘制K线图
        if HAS_MPLFINANCE:
            # 使用mplfinance绘制K线图
            import mplfinance as mpf
            
            # 设置K线图样式
            mpf_style = mpf.make_mpf_style(base_mpf_style='charles',
                                            rc={'font.sans-serif': ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']})
            
            # 绘制K线图
            mpf.plot(price_data,
                     type='candle',
                     style=mpf_style,
                     title=f"{ts_code} 价格走势图",
                     ylabel='价格',
                     volume=True,
                     mav=(5, 10, 20, 60),
                     savefig=f"{ts_code}_k线图.png")
            
            print(f"📊 K线图已保存为 {ts_code}_k线图.png")
        else:
            # 使用matplotlib自定义绘制K线图
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 绘制蜡烛图
            for i in range(len(price_data)):
                row = price_data.iloc[i]
                color = 'green' if row['close'] >= row['open'] else 'red'
                ax.add_patch(Rectangle((mdates.date2num(row['trade_date']) - 0.3, row['open']),
                                        0.6, row['close'] - row['open'],
                                        facecolor=color, edgecolor=color))
                
                # 绘制上下影线
                ax.plot([mdates.date2num(row['trade_date']), mdates.date2num(row['trade_date'])],
                        [row['low'], row['high']], color=color, linewidth=1.5)
            
            # 设置标题和标签
            ax.set_title(f"{ts_code} 价格走势图", fontsize=16)
            ax.set_xlabel("日期", fontsize=14)
            ax.set_ylabel("价格", fontsize=14)
            
            # 格式化日期显示
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            # 显示网格
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # 保存图像
            plt.tight_layout()
            plt.savefig(f"{ts_code}_价格走势图.png")
            plt.show()
            
            print(f"📊 价格走势图已保存为 {ts_code}_价格走势图.png")
    
    def plot_financial_trends(self, ts_code):
        """绘制财务指标趋势"""
        print(f"📈 绘制 {ts_code} 财务指标趋势...")
        
        # 获取财务指标数据
        fina_data = self.cache.get_stock_fina_indicator(ts_code)
        
        if fina_data is None or fina_data.empty:
            print("❌ 无财务数据")
            return
        
        fina_data = fina_data.sort_values('end_date')
        
        # 选择要绘制的财务指标
        indicators = ['eps', 'bps', 'roe', 'roa', 'netprofit_margin', 'grossprofit_margin']
        labels = {
            'eps': '每股收益(EPS)',
            'bps': '每股净资产(BPS)',
            'roe': '净资产收益率(ROE)',
            'roa': '总资产收益率(ROA)',
            'netprofit_margin': '净利润率',
            'grossprofit_margin': '毛利润率'
        }
        
        # 绘制每个指标的趋势图
        fig, axs = plt.subplots(len(indicators), 1, figsize=(10, 6), sharex=True)
        fig.suptitle(f"{ts_code} 财务指标趋势", fontsize=16)
        
        for i, indicator in enumerate(indicators):
            axs[i].plot(fina_data['end_date'], fina_data[indicator], marker='o')
            axs[i].set_ylabel(labels[indicator])
            axs[i].set_title(labels[indicator])
            axs[i].grid(True, linestyle='--', alpha=0.7)
        
        axs[-1].set_xlabel("报告期")
        
        # 自动旋转日期标签
        for label in axs[-1].get_xticklabels():
            label.set_rotation(45)
            label.set_ha("right")
        
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        plt.savefig(f"{ts_code}_财务指标趋势.png")
        plt.show()
        
        print(f"📊 财务指标趋势图已保存为 {ts_code}_财务指标趋势.png")
    
    def plot_valuation_analysis(self, ts_code):
        """绘制估值分析图"""
        print(f"📈 绘制 {ts_code} 估值分析图...")
        
        # 获取估值数据
        basic_data = self.cache.get_stock_daily_basic(ts_code)
        
        if basic_data is None or basic_data.empty:
            print("❌ 无估值数据")
            return
        
        basic_data = basic_data.sort_values('trade_date')
        
        # 绘制PE、PB、PS、总市值的时间序列图
        fig, axs = plt.subplots(4, 1, figsize=(10, 8), sharex=True)
        fig.suptitle(f"{ts_code} 估值分析", fontsize=16)
        
        axs[0].plot(basic_data['trade_date'], basic_data['pe'], marker='o', label='PE')
        axs[0].set_ylabel("市盈率(PE)")
        axs[0].set_title("市盈率(PE) 趋势")
        axs[0].grid(True, linestyle='--', alpha=0.7)
        
        axs[1].plot(basic_data['trade_date'], basic_data['pb'], marker='o', label='PB', color='orange')
        axs[1].set_ylabel("市净率(PB)")
        axs[1].set_title("市净率(PB) 趋势")
        axs[1].grid(True, linestyle='--', alpha=0.7)
        
        axs[2].plot(basic_data['trade_date'], basic_data['ps'], marker='o', label='PS', color='green')
        axs[2].set_ylabel("市销率(PS)")
        axs[2].set_title("市销率(PS) 趋势")
        axs[2].grid(True, linestyle='--', alpha=0.7)
        
        axs[3].plot(basic_data['trade_date'], basic_data['total_mv'], marker='o', label='总市值', color='red')
        axs[3].set_ylabel("总市值(万元)")
        axs[3].set_title("总市值(万元) 趋势")
        axs[3].grid(True, linestyle='--', alpha=0.7)
        
        plt.xlabel("交易日期")
        
        # 自动旋转日期标签
        for label in axs[3].get_xticklabels():
            label.set_rotation(45)
            label.set_ha("right")
        
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        plt.savefig(f"{ts_code}_估值分析图.png")
        plt.show()
        
        print(f"📊 估值分析图已保存为 {ts_code}_估值分析图.png")
    
    def analyze_extended_details(self, ts_code):
        """扩展数据详细分析"""
        print(f"\n🔍 {ts_code} 扩展数据详细分析")
        print("=" * 50)
        
        print("请选择扩展数据分析类型:")
        print("1. 复权因子分析")
        print("2. 分红送转分析")
        print("3. 股东结构分析")
        print("4. 申万行业指数分析") if ts_code.endswith('.SI') else None
        print("5. 综合扩展分析")
        print("0. 返回")
        
        choice = input("\n请输入选择: ").strip()
        
        if choice == "1":
            self.analyze_adj_factor_details(ts_code)
        elif choice == "2":
            self.analyze_dividend_details(ts_code)
        elif choice == "3":
            self.analyze_shareholder_details(ts_code)
        elif choice == "4" and ts_code.endswith('.SI'):
            self.analyze_sw_industry_details(ts_code)
        elif choice == "5":
            self.comprehensive_extended_analysis(ts_code)
        elif choice == "0":
            return
        else:
            print("❌ 无效选择")
    
    def analyze_sw_industry_details(self, ts_code):
        """申万行业指数详细分析"""
        print(f"\n🏭 申万行业指数详细分析: {ts_code}")
        print("=" * 60)
        
        try:
            import sqlite3
            
            if not os.path.exists(self.cache.stock_db):
                print("❌ 股票数据库文件不存在")
                return
            
            with sqlite3.connect(self.cache.stock_db) as conn:
                # 获取基础信息
                basic_info = conn.execute("""
                    SELECT 
                        COUNT(*) as total_records,
                        MIN(trade_date) as start_date,
                        MAX(trade_date) as end_date,
                        MAX(name) as industry_name
                    FROM sw_industry_daily
                    WHERE ts_code = ?
                """, (ts_code,)).fetchone()
                
                if basic_info[0] == 0:
                    print(f"❌ 未找到 {ts_code} 的申万行业数据")
                    return
                
                print(f"📊 基础信息:")
                print(f"  行业名称: {basic_info[3] or '未知'}")
                print(f"  数据记录: {basic_info[0]:,} 个交易日")
                print(f"  时间范围: {basic_info[1]} ~ {basic_info[2]}")
                
                # 价格表现分析
                print(f"\n📈 价格表现分析:")
                price_stats = conn.execute("""
                    SELECT 
                        AVG(close) as avg_price,
                        MIN(close) as min_price,
                        MAX(close) as max_price,
                        STDDEV(close) as price_volatility,
                        (MAX(close) / MIN(close) - 1) * 100 as total_return
                    FROM sw_industry_daily
                    WHERE ts_code = ?
                """, (ts_code,)).fetchone()
                
                if price_stats[0] is not None:
                    print(f"  平均价格: {price_stats[0]:.2f}")
                    print(f"  价格区间: {price_stats[1]:.2f} ~ {price_stats[2]:.2f}")
                    print(f"  价格波动率: {price_stats[3]:.2f}")
                    print(f"  历史累计收益: {price_stats[4]:.2f}%")
                
                # 涨跌幅统计
                print(f"\n📊 涨跌幅统计:")
                pct_stats = conn.execute("""
                    SELECT 
                        AVG(pct_change) as avg_return,
                        STDDEV(pct_change) as volatility,
                        MIN(pct_change) as max_loss,
                        MAX(pct_change) as max_gain,
                        SUM(CASE WHEN pct_change > 0 THEN 1 ELSE 0 END) as up_days,
                        SUM(CASE WHEN pct_change < 0 THEN 1 ELSE 0 END) as down_days,
                        COUNT(pct_change) as valid_days
                    FROM sw_industry_daily
                    WHERE ts_code = ? AND pct_change IS NOT NULL
                """, (ts_code,)).fetchone()
                
                if pct_stats[6] > 0:
                    up_ratio = (pct_stats[4] / pct_stats[6]) * 100
                    print(f"  平均日收益: {pct_stats[0]:+.3f}%")
                    print(f"  收益波动率: {pct_stats[1]:.3f}%")
                    print(f"  最大单日跌幅: {pct_stats[2]:.2f}%")
                    print(f"  最大单日涨幅: {pct_stats[3]:.2f}%")
                    print(f"  上涨天数比例: {up_ratio:.1f}% ({pct_stats[4]}/{pct_stats[6]})")
                
                # 估值分析
                print(f"\n💰 估值分析:")
                
                # 检查可用的估值字段
                columns_info = conn.execute("PRAGMA table_info(sw_industry_daily)").fetchall()
                available_columns = [col[1] for col in columns_info]
                valuation_fields = [field for field in ['pe', 'pb', 'ps', 'market_cap'] if field in available_columns]
                
                if valuation_fields:
                    for field in valuation_fields:
                        field_name = {
                            'pe': 'PE市盈率', 
                            'pb': 'PB市净率', 
                            'ps': 'PS市销率',
                            'market_cap': '总市值(万元)'
                        }[field]
                        
                        valuation_stats = conn.execute(f"""
                            SELECT 
                                AVG({field}) as avg_val,
                                MIN({field}) as min_val,
                                MAX({field}) as max_val,
                                COUNT({field}) as valid_count
                            FROM sw_industry_daily
                            WHERE ts_code = ? AND {field} IS NOT NULL AND {field} > 0
                        """, (ts_code,)).fetchone()
                        
                        if valuation_stats[3] > 0:
                            print(f"  {field_name}: 平均{valuation_stats[0]:.2f}, 范围{valuation_stats[1]:.2f}~{valuation_stats[2]:.2f} (有效数据{valuation_stats[3]}个)")
                    
                    # 当前vs历史估值分位数
                    latest_valuation = conn.execute(f"""
                        SELECT trade_date, pe, pb, ps, market_cap
                        FROM sw_industry_daily
                        WHERE ts_code = ? 
                        ORDER BY trade_date DESC
                        LIMIT 1
                    """, (ts_code,)).fetchone()
                    
                    if latest_valuation:
                        print(f"\n  最新估值 ({latest_valuation[0]}):")
                        val_names = ['PE', 'PB', 'PS', '市值(万元)']
                        for i, (field, name) in enumerate(zip(['pe', 'pb', 'ps', 'market_cap'], val_names), 1):
                            current_val = latest_valuation[i]
                            if current_val is not None and current_val > 0:
                                # 计算历史分位数
                                percentile = conn.execute(f"""
                                    SELECT 
                                        (SUM(CASE WHEN {field} <= ? THEN 1.0 ELSE 0.0 END) / COUNT(*)) * 100 as percentile
                                    FROM sw_industry_daily
                                    WHERE ts_code = ? AND {field} IS NOT NULL AND {field} > 0
                                """, (current_val, ts_code)).fetchone()[0]
                                
                                print(f"    {name}: {current_val:.2f} (历史第{percentile:.0f}分位数)")
                
                else:
                    print("  ❌ 暂无估值数据")
                
                # 成交量分析
                if 'vol' in available_columns and 'amount' in available_columns:
                    print(f"\n📊 成交量分析:")
                    volume_stats = conn.execute("""
                        SELECT 
                            AVG(vol) as avg_volume,
                            AVG(amount) as avg_amount,
                            MAX(vol) as max_volume,
                            MAX(amount) as max_amount,
                            COUNT(vol) as valid_days
                        FROM sw_industry_daily
                        WHERE ts_code = ? AND vol IS NOT NULL AND vol > 0
                    """, (ts_code,)).fetchone()
                    
                    if volume_stats[4] > 0:
                        print(f"  平均成交量: {volume_stats[0]:,.0f}万股")
                        print(f"  平均成交金额: {volume_stats[1]:,.0f}万元")
                        print(f"  最大成交量: {volume_stats[2]:,.0f}万股")
                        print(f"  最大成交金额: {volume_stats[3]:,.0f}万元")
                
                # 换手率分析
                if 'turnover_rate' in available_columns:
                    print(f"\n🔄 换手率分析:")
                    turnover_stats = conn.execute("""
                        SELECT 
                            AVG(turnover_rate) as avg_turnover,
                            MIN(turnover_rate) as min_turnover,
                            MAX(turnover_rate) as max_turnover,
                            COUNT(turnover_rate) as valid_days
                        FROM sw_industry_daily
                        WHERE ts_code = ? AND turnover_rate IS NOT NULL AND turnover_rate > 0
                    """, (ts_code,)).fetchone()
                    
                    if turnover_stats[3] > 0:
                        print(f"  平均换手率: {turnover_stats[0]:.3f}%")
                        print(f"  换手率区间: {turnover_stats[1]:.3f}% ~ {turnover_stats[2]:.3f}%")
                
                # 近期表现
                print(f"\n📈 近期表现 (最近20个交易日):")
                recent_data = conn.execute("""
                    SELECT trade_date, close, pct_change, vol, pe, pb
                    FROM sw_industry_daily
                    WHERE ts_code = ?
                    ORDER BY trade_date DESC
                    LIMIT 20
                """, (ts_code,)).fetchall()
                
                if recent_data:
                    print("  日期        收盘价    涨跌幅    成交量(万股)  PE     PB")
                    print("-" * 70)
                    for trade_date, close, pct_change, vol, pe, pb in recent_data:
                        vol_str = f"{vol:>10,.0f}" if vol is not None else "       N/A"
                        pct_str = f"{pct_change:+6.2f}%" if pct_change is not None else "   N/A"
                        pe_str = f"{pe:6.1f}" if pe is not None and pe > 0 else "   N/A"
                        pb_str = f"{pb:6.2f}" if pb is not None and pb > 0 else "   N/A"
                        print(f"  {trade_date}  {close:8.2f}  {pct_str}  {vol_str}  {pe_str}  {pb_str}")
                
                # 年度表现总结
                print(f"\n📅 年度表现总结:")
                yearly_performance = conn.execute("""
                    SELECT 
                        substr(trade_date, 1, 4) as year,
                        MIN(close) as year_low,
                        MAX(close) as year_high,
                        AVG(close) as year_avg,
                        (MAX(close) / MIN(close) - 1) * 100 as year_range,
                        COUNT(*) as trading_days
                    FROM sw_industry_daily
                    WHERE ts_code = ?
                    GROUP BY substr(trade_date, 1, 4)
                    ORDER BY year DESC
                    LIMIT 5
                """, (ts_code,)).fetchall()
                
                if yearly_performance:
                    print("  年份    最低价    最高价    平均价    年度振幅    交易日数")
                    print("-" * 65)
                    for year, low, high, avg, range_pct, days in yearly_performance:
                        print(f"  {year}    {low:7.2f}   {high:7.2f}   {avg:7.2f}   {range_pct:7.1f}%     {days:3d}")
                
        except Exception as e:
            print(f"❌ 申万行业指数分析失败: {e}")
            import traceback
            traceback.print_exc()
    
    def comprehensive_extended_analysis(self, ts_code):
        """综合扩展分析"""
        print(f"\n📊 {ts_code} 综合扩展分析")
        print("=" * 60)
        
        # 如果是申万行业指数，提供专门的分析
        if ts_code.endswith('.SI'):
            print("🏭 检测到申万行业指数，提供行业专项分析...")
            self.analyze_sw_industry_details(ts_code)
            return
        
        # 股票的综合扩展分析
        print("📈 正在进行股票综合扩展分析...")
        
        # 分红收益率分析
        dividend_data = self.cache.get_stock_dividend(ts_code)
        price_data = self.cache.get_stock_daily(ts_code)
        
        if dividend_data is not None and not dividend_data.empty and price_data is not None and not price_data.empty:
            print("\n💰 分红收益率分析:")
            
            # 计算年化分红收益率
            dividend_data = dividend_data.sort_values('ex_date')
            recent_dividends = dividend_data.tail(5)  # 最近5次分红
            
            total_dividend = recent_dividends['cash_div'].sum()
            latest_price = price_data.sort_values('trade_date').iloc[-1]['close']
            
            if total_dividend > 0 and latest_price > 0:
                dividend_yield = (total_dividend / latest_price) * 100
                print(f"  最近5次分红总额: {total_dividend:.3f}元/股")
                print(f"  当前股价: {latest_price:.2f}元")
                print(f"  分红收益率: {dividend_yield:.2f}%")
                
                # 分红稳定性分析
                yearly_dividends = dividend_data.groupby(dividend_data['ex_date'].str[:4])['cash_div'].sum()
                if len(yearly_dividends) >= 3:
                    std_div = yearly_dividends.std()
                    avg_div = yearly_dividends.mean()
                    stability = (1 - std_div / avg_div) * 100 if avg_div > 0 else 0
                    print(f"  分红稳定性: {stability:.1f}% (数值越高越稳定)")
        
        # 股东结构变化分析
        holder_data = self.cache.get_stock_holder_number(ts_code)
        if holder_data is not None and not holder_data.empty:
            print("\n👥 股东结构变化分析:")
            
            holder_data = holder_data.sort_values('end_date')
            if len(holder_data) >= 4:
                recent_trend = holder_data.tail(4)
                holder_changes = recent_trend['holder_num'].pct_change().dropna()
                
                avg_change = holder_changes.mean() * 100
                trend_direction = "增加" if avg_change > 1 else "减少" if avg_change < -1 else "稳定"
                
                print(f"  股东户数趋势: {trend_direction} (平均变化{avg_change:+.1f}%)")
                print(f"  最新股东户数: {recent_trend.iloc[-1]['holder_num']:,}户")
                
                # 股东集中度变化
                latest_holder = recent_trend.iloc[-1]['holder_num']
                earliest_holder = recent_trend.iloc[0]['holder_num']
                concentration_change = (earliest_holder / latest_holder - 1) * 100
                
                if abs(concentration_change) > 5:
                    concentration_desc = "提高" if concentration_change > 0 else "下降"
                    print(f"  股东集中度: {concentration_desc} ({concentration_change:+.1f}%)")
        
        # 流通性分析
        if price_data is not None and not price_data.empty:
            print("\n📊 流通性分析:")
            
            recent_data = price_data.sort_values('trade_date').tail(30)  # 最近30天
            avg_volume = recent_data['vol'].mean()
            avg_amount = recent_data['amount'].mean()
            
            volume_std = recent_data['vol'].std()
            volume_cv = (volume_std / avg_volume) * 100 if avg_volume > 0 else 0
            
            print(f"  平均日成交量: {avg_volume:,.0f}万股")
            print(f"  平均日成交额: {avg_amount:,.0f}万元")
            print(f"  成交量稳定性: {100 - volume_cv:.1f}% (数值越高越稳定)")
            
            # 换手率分析
            if 'turnover_rate' in recent_data.columns:
                avg_turnover = recent_data['turnover_rate'].mean()
                liquidity_level = "高" if avg_turnover > 2 else "中" if avg_turnover > 0.5 else "低"
                print(f"  平均换手率: {avg_turnover:.2f}% (流通性: {liquidity_level})")
        
        print(f"\n✅ {ts_code} 综合扩展分析完成")
    
    # ...existing code...

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析工具
Data Analysis Tool

简化版的数据分析工具，提供基本的数据分析功能
"""

import sys
import os
import argparse
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from data_manager import DataCache
    from data_manager.analysis_manager import AnalysisManager
except ImportError as e:
    print(f"❌ 无法导入数据管理模块: {e}")
    print("请确保 data_manager 模块已正确安装")
    sys.exit(1)


class AnalysisTool:
    """数据分析工具类"""
    
    def __init__(self):
        """初始化分析工具"""
        try:
            self.data_cache = DataCache()
            self.analyzer = AnalysisManager(self.data_cache)
            print("✅ 数据分析工具初始化成功")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            raise
    
    def overview(self):
        """数据概览"""
        print("📊 数据概览")
        print("=" * 40)
        
        overview = self.analyzer.get_data_overview()
        
        print(f"📈 股票数据:")
        print(f"  股票数量: {overview['stock_count']:,} 只")
        print(f"  价格记录: {overview['stock_records']:,} 条")
        if 'stock' in overview['db_sizes']:
            print(f"  数据库大小: {overview['db_sizes']['stock']:.2f} MB")
        
        print(f"\n🏦 基金数据:")
        print(f"  基金数量: {overview['fund_count']:,} 只")
        print(f"  价格记录: {overview['fund_records']:,} 条")
        if 'fund' in overview['db_sizes']:
            print(f"  数据库大小: {overview['db_sizes']['fund']:.2f} MB")
        
        print(f"\n📊 指数数据:")
        print(f"  指数数量: {overview['index_count']:,} 个")
        print(f"  价格记录: {overview['index_records']:,} 条")
        if 'index' in overview['db_sizes']:
            print(f"  数据库大小: {overview['db_sizes']['index']:.2f} MB")
        
        total_size = sum(overview['db_sizes'].values())
        print(f"\n💾 总数据大小: {total_size:.2f} MB")
    
    def stock_analysis(self, codes=None, days=30):
        """股票分析"""
        print(f"📈 股票表现分析(近{days}天)")
        print("=" * 40)
        
        if codes:
            ts_codes = codes.split(',')
        else:
            ts_codes = None
        
        results = self.analyzer.analyze_stock_performance(ts_codes, days)
        
        if results.empty:
            print("❌ 无可分析的股票数据")
            return
        
        # 按涨跌幅排序
        results = results.sort_values('change_pct', ascending=False)
        
        print(f"{'代码':<12} {'名称':<10} {'涨跌幅':<8} {'波动率':<8} {'当前价':<8}")
        print("-" * 60)
        
        for _, row in results.iterrows():
            print(f"{row['ts_code']:<12} {row['name']:<10} {row['change_pct']:>+6.2f}% {row['volatility']:>6.2f}% {row['current_price']:>7.2f}")
    
    def fund_analysis(self, codes=None, days=30):
        """基金分析"""
        print(f"🏦 基金表现分析(近{days}天)")
        print("=" * 40)
        
        if codes:
            ts_codes = codes.split(',')
        else:
            ts_codes = None
        
        results = self.analyzer.analyze_fund_performance(ts_codes, days)
        
        if results.empty:
            print("❌ 无可分析的基金数据")
            return
        
        # 按涨跌幅排序
        results = results.sort_values('change_pct', ascending=False)
        
        print(f"{'代码':<12} {'名称':<15} {'涨跌幅':<8} {'波动率':<8} {'当前价':<8}")
        print("-" * 65)
        
        for _, row in results.iterrows():
            print(f"{row['ts_code']:<12} {row['name']:<15} {row['change_pct']:>+6.2f}% {row['volatility']:>6.2f}% {row['current_price']:>7.2f}")
    
    def market_summary(self):
        """市场概况"""
        print("🌐 市场概况")
        print("=" * 40)
        
        summary = self.analyzer.get_market_summary()
        
        if 'stock_market' in summary:
            sm = summary['stock_market']
            print(f"📈 股票市场:")
            print(f"  总股票数: {sm['total_stocks']:,} 只")
            
            if sm.get('market_distribution'):
                print(f"  市场分布:")
                for market, count in list(sm['market_distribution'].items())[:5]:
                    print(f"    {market}: {count:,} 只")
            
            if sm.get('industry_distribution'):
                print(f"  主要行业:")
                for industry, count in list(sm['industry_distribution'].items())[:5]:
                    print(f"    {industry}: {count:,} 只")
        
        if 'fund_market' in summary:
            fm = summary['fund_market']
            print(f"\n🏦 基金市场:")
            print(f"  总基金数: {fm['total_funds']:,} 只")
            
            if fm.get('type_distribution'):
                print(f"  基金类型:")
                for fund_type, count in list(fm['type_distribution'].items())[:5]:
                    print(f"    {fund_type}: {count:,} 只")
    
    def quality_check(self):
        """数据质量检查"""
        print("🔍 数据质量检查")
        print("=" * 40)
        
        quality = self.analyzer.check_data_quality()
        
        if 'stock_quality' in quality:
            sq = quality['stock_quality']
            print(f"📈 股票数据质量:")
            print(f"  抽样数量: {sq['sample_size']} 只")
            print(f"  有效数据: {sq['valid_count']} 只")
            print(f"  质量评分: {sq['quality_rate']:.1f}%")
        
        if 'fund_quality' in quality:
            fq = quality['fund_quality']
            print(f"\n🏦 基金数据质量:")
            print(f"  抽样数量: {fq['sample_size']} 只")
            print(f"  有效数据: {fq['valid_count']} 只")
            print(f"  质量评分: {fq['quality_rate']:.1f}%")
        
        if quality['issues']:
            print(f"\n⚠️ 发现的问题:")
            for issue in quality['issues'][:10]:
                print(f"  {issue}")
        else:
            print(f"\n✅ 未发现明显问题")
    
    def full_report(self):
        """完整报告"""
        print("📊 完整数据分析报告")
        print("=" * 50)
        print(f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        self.analyzer.print_analysis_report()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='数据分析工具')
    parser.add_argument('command', choices=['overview', 'stock', 'fund', 'market', 'quality', 'report'],
                       help='分析命令')
    parser.add_argument('--codes', help='股票/基金代码，多个用逗号分隔')
    parser.add_argument('--days', type=int, default=30, help='分析天数 (默认30天)')
    
    args = parser.parse_args()
    
    print("📊 数据分析工具")
    print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        tool = AnalysisTool()
        
        if args.command == 'overview':
            tool.overview()
        elif args.command == 'stock':
            tool.stock_analysis(args.codes, args.days)
        elif args.command == 'fund':
            tool.fund_analysis(args.codes, args.days)
        elif args.command == 'market':
            tool.market_summary()
        elif args.command == 'quality':
            tool.quality_check()
        elif args.command == 'report':
            tool.full_report()
        
        print(f"\n✅ 程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

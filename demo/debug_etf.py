#!/usr/bin/env python3
"""
ETF数据获取调试脚本
"""

import sys
sys.path.append('.')
from data_manager.api_client import TushareClient

def test_etf_api():
    """测试ETF API调用"""
    client = TushareClient()
    
    print("=== ETF API调试测试 ===\n")
    
    # 测试单个ETF
    test_codes = ['510300.SH', '510500.SH', '510050.SH']
    
    for code in test_codes:
        print(f"测试 {code}:")
        
        try:
            # 1. 测试最近一天的数据
            result1 = client.pro.fund_daily(ts_code=code, start_date='20250725', end_date='20250725')
            print(f"  直接API调用 (1天): 类型={type(result1)}, 为空={result1.empty if hasattr(result1, 'empty') else 'N/A'}")
            if hasattr(result1, 'empty') and not result1.empty:
                print(f"    数据条数: {len(result1)}")
            
            # 2. 测试最近一周的数据
            result2 = client.pro.fund_daily(ts_code=code, start_date='20250720', end_date='20250726')
            print(f"  直接API调用 (1周): 类型={type(result2)}, 为空={result2.empty if hasattr(result2, 'empty') else 'N/A'}")
            if hasattr(result2, 'empty') and not result2.empty:
                print(f"    数据条数: {len(result2)}")
                print(f"    列名: {list(result2.columns)}")
                print(f"    样例数据:")
                print(result2.head(2))
            
            # 3. 测试我们封装的方法
            result3 = client.get_fund_daily(ts_code=code, start_date='20250725', end_date='20250725')
            print(f"  封装方法调用: 类型={type(result3)}, 为空={result3.empty if hasattr(result3, 'empty') else 'N/A'}")
            
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        
        print()
    
    # 测试批量获取
    print("=== 测试批量获取 ===")
    try:
        batch_codes = ['510300.SH', '510500.SH']
        codes_str = ','.join(batch_codes)
        
        print(f"批量代码: {codes_str}")
        
        # 直接API调用
        batch_result = client.pro.fund_daily(ts_code=codes_str, start_date='20250725', end_date='20250725')
        print(f"批量直接API: 类型={type(batch_result)}, 为空={batch_result.empty if hasattr(batch_result, 'empty') else 'N/A'}")
        if hasattr(batch_result, 'empty') and not batch_result.empty:
            print(f"  数据条数: {len(batch_result)}")
            print(batch_result.head())
        
    except Exception as e:
        print(f"❌ 批量测试错误: {e}")

if __name__ == "__main__":
    test_etf_api()

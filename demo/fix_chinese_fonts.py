#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力修复Mac中文字体显示问题
Force Fix Mac Chinese Font Display Issues
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import os
import sys

def clear_matplotlib_cache():
    """清除matplotlib缓存"""
    import shutil
    cache_dir = os.path.expanduser('~/.matplotlib')
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print("✅ 已清除matplotlib缓存")
    else:
        print("ℹ️ matplotlib缓存目录不存在")

def force_rebuild_font_cache():
    """强制重建字体缓存"""
    print("🔄 强制重建字体缓存...")
    try:
        # 新版本matplotlib
        fm.fontManager.__init__()
    except:
        try:
            # 旧版本matplotlib
            fm._rebuild()
        except:
            print("⚠️ 无法重建字体缓存，继续执行...")
    print("✅ 字体缓存重建完成")

def test_specific_font(font_name):
    """测试特定字体"""
    print(f"\n🧪 测试字体: {font_name}")
    
    try:
        # 强制设置字体
        plt.rcParams['font.sans-serif'] = [font_name]
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建测试图
        fig, ax = plt.subplots(figsize=(8, 6))
        
        test_text = f"测试中文字体: {font_name}\n股票代码: 600519.SH\n股票名称: 贵州茅台\n价格: ¥1,234.56"
        
        ax.text(0.1, 0.5, test_text, fontsize=16, transform=ax.transAxes)
        ax.set_title(f'中文字体测试 - {font_name}', fontsize=18, fontweight='bold')
        
        # 移除坐标轴
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存图片
        filename = f"test_{font_name.replace(' ', '_').replace('/', '_')}.png"
        plt.savefig(filename, dpi=150, bbox_inches='tight', facecolor='white')
        plt.close(fig)
        
        print(f"✅ 测试图片已保存: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 字体测试失败: {e}")
        return False

def find_working_chinese_font():
    """找到可用的中文字体"""
    print("🔍 寻找可用的中文字体...")
    
    # 获取所有字体
    all_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # Mac中文字体候选
    mac_chinese_fonts = [
        'PingFang HK',
        'PingFang SC', 
        'Hiragino Sans GB',
        'Hiragino Sans',
        'STHeiti',
        'Heiti TC',
        'Kaiti SC',
        'Songti SC',
        'Arial Unicode MS',
        'SimSong'
    ]
    
    print(f"📊 系统总字体数: {len(set(all_fonts))}")
    
    # 检查可用的中文字体
    available_chinese_fonts = []
    for font in mac_chinese_fonts:
        if font in all_fonts:
            available_chinese_fonts.append(font)
            print(f"✅ 找到字体: {font}")
    
    if not available_chinese_fonts:
        print("❌ 未找到任何中文字体")
        return None
    
    # 测试每个字体
    working_fonts = []
    for font in available_chinese_fonts:
        if test_specific_font(font):
            working_fonts.append(font)
    
    return working_fonts

def apply_font_fix(font_name):
    """应用字体修复"""
    print(f"\n🔧 应用字体修复: {font_name}")
    
    # 设置matplotlib配置
    plt.rcParams['font.sans-serif'] = [font_name]
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建配置文件
    config_content = f"""
# matplotlib中文字体配置
import matplotlib.pyplot as plt

# Mac中文字体设置
plt.rcParams['font.sans-serif'] = ['{font_name}']
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['axes.unicode_minus'] = False

print("✅ 中文字体已配置: {font_name}")
"""
    
    with open('matplotlib_chinese_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ 配置文件已创建: matplotlib_chinese_config.py")

def create_comprehensive_test():
    """创建综合测试图"""
    print("\n📊 创建综合测试图...")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 测试1: 基本中文文本
    ax1.text(0.1, 0.5, "基本中文测试\n股票代码: 000001.SZ\n股票名称: 平安银行", 
             fontsize=14, transform=ax1.transAxes)
    ax1.set_title("基本中文文本测试", fontsize=16, fontweight='bold')
    ax1.axis('off')
    
    # 测试2: 数字和符号
    ax2.text(0.1, 0.5, "数字符号测试\n价格: ¥123.45\n涨跌幅: +5.67%\n成交量: 1,234,567手", 
             fontsize=14, transform=ax2.transAxes)
    ax2.set_title("数字符号测试", fontsize=16, fontweight='bold')
    ax2.axis('off')
    
    # 测试3: 技术指标
    ax3.text(0.1, 0.5, "技术指标测试\nMACD指标\nRSI相对强弱指标\n布林带\n移动平均线", 
             fontsize=14, transform=ax3.transAxes)
    ax3.set_title("技术指标测试", fontsize=16, fontweight='bold')
    ax3.axis('off')
    
    # 测试4: 行业名称
    ax4.text(0.1, 0.5, "行业名称测试\n银行\n食品饮料\n医药生物\n电子\n房地产", 
             fontsize=14, transform=ax4.transAxes)
    ax4.set_title("行业名称测试", fontsize=16, fontweight='bold')
    ax4.axis('off')
    
    plt.suptitle('Mac中文字体综合测试', fontsize=20, fontweight='bold')
    plt.tight_layout()
    
    filename = "comprehensive_chinese_test.png"
    plt.savefig(filename, dpi=200, bbox_inches='tight', facecolor='white')
    plt.close(fig)
    
    print(f"✅ 综合测试图已保存: {filename}")

def main():
    """主函数"""
    print("🔧 Mac中文字体强力修复工具")
    print("="*50)
    
    if platform.system() != 'Darwin':
        print("❌ 此工具专为Mac系统设计")
        return
    
    # 1. 清除缓存
    clear_matplotlib_cache()
    
    # 2. 重建字体缓存
    force_rebuild_font_cache()
    
    # 3. 寻找可用字体
    working_fonts = find_working_chinese_font()
    
    if not working_fonts:
        print("\n❌ 没有找到可用的中文字体")
        print("💡 建议:")
        print("1. 安装中文字体包")
        print("2. 检查系统字体设置")
        return
    
    # 4. 选择最佳字体
    best_font = working_fonts[0]
    print(f"\n🎯 选择最佳字体: {best_font}")
    
    # 5. 应用修复
    apply_font_fix(best_font)
    
    # 6. 创建综合测试
    create_comprehensive_test()
    
    print("\n" + "="*50)
    print("✅ 字体修复完成!")
    print("="*50)
    print(f"🎨 推荐字体: {best_font}")
    print("📁 请查看生成的测试图片确认效果")
    print("\n🔧 在代码中使用:")
    print(f"plt.rcParams['font.sans-serif'] = ['{best_font}']")
    print("plt.rcParams['axes.unicode_minus'] = False")
    
    print(f"\n📋 找到的可用字体: {working_fonts}")

if __name__ == "__main__":
    main()

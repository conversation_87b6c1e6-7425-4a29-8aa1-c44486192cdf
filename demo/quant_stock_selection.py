"""
量化选股策略与回测分析
Quantitative Stock Selection Strategies and Backtesting

本模块实现了多种经典的量化选股策略：
1. 价值选股策略 (Value Strategy)
2. 成长选股策略 (Growth Strategy) 
3. 质量选股策略 (Quality Strategy)
4. 动量选股策略 (Momentum Strategy)
5. 低波动率策略 (Low Volatility Strategy)
6. 多因子综合策略 (Multi-Factor Strategy)
"""

import pandas as pd
import numpy as np
import sqlite3
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class QuantStockSelector:
    """量化选股策略类"""
    
    def __init__(self, data_cache_manager=None):
        """初始化选股器"""
        if data_cache_manager is None:
            from data_manager.data_cache_facade import DataCache
            self.cache = DataCache()
        else:
            self.cache = data_cache_manager
        
        self.stock_db = self.cache.stock_db
        self.basic_dir = self.cache.basic_dir
    
    def get_stock_universe(self, min_market_cap=50, exclude_st=True):
        """获取股票池"""
        print("🔍 构建股票池...")
        
        # 获取股票基本信息
        stock_basic = self.cache.get_stock_basic()
        if stock_basic is None:
            print("❌ 无法获取股票基本信息")
            return []
        
        # 过滤条件
        valid_stocks = stock_basic.copy()
        
        # 排除ST股票
        if exclude_st:
            valid_stocks = valid_stocks[~valid_stocks['name'].str.contains('ST|退', na=False)]
        
        # 排除科创板和创业板（可选）
        # valid_stocks = valid_stocks[~valid_stocks['ts_code'].str.startswith(('688', '300'))]
        
        stock_list = valid_stocks['ts_code'].tolist()
        print(f"✅ 股票池构建完成: {len(stock_list)} 只股票")
        
        return stock_list
    
    def calculate_financial_indicators(self, stock_list, lookback_days=252):
        """计算财务指标"""
        print("📊 计算财务指标...")
        
        indicators = []
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=lookback_days)).strftime('%Y%m%d')
        
        print(f"📅 数据时间范围: {start_date} - {end_date}")
        
        with sqlite3.connect(self.stock_db) as conn:
            for i, ts_code in enumerate(stock_list):
                if i % 50 == 0:
                    print(f"  处理进度: {i+1}/{len(stock_list)}")
                
                try:
                    # 获取股票日线数据
                    daily_query = """
                    SELECT * FROM stock_daily 
                    WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
                    ORDER BY trade_date DESC
                    """
                    daily_data = pd.read_sql(daily_query, conn, params=[ts_code, start_date, end_date])
                    
                    if len(daily_data) < 20:  # 至少需要20个交易日数据
                        continue
                    
                    # 获取估值数据
                    basic_query = """
                    SELECT * FROM stock_daily_basic 
                    WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
                    ORDER BY trade_date DESC
                    """
                    basic_data = pd.read_sql(basic_query, conn, params=[ts_code, start_date, end_date])
                    
                    # 计算技术指标
                    latest_price = daily_data['close'].iloc[0]
                    returns = daily_data['pct_chg'].fillna(0) / 100
                    
                    # 价值指标
                    pe = basic_data['pe'].iloc[0] if not basic_data.empty and not pd.isna(basic_data['pe'].iloc[0]) else None
                    pb = basic_data['pb'].iloc[0] if not basic_data.empty and not pd.isna(basic_data['pb'].iloc[0]) else None
                    
                    # 动量指标
                    momentum_20d = (daily_data['close'].iloc[0] / daily_data['close'].iloc[19] - 1) if len(daily_data) >= 20 else None
                    momentum_60d = (daily_data['close'].iloc[0] / daily_data['close'].iloc[59] - 1) if len(daily_data) >= 60 else None
                    
                    # 波动率指标
                    volatility = returns.std() * np.sqrt(252) if len(returns) > 0 else None
                    
                    # 成交量指标
                    avg_volume = daily_data['vol'].mean()
                    volume_ratio = daily_data['vol'].iloc[0] / avg_volume if avg_volume > 0 else None
                    
                    # 市值指标（简化计算）
                    market_cap = latest_price * daily_data['vol'].iloc[0] / 100 if daily_data['vol'].iloc[0] > 0 else None
                    
                    indicators.append({
                        'ts_code': ts_code,
                        'latest_price': latest_price,
                        'pe': pe,
                        'pb': pb,
                        'momentum_20d': momentum_20d,
                        'momentum_60d': momentum_60d,
                        'volatility': volatility,
                        'volume_ratio': volume_ratio,
                        'market_cap': market_cap,
                        'avg_volume': avg_volume
                    })
                    
                except Exception as e:
                    continue
        
        df = pd.DataFrame(indicators)
        print(f"✅ 财务指标计算完成: {len(df)} 只股票")
        return df
    
    def value_strategy(self, df, top_n=10):
        """价值选股策略：低PE、低PB"""
        print("\n💰 价值选股策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['pe', 'pb'])
        valid_df = valid_df[(valid_df['pe'] > 0) & (valid_df['pb'] > 0)]
        
        if len(valid_df) == 0:
            print("❌ 无有效价值数据")
            return []
        
        # 计算价值评分（PE和PB的倒数排名）
        valid_df['pe_rank'] = valid_df['pe'].rank(ascending=True)
        valid_df['pb_rank'] = valid_df['pb'].rank(ascending=True)
        valid_df['value_score'] = (valid_df['pe_rank'] + valid_df['pb_rank']) / 2
        
        # 选择价值评分最高的股票
        selected = valid_df.nsmallest(top_n, 'value_score')
        
        print(f"✅ 选出 {len(selected)} 只价值股:")
        for _, stock in selected.iterrows():
            print(f"  {stock['ts_code']}: PE={stock['pe']:.2f}, PB={stock['pb']:.2f}")
        
        return selected['ts_code'].tolist()
    
    def momentum_strategy(self, df, top_n=10):
        """动量选股策略：强势股"""
        print("\n🚀 动量选股策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['momentum_20d', 'momentum_60d'])
        
        if len(valid_df) == 0:
            print("❌ 无有效动量数据")
            return []
        
        # 计算动量评分
        valid_df['momentum_score'] = (valid_df['momentum_20d'] * 0.6 + valid_df['momentum_60d'] * 0.4)
        
        # 选择动量最强的股票
        selected = valid_df.nlargest(top_n, 'momentum_score')
        
        print(f"✅ 选出 {len(selected)} 只动量股:")
        for _, stock in selected.iterrows():
            print(f"  {stock['ts_code']}: 20日涨幅={stock['momentum_20d']:.2%}, 60日涨幅={stock['momentum_60d']:.2%}")
        
        return selected['ts_code'].tolist()
    
    def low_volatility_strategy(self, df, top_n=10):
        """低波动率策略：稳健股"""
        print("\n📉 低波动率策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['volatility'])
        valid_df = valid_df[valid_df['volatility'] > 0]
        
        if len(valid_df) == 0:
            print("❌ 无有效波动率数据")
            return []
        
        # 选择波动率最低的股票
        selected = valid_df.nsmallest(top_n, 'volatility')
        
        print(f"✅ 选出 {len(selected)} 只低波动股:")
        for _, stock in selected.iterrows():
            print(f"  {stock['ts_code']}: 年化波动率={stock['volatility']:.2%}")
        
        return selected['ts_code'].tolist()
    
    def quality_strategy(self, df, top_n=10):
        """质量选股策略：综合质量评分"""
        print("\n⭐ 质量选股策略")
        
        # 过滤有效数据
        valid_df = df.dropna(subset=['pe', 'pb', 'volatility', 'volume_ratio'])
        valid_df = valid_df[(valid_df['pe'] > 0) & (valid_df['pb'] > 0) & (valid_df['volatility'] > 0)]
        
        if len(valid_df) == 0:
            print("❌ 无有效质量数据")
            return []
        
        # 计算质量评分（标准化后综合）
        valid_df['pe_norm'] = (valid_df['pe'] - valid_df['pe'].mean()) / valid_df['pe'].std()
        valid_df['pb_norm'] = (valid_df['pb'] - valid_df['pb'].mean()) / valid_df['pb'].std()
        valid_df['vol_norm'] = (valid_df['volatility'] - valid_df['volatility'].mean()) / valid_df['volatility'].std()
        
        # 质量评分：低估值 + 低波动
        valid_df['quality_score'] = -(valid_df['pe_norm'] + valid_df['pb_norm'] + valid_df['vol_norm']) / 3
        
        # 选择质量评分最高的股票
        selected = valid_df.nlargest(top_n, 'quality_score')
        
        print(f"✅ 选出 {len(selected)} 只质量股:")
        for _, stock in selected.iterrows():
            print(f"  {stock['ts_code']}: 质量评分={stock['quality_score']:.2f}")
        
        return selected['ts_code'].tolist()
    
    def multi_factor_strategy(self, df, top_n=10):
        """多因子综合策略"""
        print("\n🎯 多因子综合策略")
        
        # 过滤有效数据
        required_cols = ['pe', 'pb', 'momentum_20d', 'volatility']
        valid_df = df.dropna(subset=required_cols)
        valid_df = valid_df[(valid_df['pe'] > 0) & (valid_df['pb'] > 0) & (valid_df['volatility'] > 0)]
        
        if len(valid_df) == 0:
            print("❌ 无有效多因子数据")
            return []
        
        # 因子标准化
        for col in required_cols:
            valid_df[f'{col}_norm'] = (valid_df[col] - valid_df[col].mean()) / valid_df[col].std()
        
        # 综合评分：价值(40%) + 动量(30%) + 质量(30%)
        valid_df['multi_score'] = (
            -0.2 * valid_df['pe_norm'] +      # 低PE
            -0.2 * valid_df['pb_norm'] +      # 低PB
            0.3 * valid_df['momentum_20d_norm'] +  # 高动量
            -0.3 * valid_df['volatility_norm']     # 低波动
        )
        
        # 选择综合评分最高的股票
        selected = valid_df.nlargest(top_n, 'multi_score')
        
        print(f"✅ 选出 {len(selected)} 只多因子股:")
        for _, stock in selected.iterrows():
            print(f"  {stock['ts_code']}: 综合评分={stock['multi_score']:.2f}")
        
        return selected['ts_code'].tolist()

    def backtest_strategy(self, selected_stocks, holding_days=100, start_date=None):
        """回测选股策略"""
        print(f"\n📈 回测分析 - 持仓{holding_days}天")

        if not selected_stocks:
            print("❌ 无选中股票")
            return None

        if start_date is None:
            start_date = (datetime.now() - timedelta(days=holding_days + 60)).strftime('%Y%m%d')

        end_date = datetime.now().strftime('%Y%m%d')

        portfolio_returns = []
        stock_performances = []

        with sqlite3.connect(self.stock_db) as conn:
            for ts_code in selected_stocks:
                try:
                    # 获取股票数据
                    query = """
                    SELECT trade_date, close, pct_chg FROM stock_daily
                    WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
                    ORDER BY trade_date DESC
                    """
                    df = pd.read_sql(query, conn, params=[ts_code, start_date, end_date])

                    if len(df) < 10:  # 至少需要10个交易日数据
                        continue

                    # 计算持仓期收益
                    actual_holding_days = min(holding_days, len(df))
                    start_price = df['close'].iloc[-1]  # 最早的价格（因为是DESC排序）
                    end_price = df['close'].iloc[max(0, len(df) - actual_holding_days)]  # 持仓期结束价格
                    
                    if start_price <= 0 or end_price <= 0:
                        continue
                    
                    stock_return = (end_price / start_price - 1)

                    portfolio_returns.append(stock_return)
                    stock_performances.append({
                        'ts_code': ts_code,
                        'start_price': start_price,
                        'end_price': end_price,
                        'return': stock_return,
                        'holding_days': actual_holding_days
                    })

                except Exception as e:
                    continue

        if not portfolio_returns:
            print("❌ 无有效回测数据")
            return None

        print(f"✅ 成功回测 {len(portfolio_returns)} 只股票")

        # 计算组合表现
        portfolio_return = np.mean(portfolio_returns)
        portfolio_std = np.std(portfolio_returns)
        win_rate = len([r for r in portfolio_returns if r > 0]) / len(portfolio_returns)

        # 获取基准收益（沪深300或上证指数）
        benchmark_return = self.get_benchmark_return(start_date, end_date, holding_days)

        results = {
            'portfolio_return': portfolio_return,
            'portfolio_std': portfolio_std,
            'win_rate': win_rate,
            'benchmark_return': benchmark_return,
            'excess_return': portfolio_return - benchmark_return if benchmark_return else None,
            'sharpe_ratio': portfolio_return / portfolio_std if portfolio_std > 0 else None,
            'stock_count': len(portfolio_returns),
            'stock_performances': stock_performances
        }

        self.print_backtest_results(results)
        return results

    def get_benchmark_return(self, start_date, end_date, holding_days):
        """获取基准收益率"""
        try:
            with sqlite3.connect(self.stock_db) as conn:
                # 尝试获取沪深300指数数据
                query = """
                SELECT trade_date, close FROM index_daily
                WHERE ts_code = '000300.SH' AND trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date DESC
                """
                df = pd.read_sql(query, conn, params=[start_date, end_date])

                if len(df) >= 10:  # 至少需要10天数据
                    actual_days = min(holding_days, len(df))
                    start_price = df['close'].iloc[-1]  # 最早价格
                    end_price = df['close'].iloc[max(0, len(df) - actual_days)]  # 持仓期结束价格
                    return (end_price / start_price - 1)
                
                # 如果沪深300数据不足，尝试获取上证指数
                query2 = """
                SELECT trade_date, close FROM index_daily
                WHERE ts_code = '000001.SH' AND trade_date >= ? AND trade_date <= ?
                ORDER BY trade_date DESC
                """
                df2 = pd.read_sql(query2, conn, params=[start_date, end_date])
                
                if len(df2) >= 10:
                    actual_days = min(holding_days, len(df2))
                    start_price = df2['close'].iloc[-1]
                    end_price = df2['close'].iloc[max(0, len(df2) - actual_days)]
                    return (end_price / start_price - 1)
                
        except Exception as e:
            print(f"⚠️ 获取基准收益失败: {e}")

        return None

    def print_backtest_results(self, results):
        """打印回测结果"""
        print("\n" + "="*50)
        print("📊 回测结果汇总")
        print("="*50)

        print(f"📈 组合收益率: {results['portfolio_return']:.2%}")
        print(f"📊 收益波动率: {results['portfolio_std']:.2%}")
        print(f"🎯 胜率: {results['win_rate']:.1%}")
        print(f"📊 股票数量: {results['stock_count']}")

        if results['benchmark_return']:
            print(f"📈 基准收益率: {results['benchmark_return']:.2%}")
            print(f"📊 超额收益: {results['excess_return']:.2%}")

        if results['sharpe_ratio']:
            print(f"📊 夏普比率: {results['sharpe_ratio']:.2f}")

        print("\n📋 个股表现TOP10:")
        # 按收益率排序显示
        sorted_performances = sorted(results['stock_performances'], key=lambda x: x['return'], reverse=True)
        for i, perf in enumerate(sorted_performances[:10]):
            print(f"  {i+1:2d}. {perf['ts_code']}: {perf['return']:.2%} ({perf['holding_days']}天)")
            
        if len(results['stock_performances']) > 10:
            print(f"  ... 还有{len(results['stock_performances'])-10}只股票")
            
        # 显示最差的几只股票
        if len(sorted_performances) > 5:
            print("\n📋 表现最差的股票:")
            for i, perf in enumerate(sorted_performances[-3:]):
                print(f"  {perf['ts_code']}: {perf['return']:.2%}")
        
        print("")

    def run_strategy_comparison(self):
        """运行策略对比分析"""
        print("\n🔍 量化选股策略对比分析")
        print("="*60)

        # 获取股票池
        stock_list = self.get_stock_universe()
        if not stock_list:
            print("❌ 无法构建股票池")
            return

        # 计算财务指标
        df = self.calculate_financial_indicators(stock_list)
        if df.empty:
            print("❌ 无法计算财务指标")
            return

        # 运行各种策略
        strategies = {
            '价值策略': self.value_strategy,
            '动量策略': self.momentum_strategy,
            '低波动策略': self.low_volatility_strategy,
            '质量策略': self.quality_strategy,
            '多因子策略': self.multi_factor_strategy
        }

        strategy_results = {}

        for strategy_name, strategy_func in strategies.items():
            print(f"\n{'='*20} {strategy_name} {'='*20}")

            # 执行选股
            selected_stocks = strategy_func(df.copy())

            if selected_stocks:
                # 回测分析
                backtest_result = self.backtest_strategy(selected_stocks, holding_days=100)
                strategy_results[strategy_name] = {
                    'selected_stocks': selected_stocks,
                    'backtest_result': backtest_result
                }
            else:
                strategy_results[strategy_name] = {
                    'selected_stocks': [],
                    'backtest_result': None
                }

        # 策略对比总结
        self.print_strategy_comparison(strategy_results)

        return strategy_results

    def print_strategy_comparison(self, strategy_results):
        """打印策略对比结果"""
        print("\n" + "="*60)
        print("📊 策略对比总结")
        print("="*60)

        comparison_data = []
        for strategy_name, result in strategy_results.items():
            if result['backtest_result']:
                br = result['backtest_result']
                comparison_data.append({
                    '策略': strategy_name,
                    '收益率': f"{br['portfolio_return']:.2%}",
                    '胜率': f"{br['win_rate']:.1%}",
                    '股票数': br['stock_count'],
                    '夏普比率': f"{br['sharpe_ratio']:.2f}" if br['sharpe_ratio'] else "N/A"
                })
            else:
                comparison_data.append({
                    '策略': strategy_name,
                    '收益率': "N/A",
                    '胜率': "N/A",
                    '股票数': 0,
                    '夏普比率': "N/A"
                })

        if comparison_data:
            df_comparison = pd.DataFrame(comparison_data)
            print(df_comparison.to_string(index=False))

        print("\n💡 策略建议:")
        print("1. 价值策略适合长期投资，关注低估值股票")
        print("2. 动量策略适合趋势行情，关注强势股票")
        print("3. 低波动策略适合稳健投资，关注防御性股票")
        print("4. 质量策略综合考虑多个维度，平衡风险收益")
        print("5. 多因子策略结合多种因子，分散化投资")
    
    def realtime_stock_selection(self):
        """实时选股（今日推荐）"""
        print("\n🔥 实时选股 - 今日推荐")
        print("="*50)
        
        today = datetime.now().strftime('%Y%m%d')
        print(f"📅 选股日期: {today}")
        
        # 获取股票池
        stock_list = self.get_stock_universe()
        if not stock_list:
            print("❌ 无法构建股票池")
            return
        
        # 计算最新财务指标
        df = self.calculate_financial_indicators(stock_list, lookback_days=60)  # 使用较短时间窗口获取最新数据
        if df.empty:
            print("❌ 无法计算财务指标")
            return
        
        print(f"\n📊 基于 {len(df)} 只股票进行实时选股分析")
        
        # 运行所有策略并给出综合推荐
        strategies = {
            '价值策略': self.value_strategy,
            '动量策略': self.momentum_strategy,
            '低波动策略': self.low_volatility_strategy,
            '质量策略': self.quality_strategy,
            '多因子策略': self.multi_factor_strategy
        }
        
        all_selected = {}
        strategy_scores = {}
        
        for strategy_name, strategy_func in strategies.items():
            print(f"\n{'='*15} {strategy_name} {'='*15}")
            selected_stocks = strategy_func(df.copy(), top_n=15)  # 每个策略选15只
            all_selected[strategy_name] = selected_stocks
            
            # 为每只股票打分
            for i, stock in enumerate(selected_stocks):
                score = 15 - i  # 排名越前分数越高
                if stock not in strategy_scores:
                    strategy_scores[stock] = 0
                strategy_scores[stock] += score
        
        # 综合推荐（多策略交集和高分股票）
        self.print_realtime_recommendations(all_selected, strategy_scores, df)
        
        # 生成投资计划
        print(f"\n" + "="*60)
        print("💼 基于选股结果生成投资计划")
        print("="*60)
        
        # 取综合评分前10的股票作为投资标的
        top_stocks = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)[:10]
        investment_stocks = [stock[0] for stock in top_stocks]
        
        # 让用户选择投资金额
        print(f"\n请输入投资金额 (默认10万元): ", end="")
        try:
            capital_input = input().strip()
            total_capital = float(capital_input) if capital_input else 100000
        except:
            total_capital = 100000
        
        # 生成投资计划
        investment_plan = self.generate_investment_plan(investment_stocks, df, total_capital)
        
        # 生成监控计划
        if investment_plan:
            self.generate_monitoring_plan(investment_plan['portfolio'])
        
        return all_selected, strategy_scores, investment_plan
    
    def print_realtime_recommendations(self, all_selected, strategy_scores, df):
        """打印实时推荐结果"""
        print("\n" + "="*60)
        print("🎯 今日综合推荐")
        print("="*60)
        
        # 按综合得分排序
        sorted_scores = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
        
        print("\n📈 综合评分TOP10 (多策略加权):")
        print("排名  股票代码    综合得分  策略命中  最新价格   PE     PB    波动率")
        print("-" * 70)
        
        for i, (ts_code, score) in enumerate(sorted_scores[:10]):
            # 统计命中的策略
            hit_strategies = []
            for strategy_name, stocks in all_selected.items():
                if ts_code in stocks:
                    hit_strategies.append(strategy_name[:2])
            
            # 获取股票基本信息
            stock_info = df[df['ts_code'] == ts_code].iloc[0] if len(df[df['ts_code'] == ts_code]) > 0 else None
            
            if stock_info is not None:
                price = stock_info['latest_price']
                pe = stock_info['pe'] if not pd.isna(stock_info['pe']) else 0
                pb = stock_info['pb'] if not pd.isna(stock_info['pb']) else 0
                vol = stock_info['volatility'] if not pd.isna(stock_info['volatility']) else 0
                
                print(f"{i+1:2d}.   {ts_code:10s} {score:6.0f}分   {','.join(hit_strategies):8s}  "
                      f"{price:7.2f}元  {pe:5.1f}  {pb:5.2f}  {vol:.1%}")
            else:
                print(f"{i+1:2d}.   {ts_code:10s} {score:6.0f}分   {','.join(hit_strategies):8s}  数据不足")
        
        # 多策略交集推荐
        print("\n🔥 多策略交集推荐 (最稳健):")
        strategy_intersections = {}
        strategy_names = list(all_selected.keys())
        
        # 找出被多个策略同时选中的股票
        for stock in strategy_scores.keys():
            count = sum(1 for stocks in all_selected.values() if stock in stocks)
            if count >= 2:  # 至少被2个策略选中
                strategy_intersections[stock] = count
        
        if strategy_intersections:
            sorted_intersections = sorted(strategy_intersections.items(), key=lambda x: x[1], reverse=True)
            for stock, count in sorted_intersections[:5]:
                strategies = [name for name, stocks in all_selected.items() if stock in stocks]
                print(f"  📌 {stock}: 被{count}个策略选中 ({', '.join(strategies)})")
        else:
            print("  暂无多策略交集股票")
        
        # 各策略独家推荐
        print("\n💡 各策略特色推荐:")
        for strategy_name, stocks in all_selected.items():
            if stocks:
                print(f"  {strategy_name}: {stocks[0]} (排名第一)")
        
        print(f"\n⏰ 选股完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("💡 投资建议: 综合评分高且多策略交集的股票相对更稳健")
        print("⚠️  风险提示: 请结合市场环境和个人风险偏好进行投资决策")
    
    def generate_investment_plan(self, selected_stocks, df, total_capital=100000):
        """生成投资计划和买股策略"""
        print("\n💼 投资计划生成")
        print("="*50)
        
        if not selected_stocks:
            print("❌ 无选中股票")
            return None
        
        print(f"💰 总投资资金: {total_capital:,.0f} 元")
        print(f"📊 选中股票数量: {len(selected_stocks)} 只")
        
        # 获取股票详细信息
        stock_details = []
        for ts_code in selected_stocks:
            stock_info = df[df['ts_code'] == ts_code]
            if not stock_info.empty:
                stock_details.append(stock_info.iloc[0])
        
        if not stock_details:
            print("❌ 无有效股票信息")
            return None
        
        # 生成投资组合配置
        portfolio = self.calculate_portfolio_allocation(stock_details, total_capital)
        
        # 生成买入策略
        buy_strategy = self.generate_buy_strategy(portfolio)
        
        # 打印投资计划
        self.print_investment_plan(portfolio, buy_strategy, total_capital)
        
        return {
            'portfolio': portfolio,
            'buy_strategy': buy_strategy, 
            'total_capital': total_capital
        }
    
    def calculate_portfolio_allocation(self, stock_details, total_capital):
        """计算投资组合配置"""
        portfolio = []
        
        # 基于风险调整的权重分配
        for i, stock in enumerate(stock_details):
            ts_code = stock['ts_code']
            price = stock['latest_price']
            volatility = stock.get('volatility', 0.3)  # 默认30%波动率
            
            # 基础权重（等权重起始）
            base_weight = 1.0 / len(stock_details)
            
            # 风险调整（低波动率获得更高权重）
            risk_adjustment = 0.3 / max(volatility, 0.1)  # 防止除0
            risk_adjustment = min(risk_adjustment, 2.0)  # 限制最大调整倍数
            
            # 调整后权重
            adjusted_weight = base_weight * risk_adjustment
            
            portfolio.append({
                'ts_code': ts_code,
                'price': price,
                'volatility': volatility,
                'weight': adjusted_weight,
                'pe': stock.get('pe', 0),
                'pb': stock.get('pb', 0)
            })
        
        # 归一化权重
        total_weight = sum(stock['weight'] for stock in portfolio)
        for stock in portfolio:
            stock['weight'] = stock['weight'] / total_weight
            stock['target_amount'] = total_capital * stock['weight']
            stock['target_shares'] = int(stock['target_amount'] / stock['price'] / 100) * 100  # 按手买入
            stock['actual_amount'] = stock['target_shares'] * stock['price']
        
        # 按权重排序
        portfolio.sort(key=lambda x: x['weight'], reverse=True)
        
        return portfolio
    
    def generate_buy_strategy(self, portfolio):
        """生成买入策略"""
        strategy = {
            'approach': 'gradual_entry',  # 分批建仓
            'time_horizon': '1-2周',
            'entry_methods': [],
            'risk_management': []
        }
        
        # 按权重分组买入策略
        high_weight_stocks = [s for s in portfolio if s['weight'] > 0.15]  # 高权重股票
        medium_weight_stocks = [s for s in portfolio if 0.08 <= s['weight'] <= 0.15]  # 中权重股票
        low_weight_stocks = [s for s in portfolio if s['weight'] < 0.08]  # 低权重股票
        
        if high_weight_stocks:
            strategy['entry_methods'].append({
                'category': '核心持仓 (权重>15%)',
                'stocks': [s['ts_code'] for s in high_weight_stocks],
                'method': '分3批买入',
                'timing': '第1天买入50%，第3天买入30%，第7天买入20%',
                'reason': '重要持仓需要分散建仓风险'
            })
        
        if medium_weight_stocks:
            strategy['entry_methods'].append({
                'category': '卫星持仓 (权重8-15%)',
                'stocks': [s['ts_code'] for s in medium_weight_stocks],
                'method': '分2批买入',
                'timing': '第1天买入60%，第5天买入40%',
                'reason': '平衡收益与风险'
            })
        
        if low_weight_stocks:
            strategy['entry_methods'].append({
                'category': '配置持仓 (权重<8%)',
                'stocks': [s['ts_code'] for s in low_weight_stocks],
                'method': '一次性买入',
                'timing': '选择合适时机一次买入',
                'reason': '仓位较小，交易成本考虑'
            })
        
        # 风险管理策略
        strategy['risk_management'] = [
            '止损设置: 单股下跌15%触发止损考虑',
            '止盈设置: 单股上涨50%考虑减仓',
            '组合监控: 每周评估组合表现',
            '动态调整: 月度根据基本面变化调整',
            '仓位控制: 单股最高不超过20%仓位'
        ]
        
        return strategy
    
    def print_investment_plan(self, portfolio, buy_strategy, total_capital):
        """打印投资计划"""
        print("\n📋 投资组合配置")
        print("-" * 80)
        print(f"{'排名':<4} {'股票代码':<10} {'权重':<8} {'目标金额':<10} {'目标股数':<8} {'当前价格':<8} {'PE':<6} {'PB':<6} {'波动率':<8}")
        print("-" * 80)
        
        total_actual = 0
        for i, stock in enumerate(portfolio):
            total_actual += stock['actual_amount']
            print(f"{i+1:<4} {stock['ts_code']:<10} {stock['weight']:.1%} "
                  f"{stock['actual_amount']:>9,.0f} {stock['target_shares']:>7} "
                  f"{stock['price']:>7.2f} {stock['pe']:>5.1f} {stock['pb']:>5.2f} "
                  f"{stock['volatility']:.1%}")
        
        print("-" * 80)
        print(f"总计实际投资: {total_actual:,.0f} 元 (目标: {total_capital:,.0f} 元)")
        print(f"资金利用率: {total_actual/total_capital:.1%}")
        
        print(f"\n🎯 买入策略: {buy_strategy['approach']} ({buy_strategy['time_horizon']})")
        print("\n📅 分批建仓计划:")
        for method in buy_strategy['entry_methods']:
            print(f"\n{method['category']}:")
            print(f"  股票: {', '.join(method['stocks'])}")
            print(f"  方法: {method['method']}")
            print(f"  时机: {method['timing']}")
            print(f"  理由: {method['reason']}")
        
        print(f"\n⚠️ 风险管理:")
        for rule in buy_strategy['risk_management']:
            print(f"  • {rule}")
        
        # 市场时机建议
        self.print_market_timing_advice()
    
    def print_market_timing_advice(self):
        """打印市场择时建议"""
        print(f"\n⏰ 买入时机建议:")
        print("  📈 技术面考虑:")
        print("    • 个股突破重要阻力位时加仓")
        print("    • 大盘调整时分批买入")
        print("    • 避免追高，等待回调机会")
        
        print("  📊 基本面考虑:")
        print("    • 业绩公告前后关注基本面变化")
        print("    • 行业利好消息时适当增仓")
        print("    • 市场恐慌时是较好的买入时机")
        
        print("  💡 实操建议:")
        print("    • 开盘前制定当日买入计划")
        print("    • 分批下单，避免单笔大额交易")
        print("    • 利用盘中回调机会买入")
        print("    • 设置合理的买入价格区间")
    
    def generate_monitoring_plan(self, portfolio):
        """生成监控计划"""
        print(f"\n📊 投资监控计划")
        print("="*50)
        
        print("📅 监控频率:")
        print("  • 每日: 查看股价变动和重大新闻")
        print("  • 每周: 评估组合收益和风险指标")
        print("  • 每月: 重新运行选股策略，考虑调整")
        print("  • 每季: 全面评估投资策略有效性")
        
        print(f"\n📈 关键监控指标:")
        print("  • 个股表现: 收益率、相对基准表现")
        print("  • 组合风险: 波动率、最大回撤")
        print("  • 基本面变化: PE/PB变化、业绩预期")
        print("  • 技术指标: 趋势、支撑阻力位")
        
        print(f"\n🚨 预警机制:")
        high_weight_stocks = [s['ts_code'] for s in portfolio if s['weight'] > 0.15]
        if high_weight_stocks:
            print(f"  • 核心持仓预警: {', '.join(high_weight_stocks)}")
            print("    - 单日跌幅超过5%")
            print("    - 相对基准跌幅超过10%")
        
        print("  • 组合整体预警:")
        print("    - 组合回撤超过8%")
        print("    - 连续5个交易日跑输基准")
        
        return {
            'monitoring_frequency': {
                'daily': '股价和新闻',
                'weekly': '组合表现',
                'monthly': '策略调整',
                'quarterly': '全面评估'
            },
            'key_metrics': ['收益率', '波动率', '基本面指标', '技术指标'],
            'alert_rules': ['个股预警', '组合预警']
        }
    
def main():
    """主函数"""
    print("🚀 量化选股策略系统")
    print("="*50)

    # 初始化选股器
    selector = QuantStockSelector()

    while True:
        print("\n📋 请选择功能:")
        print("1. 运行策略对比分析")
        print("2. 单独运行价值策略")
        print("3. 单独运行动量策略")
        print("4. 单独运行低波动策略")
        print("5. 单独运行质量策略")
        print("6. 单独运行多因子策略")
        print("7. 实时选股（今日推荐）")
        print("8. 生成投资计划")
        print("0. 退出")

        choice = input("\n请输入选择 (0-8): ").strip()

        if choice == '0':
            print("👋 感谢使用!")
            break
        elif choice == '1':
            selector.run_strategy_comparison()
        elif choice == '7':
            selector.realtime_stock_selection()
        elif choice == '8':
            # 生成投资计划
            print("请先输入要投资的股票代码 (用逗号分隔): ", end="")
            stock_input = input().strip()
            if stock_input:
                stock_codes = [code.strip().upper() for code in stock_input.split(',')]
                stock_list = selector.get_stock_universe()
                df = selector.calculate_financial_indicators(stock_list)
                if not df.empty:
                    # 过滤用户输入的股票
                    valid_stocks = [code for code in stock_codes if code in df['ts_code'].values]
                    if valid_stocks:
                        print(f"✅ 找到 {len(valid_stocks)} 只有效股票: {', '.join(valid_stocks)}")
                        investment_plan = selector.generate_investment_plan(valid_stocks, df)
                        if investment_plan:
                            selector.generate_monitoring_plan(investment_plan['portfolio'])
                    else:
                        print("❌ 未找到有效的股票代码")
                else:
                    print("❌ 无法获取股票数据")
            else:
                print("❌ 请输入股票代码")
        elif choice in ['2', '3', '4', '5', '6']:
            # 单独运行策略
            stock_list = selector.get_stock_universe()
            if stock_list:
                df = selector.calculate_financial_indicators(stock_list)
                if not df.empty:
                    strategy_map = {
                        '2': ('价值策略', selector.value_strategy),
                        '3': ('动量策略', selector.momentum_strategy),
                        '4': ('低波动策略', selector.low_volatility_strategy),
                        '5': ('质量策略', selector.quality_strategy),
                        '6': ('多因子策略', selector.multi_factor_strategy)
                    }

                    strategy_name, strategy_func = strategy_map[choice]
                    print(f"\n{'='*20} {strategy_name} {'='*20}")

                    selected_stocks = strategy_func(df.copy())
                    if selected_stocks:
                        selector.backtest_strategy(selected_stocks, holding_days=100)
                else:
                    print("❌ 无财务指标数据")
            else:
                print("❌ 无股票池数据")
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()

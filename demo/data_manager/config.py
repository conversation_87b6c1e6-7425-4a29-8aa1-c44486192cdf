"""
数据管理配置文件
Data Management Configuration
"""

import os
from datetime import datetime

class DataConfig:
    """数据管理配置类"""
    
    def __init__(self, base_dir=None):
        """初始化配置"""
        if base_dir is None:
            # 默认使用当前脚本所在目录的上级目录
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            base_dir = script_dir
        
        self.base_dir = base_dir
        self.cache_dir = os.path.join(base_dir, "data_cache")
        self.basic_dir = os.path.join(self.cache_dir, "basic")
        
        # 数据库文件路径
        self.stock_db = os.path.join(self.cache_dir, "stock_data.db")
        self.fund_db = os.path.join(self.cache_dir, "fund_data.db")
        self.index_db = os.path.join(self.cache_dir, "index_data.db")
        
        # API配置
        self.tushare_token = "te77a2c407d1a70139a8f9f71891e402a47"
        
        # 缓存配置
        self.cache_settings = {
            'stock_basic_max_age_days': 7,
            'fund_basic_max_age_days': 7,
            'trade_calendar_max_age_days': 30,
            'index_basic_max_age_days': 7,
            'cleanup_interval_days': 7,
        }
        
        # 数据库配置
        self.db_settings = {
            'timeout': 30.0,
            'journal_mode': 'WAL',
            'synchronous': 'NORMAL',
            'max_retries': 3,
        }
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for dir_path in [self.cache_dir, self.basic_dir]:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)
    
    def get_basic_file_path(self, data_type):
        """获取基础信息CSV文件路径"""
        return os.path.join(self.basic_dir, f"{data_type}.csv")
    
    def get_default_date_range(self, start_date=None, end_date=None):
        """获取默认日期范围"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        if start_date is None:
            start_date = '20100101'
        return start_date, end_date

# 全局配置实例
config = DataConfig()

"""
数据分析管理器
Data Analysis Manager

提供股票、基金、指数等金融数据的分析功能
"""

import os
import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from .config import config


class AnalysisManager:
    """数据分析管理器"""
    
    def __init__(self, data_cache=None):
        """初始化分析管理器"""
        if data_cache is None:
            from .data_cache_facade import DataCache
            self.cache = DataCache()
        else:
            self.cache = data_cache
        
        self.config = config
    
    def get_data_overview(self):
        """获取数据概览"""
        overview = {
            'stock_count': 0,
            'fund_count': 0,
            'index_count': 0,
            'stock_records': 0,
            'fund_records': 0,
            'index_records': 0,
            'db_sizes': {}
        }
        
        # 股票基本信息
        stock_basic = self.cache.get_stock_basic()
        if not stock_basic.empty:
            overview['stock_count'] = len(stock_basic)
        
        # 基金基本信息
        fund_basic = self.cache.get_fund_basic()
        if not fund_basic.empty:
            overview['fund_count'] = len(fund_basic)
        
        # 指数基本信息
        index_basic = self.cache.get_index_basic()
        if not index_basic.empty:
            overview['index_count'] = len(index_basic)
        
        # 数据库大小
        for db_name, db_path in [
            ('stock', self.config.stock_db),
            ('fund', self.config.fund_db),
            ('index', self.config.index_db)
        ]:
            if os.path.exists(db_path):
                size_mb = os.path.getsize(db_path) / 1024 / 1024
                overview['db_sizes'][db_name] = size_mb
                
                # 记录数统计
                try:
                    with sqlite3.connect(db_path) as conn:
                        if db_name == 'stock':
                            count = conn.execute("SELECT COUNT(*) FROM stock_daily").fetchone()[0]
                            overview['stock_records'] = count
                        elif db_name == 'fund':
                            etf_count = conn.execute("SELECT COUNT(*) FROM etf_daily").fetchone()[0]
                            nav_count = conn.execute("SELECT COUNT(*) FROM fund_nav").fetchone()[0]
                            overview['fund_records'] = etf_count + nav_count
                        elif db_name == 'index':
                            count = conn.execute("SELECT COUNT(*) FROM index_daily").fetchone()[0]
                            overview['index_records'] = count
                except:
                    pass
        
        return overview
    
    def analyze_stock_performance(self, ts_codes=None, days=30):
        """分析股票表现"""
        if ts_codes is None:
            # 默认分析主要指数成分股
            ts_codes = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ', '600519.SH']
        
        results = []
        
        for ts_code in ts_codes:
            try:
                data = self.cache.get_stock_daily(ts_code, from_db=True)
                if data.empty:
                    continue
                
                # 取最近N天数据
                recent_data = data.tail(days)
                if len(recent_data) < 2:
                    continue
                
                # 计算基本指标
                start_price = recent_data['close'].iloc[0]
                end_price = recent_data['close'].iloc[-1]
                change_pct = (end_price / start_price - 1) * 100
                
                volatility = recent_data['close'].pct_change().std() * 100
                avg_volume = recent_data['vol'].mean()
                
                # 获取股票名称
                stock_basic = self.cache.get_stock_basic()
                stock_name = ts_code
                if not stock_basic.empty:
                    stock_info = stock_basic[stock_basic['ts_code'] == ts_code]
                    if not stock_info.empty:
                        stock_name = stock_info['name'].iloc[0]
                
                results.append({
                    'ts_code': ts_code,
                    'name': stock_name,
                    'change_pct': change_pct,
                    'volatility': volatility,
                    'avg_volume': avg_volume,
                    'current_price': end_price,
                    'data_points': len(recent_data)
                })
                
            except Exception as e:
                print(f"⚠️ 分析 {ts_code} 失败: {e}")
        
        return pd.DataFrame(results)
    
    def analyze_fund_performance(self, ts_codes=None, days=30):
        """分析基金表现"""
        if ts_codes is None:
            # 默认分析主要ETF
            ts_codes = ['510300.SH', '159919.SZ', '512100.SH', '159995.SZ', '515050.SH']
        
        results = []
        
        for ts_code in ts_codes:
            try:
                # 尝试ETF数据
                data = self.cache.get_etf_daily(ts_code, from_db=True)
                price_col = 'close'
                
                if data.empty:
                    # 尝试基金净值数据
                    data = self.cache.get_fund_nav(ts_code, from_db=True)
                    price_col = 'unit_nav'
                
                if data.empty:
                    continue
                
                # 取最近N天数据
                recent_data = data.tail(days)
                if len(recent_data) < 2:
                    continue
                
                # 计算基本指标
                start_price = recent_data[price_col].iloc[0]
                end_price = recent_data[price_col].iloc[-1]
                change_pct = (end_price / start_price - 1) * 100
                
                volatility = recent_data[price_col].pct_change().std() * 100
                
                # 获取基金名称
                fund_basic = self.cache.get_fund_basic()
                fund_name = ts_code
                if not fund_basic.empty:
                    fund_info = fund_basic[fund_basic['ts_code'] == ts_code]
                    if not fund_info.empty:
                        fund_name = fund_info['name'].iloc[0]
                
                results.append({
                    'ts_code': ts_code,
                    'name': fund_name,
                    'change_pct': change_pct,
                    'volatility': volatility,
                    'current_price': end_price,
                    'data_points': len(recent_data)
                })
                
            except Exception as e:
                print(f"⚠️ 分析 {ts_code} 失败: {e}")
        
        return pd.DataFrame(results)
    
    def get_market_summary(self):
        """获取市场概况"""
        summary = {}
        
        # 股票市场概况
        stock_basic = self.cache.get_stock_basic()
        if not stock_basic.empty:
            summary['stock_market'] = {
                'total_stocks': len(stock_basic),
                'market_distribution': stock_basic.get('market', pd.Series()).value_counts().to_dict(),
                'industry_distribution': stock_basic.get('industry', pd.Series()).value_counts().head(10).to_dict()
            }
        
        # 基金市场概况
        fund_basic = self.cache.get_fund_basic()
        if not fund_basic.empty:
            summary['fund_market'] = {
                'total_funds': len(fund_basic),
                'type_distribution': fund_basic.get('fund_type', pd.Series()).value_counts().head(10).to_dict(),
                'management_distribution': fund_basic.get('management', pd.Series()).value_counts().head(10).to_dict()
            }
        
        return summary
    
    def check_data_quality(self, sample_size=10):
        """检查数据质量"""
        quality_report = {
            'stock_quality': {},
            'fund_quality': {},
            'issues': []
        }
        
        # 检查股票数据质量
        stock_basic = self.cache.get_stock_basic()
        if not stock_basic.empty:
            sample_stocks = stock_basic.sample(min(sample_size, len(stock_basic)))['ts_code'].tolist()
            
            valid_count = 0
            for ts_code in sample_stocks:
                try:
                    data = self.cache.get_stock_daily(ts_code, from_db=True)
                    if not data.empty and len(data) > 10:
                        valid_count += 1
                except:
                    quality_report['issues'].append(f"股票 {ts_code} 数据读取失败")
            
            quality_report['stock_quality'] = {
                'sample_size': len(sample_stocks),
                'valid_count': valid_count,
                'quality_rate': valid_count / len(sample_stocks) * 100
            }
        
        # 检查基金数据质量
        fund_basic = self.cache.get_fund_basic()
        if not fund_basic.empty:
            sample_funds = fund_basic.sample(min(sample_size, len(fund_basic)))['ts_code'].tolist()
            
            valid_count = 0
            for ts_code in sample_funds:
                try:
                    etf_data = self.cache.get_etf_daily(ts_code, from_db=True)
                    nav_data = self.cache.get_fund_nav(ts_code, from_db=True)
                    if not etf_data.empty or not nav_data.empty:
                        valid_count += 1
                except:
                    quality_report['issues'].append(f"基金 {ts_code} 数据读取失败")
            
            quality_report['fund_quality'] = {
                'sample_size': len(sample_funds),
                'valid_count': valid_count,
                'quality_rate': valid_count / len(sample_funds) * 100
            }
        
        return quality_report
    
    def print_analysis_report(self):
        """打印分析报告"""
        print("📊 数据分析报告")
        print("=" * 50)
        
        # 数据概览
        overview = self.get_data_overview()
        print(f"\n📈 数据概览:")
        print(f"  股票数量: {overview['stock_count']:,} 只")
        print(f"  基金数量: {overview['fund_count']:,} 只")
        print(f"  指数数量: {overview['index_count']:,} 个")
        print(f"  股票记录: {overview['stock_records']:,} 条")
        print(f"  基金记录: {overview['fund_records']:,} 条")
        print(f"  指数记录: {overview['index_records']:,} 条")
        
        # 数据库大小
        print(f"\n💾 数据库大小:")
        for db_name, size_mb in overview['db_sizes'].items():
            print(f"  {db_name}: {size_mb:.2f} MB")
        
        # 股票表现分析
        print(f"\n📈 主要股票表现(近30天):")
        stock_perf = self.analyze_stock_performance()
        if not stock_perf.empty:
            for _, row in stock_perf.iterrows():
                print(f"  {row['ts_code']}({row['name']}): {row['change_pct']:+.2f}%")
        
        # 基金表现分析
        print(f"\n🏦 主要基金表现(近30天):")
        fund_perf = self.analyze_fund_performance()
        if not fund_perf.empty:
            for _, row in fund_perf.iterrows():
                print(f"  {row['ts_code']}({row['name']}): {row['change_pct']:+.2f}%")
        
        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        quality = self.check_data_quality()
        if 'stock_quality' in quality:
            sq = quality['stock_quality']
            print(f"  股票数据质量: {sq['quality_rate']:.1f}% ({sq['valid_count']}/{sq['sample_size']})")
        if 'fund_quality' in quality:
            fq = quality['fund_quality']
            print(f"  基金数据质量: {fq['quality_rate']:.1f}% ({fq['valid_count']}/{fq['sample_size']})")
        
        if quality['issues']:
            print(f"\n⚠️ 发现问题:")
            for issue in quality['issues'][:5]:  # 只显示前5个问题
                print(f"  {issue}")
        
        print(f"\n✅ 分析报告完成")

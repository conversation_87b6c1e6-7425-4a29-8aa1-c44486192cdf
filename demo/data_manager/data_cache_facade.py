"""
数据缓存外观类
Data Cache Facade

提供统一的数据访问接口，兼容原有的DataCache类
"""

from .config import config
from .database_manager import DatabaseManager
from .api_client import TushareClient
from .basic_data_manager import BasicDataManager
from .update_manager import UpdateManager

class DataCache:
    """数据缓存外观类 - 兼容原有接口"""
    
    def __init__(self):
        """初始化数据缓存"""
        self.config = config
        
        # 初始化各个管理器
        self.db_manager = DatabaseManager()
        self.api_client = TushareClient()
        self.basic_manager = BasicDataManager(self.api_client)
        self.update_manager = UpdateManager(self.api_client, self.db_manager, self.basic_manager)
        
        # 兼容性属性
        self.stock_db = config.stock_db
        self.fund_db = config.fund_db
        self.index_db = config.index_db
        self.basic_dir = config.basic_dir
        self.pro = self.api_client.pro
    
    # ==================== 基础数据接口 ====================
    
    def get_stock_basic(self, force_update=False):
        """获取股票基本信息"""
        return self.basic_manager.get_stock_basic(force_update)
    
    def get_fund_basic(self, force_update=False):
        """获取基金基本信息"""
        return self.basic_manager.get_fund_basic(force_update)
    
    def get_index_basic(self, force_update=False):
        """获取指数基本信息"""
        return self.basic_manager.get_index_basic(force_update)
    
    def get_trade_calendar(self, force_update=False):
        """获取交易日历"""
        return self.basic_manager.get_trade_calendar(force_update)
    
    def get_stock_list(self, exclude_delisted=True):
        """获取股票列表"""
        return self.basic_manager.get_stock_list(exclude_delisted)
    
    def get_fund_list(self):
        """获取基金列表"""
        return self.basic_manager.get_fund_list()
    
    def get_index_list(self):
        """获取指数列表"""
        return self.basic_manager.get_index_list()
    
    # ==================== 交易日历接口 ====================
    
    def is_trade_date(self, date_str):
        """检查是否为交易日"""
        return self.basic_manager.is_trade_date(date_str)
    
    def get_latest_trade_date(self):
        """获取最新交易日"""
        return self.basic_manager.get_latest_trade_date()
    
    def get_previous_trade_date(self, date_str, days=1):
        """获取指定日期前N个交易日"""
        return self.basic_manager.get_previous_trade_date(date_str, days)
    
    # ==================== 数据库操作接口 ====================
    
    def save_to_database(self, data, table_name, db_path):
        """保存数据到数据库"""
        return self.db_manager.save_to_database(data, table_name, db_path)
    
    def load_from_database(self, table_name, db_path, ts_code=None, date_column='trade_date'):
        """从数据库加载数据"""
        return self.db_manager.load_from_database(table_name, db_path, ts_code, date_column)
    
    def get_latest_date(self, table_name, db_path, ts_code, date_column):
        """获取指定代码的最新数据日期"""
        return self.db_manager.get_latest_date(table_name, db_path, ts_code, date_column)
    
    # ==================== 股票数据接口 ====================
    
    def get_stock_daily(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取股票日线数据"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('stock_daily', self.stock_db, ts_code)
        else:
            # 从API获取
            return self.api_client.get_stock_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    def get_stock_daily_basic(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取股票估值数据"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('stock_daily_basic', self.stock_db, ts_code)
        else:
            # 从API获取
            return self.api_client.get_stock_daily_basic(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    def get_stock_fina_indicator(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取股票财务指标"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('stock_fina_indicator', self.stock_db, ts_code, 'end_date')
        else:
            # 从API获取
            return self.api_client.get_stock_fina_indicator(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    # ==================== 基金数据接口 ====================
    
    def get_fund_nav(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取基金净值数据"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('fund_nav', self.fund_db, ts_code, 'nav_date')
        else:
            # 从API获取
            return self.api_client.get_fund_nav(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    def get_etf_daily(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取ETF日线数据"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('etf_daily', self.fund_db, ts_code)
        else:
            # 从API获取
            return self.api_client.get_etf_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    # ==================== 指数数据接口 ====================
    
    def get_index_daily(self, ts_code=None, start_date=None, end_date=None, from_db=True):
        """获取指数日线数据"""
        if from_db and ts_code:
            # 从数据库获取
            return self.db_manager.load_from_database('index_daily', self.index_db, ts_code)
        else:
            # 从API获取
            return self.api_client.get_index_daily(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date
            )
    
    # ==================== 批量数据获取 ====================
    
    def cache_all_stocks_data(self, start_date=None, end_date=None, force_update=False):
        """批量缓存股票数据"""
        print("🔄 批量缓存股票数据...")
        
        if start_date is None or end_date is None:
            start_date, end_date = self.config.get_default_date_range(start_date, end_date)
        
        success_count = 0
        
        try:
            # 获取股票日线数据
            daily_data = self.api_client.get_batch_stock_daily(start_date, end_date)
            if not daily_data.empty:
                # 过滤掉退市股票的数据
                current_stocks = self.get_stock_basic()
                if current_stocks is not None:
                    active_stock_codes = set(current_stocks['ts_code'].tolist())
                    before_count = len(daily_data)
                    daily_data = daily_data[daily_data['ts_code'].isin(active_stock_codes)]
                    after_count = len(daily_data)
                    
                    if before_count > after_count:
                        print(f"   🔍 过滤退市股票数据: {before_count - after_count} 条")
                
                if not daily_data.empty:
                    self.save_to_database(daily_data, 'stock_daily', self.stock_db)
                    success_count += len(daily_data)
            
            # 获取股票估值数据
            basic_data = self.api_client.get_batch_stock_basic_data(start_date, end_date)
            if not basic_data.empty:
                # 过滤掉退市股票的数据
                current_stocks = self.get_stock_basic()
                if current_stocks is not None:
                    active_stock_codes = set(current_stocks['ts_code'].tolist())
                    before_count = len(basic_data)
                    basic_data = basic_data[basic_data['ts_code'].isin(active_stock_codes)]
                    after_count = len(basic_data)
                    
                    if before_count > after_count:
                        print(f"   🔍 过滤退市股票数据: {before_count - after_count} 条")
                
                if not basic_data.empty:
                    self.save_to_database(basic_data, 'stock_daily_basic', self.stock_db)
                    success_count += len(basic_data)
            
        except Exception as e:
            print(f"❌ 批量缓存失败: {e}")
        
        print(f"✅ 批量缓存完成: {success_count:,} 条记录")
        return success_count
    
    # ==================== 数据验证接口 ====================
    
    def is_stock_delisted(self, ts_code):
        """检查股票是否已退市"""
        return self.api_client.check_stock_status(ts_code) == 'D'
    
    def filter_active_stocks(self, stock_codes):
        """过滤出仍在交易的股票"""
        print(f"🔍 检查 {len(stock_codes)} 只股票的交易状态...")
        
        active_stocks = []
        delisted_stocks = []
        
        # 获取当前所有正常上市的股票
        current_stocks = self.get_stock_basic()
        if current_stocks is not None:
            active_stock_codes = set(current_stocks['ts_code'].tolist())
            
            for ts_code in stock_codes:
                if ts_code in active_stock_codes:
                    active_stocks.append(ts_code)
                else:
                    delisted_stocks.append(ts_code)
        else:
            # 如果无法获取基本信息，逐个检查
            for ts_code in stock_codes:
                if not self.is_stock_delisted(ts_code):
                    active_stocks.append(ts_code)
                else:
                    delisted_stocks.append(ts_code)
        
        if delisted_stocks:
            print(f"⚠️ 发现 {len(delisted_stocks)} 只退市股票，已排除")
            print(f"   退市股票: {delisted_stocks[:10]}{'...' if len(delisted_stocks) > 10 else ''}")
        
        print(f"✅ 有效股票: {len(active_stocks)} 只")
        return active_stocks
    
    # ==================== 统计信息接口 ====================
    
    def get_data_statistics(self):
        """获取数据统计信息"""
        stats = {}
        
        # 股票数据统计
        stock_basic = self.get_stock_basic()
        stats['stock_count'] = len(stock_basic) if not stock_basic.empty else 0
        
        # 基金数据统计
        fund_basic = self.get_fund_basic()
        stats['fund_count'] = len(fund_basic) if not fund_basic.empty else 0
        
        # 指数数据统计
        index_basic = self.get_index_basic()
        stats['index_count'] = len(index_basic) if not index_basic.empty else 0
        
        return stats
    
    def print_data_statistics(self):
        """打印数据统计信息"""
        stats = self.get_data_statistics()
        
        print("\n📊 数据统计信息:")
        print(f"   股票数量: {stats['stock_count']:,} 只")
        print(f"   基金数量: {stats['fund_count']:,} 只")
        print(f"   指数数量: {stats['index_count']:,} 只")
        
        return stats

    # ==================== 数据更新接口 ====================

    def cache_all_stocks_data(self, force_update=False, start_date=None, end_date=None):
        """批量缓存股票数据 - 兼容接口"""
        return self.update_manager.update_stocks_data(force_update, start_date, end_date)

    def cache_all_funds_data(self, force_update=False, start_date=None, end_date=None):
        """批量缓存基金数据 - 兼容接口"""
        return self.update_manager.update_funds_data(force_update, start_date, end_date)

    def cache_all_index_data(self, force_update=False, start_date=None, end_date=None):
        """批量缓存指数数据 - 兼容接口"""
        return self.update_manager.update_indexes_data(force_update, start_date, end_date)

    def cache_all_industry_data(self, force_update=False, start_date=None, end_date=None):
        """批量缓存行业数据 - 兼容接口"""
        return self.update_manager.update_industries_data(force_update, start_date, end_date)

    def update_all_data(self, force_update=False):
        """更新所有数据"""
        return self.update_manager.update_all(force_update)

    def update_quick_data(self, force_update=False):
        """快速更新数据"""
        return self.update_manager.update_quick(force_update)

    def cleanup_delisted_data(self, force_cleanup=False):
        """清理退市数据"""
        return self.update_manager.cleanup_delisted_data(force_cleanup)

    def show_data_summary(self):
        """显示数据汇总"""
        return self.update_manager.show_summary()

    def get_update_report(self):
        """获取更新报告"""
        return self.update_manager.get_update_report()

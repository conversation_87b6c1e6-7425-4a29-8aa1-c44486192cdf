"""
数据库管理器
Database Manager

负责数据库连接、表创建、数据存储和查询
"""

import sqlite3
import pandas as pd
import time
from .config import config

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config = config
        self._init_databases()
    
    def _init_databases(self):
        """初始化数据库表结构"""
        self._init_stock_database()
        self._init_fund_database()
        self._init_index_database()
        print("✅ 数据库初始化完成")
    
    def _init_stock_database(self):
        """初始化股票数据库"""
        with sqlite3.connect(self.config.stock_db) as conn:
            # 股票日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 股票估值数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_daily_basic (
                    ts_code TEXT,
                    trade_date TEXT,
                    close REAL,
                    turnover_rate REAL,
                    turnover_rate_f REAL,
                    volume_ratio REAL,
                    pe REAL,
                    pe_ttm REAL,
                    pb REAL,
                    ps REAL,
                    ps_ttm REAL,
                    dv_ratio REAL,
                    dv_ttm REAL,
                    total_share REAL,
                    float_share REAL,
                    free_share REAL,
                    total_mv REAL,
                    circ_mv REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 财务指标表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS stock_fina_indicator (
                    ts_code TEXT,
                    ann_date TEXT,
                    end_date TEXT,
                    eps REAL,
                    dt_eps REAL,
                    total_revenue_ps REAL,
                    revenue_ps REAL,
                    capital_rese_ps REAL,
                    surplus_rese_ps REAL,
                    undist_profit_ps REAL,
                    extra_item REAL,
                    profit_dedt REAL,
                    gross_margin REAL,
                    current_ratio REAL,
                    quick_ratio REAL,
                    cash_ratio REAL,
                    invturn_days REAL,
                    arturn_days REAL,
                    inv_turn REAL,
                    ar_turn REAL,
                    ca_turn REAL,
                    fa_turn REAL,
                    assets_turn REAL,
                    op_income REAL,
                    valuechange_income REAL,
                    interst_income REAL,
                    daa REAL,
                    ebit REAL,
                    ebitda REAL,
                    fcff REAL,
                    fcfe REAL,
                    current_exint REAL,
                    noncurrent_exint REAL,
                    interestdebt REAL,
                    netdebt REAL,
                    tangible_asset REAL,
                    working_capital REAL,
                    networking_capital REAL,
                    invest_capital REAL,
                    retained_earnings REAL,
                    diluted2_eps REAL,
                    bps REAL,
                    ocfps REAL,
                    retainedps REAL,
                    cfps REAL,
                    ebit_ps REAL,
                    fcff_ps REAL,
                    fcfe_ps REAL,
                    netprofit_margin REAL,
                    grossprofit_margin REAL,
                    cogs_of_sales REAL,
                    expense_of_sales REAL,
                    profit_to_gr REAL,
                    saleexp_to_gr REAL,
                    adminexp_of_gr REAL,
                    finaexp_of_gr REAL,
                    impai_ttm REAL,
                    gc_of_gr REAL,
                    op_of_gr REAL,
                    ebit_of_gr REAL,
                    roe REAL,
                    roe_waa REAL,
                    roe_dt REAL,
                    roa REAL,
                    npta REAL,
                    roic REAL,
                    roe_yearly REAL,
                    roa_yearly REAL,
                    roe_avg REAL,
                    opincome_of_ebt REAL,
                    investincome_of_ebt REAL,
                    n_op_profit_of_ebt REAL,
                    tax_to_ebt REAL,
                    dtprofit_to_profit REAL,
                    salescash_to_or REAL,
                    ocf_to_or REAL,
                    ocf_to_opincome REAL,
                    capitalized_to_da REAL,
                    debt_to_assets REAL,
                    assets_to_eqt REAL,
                    dp_assets_to_eqt REAL,
                    ca_to_assets REAL,
                    nca_to_assets REAL,
                    tbassets_to_totalassets REAL,
                    int_to_talcap REAL,
                    eqt_to_talcapital REAL,
                    currentdebt_to_debt REAL,
                    longdeb_to_debt REAL,
                    ocf_to_shortdebt REAL,
                    debt_to_eqt REAL,
                    eqt_to_debt REAL,
                    eqt_to_interestdebt REAL,
                    tangibleasset_to_debt REAL,
                    tangasset_to_intdebt REAL,
                    tangibleasset_to_netdebt REAL,
                    ocf_to_debt REAL,
                    ocf_to_interestdebt REAL,
                    ocf_to_netdebt REAL,
                    ebit_to_interest REAL,
                    longdebt_to_workingcapital REAL,
                    ebitda_to_debt REAL,
                    turn_days REAL,
                    roa_yearly REAL,
                    roa_dp REAL,
                    fixed_assets REAL,
                    profit_prefin_exp REAL,
                    non_op_profit REAL,
                    op_to_ebt REAL,
                    nop_to_ebt REAL,
                    ocf_to_profit REAL,
                    cash_to_liqdebt REAL,
                    cash_to_liqdebt_withinterest REAL,
                    op_to_liqdebt REAL,
                    op_to_debt REAL,
                    roic_yearly REAL,
                    total_fa_trun REAL,
                    profit_to_op REAL,
                    q_opincome REAL,
                    q_investincome REAL,
                    q_dtprofit REAL,
                    q_eps REAL,
                    q_netprofit_margin REAL,
                    q_gsprofit_margin REAL,
                    q_exp_to_sales REAL,
                    q_profit_to_gr REAL,
                    q_saleexp_to_gr REAL,
                    q_adminexp_to_gr REAL,
                    q_finaexp_to_gr REAL,
                    q_impair_to_gr_ttm REAL,
                    q_gc_to_gr REAL,
                    q_op_to_gr REAL,
                    q_roe REAL,
                    q_dt_roe REAL,
                    q_npta REAL,
                    q_opincome_to_ebt REAL,
                    q_investincome_to_ebt REAL,
                    q_dtprofit_to_profit REAL,
                    q_salescash_to_or REAL,
                    q_ocf_to_sales REAL,
                    q_ocf_to_or REAL,
                    basic_eps_yoy REAL,
                    dt_eps_yoy REAL,
                    cfps_yoy REAL,
                    op_yoy REAL,
                    ebt_yoy REAL,
                    netprofit_yoy REAL,
                    dt_netprofit_yoy REAL,
                    ocf_yoy REAL,
                    roe_yoy REAL,
                    bps_yoy REAL,
                    assets_yoy REAL,
                    eqt_yoy REAL,
                    tr_yoy REAL,
                    or_yoy REAL,
                    q_gr_yoy REAL,
                    q_gr_qoq REAL,
                    q_sales_yoy REAL,
                    q_sales_qoq REAL,
                    q_op_yoy REAL,
                    q_op_qoq REAL,
                    q_profit_yoy REAL,
                    q_profit_qoq REAL,
                    q_netprofit_yoy REAL,
                    q_netprofit_qoq REAL,
                    equity_yoy REAL,
                    rd_exp REAL,
                    q_sales_yoy2 REAL,
                    q_sales_qoq2 REAL,
                    q_roe_yoy REAL,
                    q_roa_yoy REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, ann_date, end_date)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_code ON stock_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_daily_date ON stock_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_basic_code ON stock_daily_basic(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_stock_basic_date ON stock_daily_basic(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_code ON stock_fina_indicator(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fina_indicator_date ON stock_fina_indicator(end_date)')
    
    def _init_fund_database(self):
        """初始化基金数据库"""
        with sqlite3.connect(self.config.fund_db) as conn:
            # ETF日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS etf_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 基金净值数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS fund_nav (
                    ts_code TEXT,
                    ann_date TEXT,
                    nav_date TEXT,
                    unit_nav REAL,
                    accum_nav REAL,
                    accum_div REAL,
                    net_asset REAL,
                    total_netasset REAL,
                    adj_nav REAL,
                    update_flag TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, nav_date)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_code ON etf_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_etf_daily_date ON etf_daily(trade_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_code ON fund_nav(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_fund_nav_date ON fund_nav(nav_date)')
    
    def _init_index_database(self):
        """初始化指数数据库"""
        with sqlite3.connect(self.config.index_db) as conn:
            # 指数日线数据表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS index_daily (
                    ts_code TEXT,
                    trade_date TEXT,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    pre_close REAL,
                    change REAL,
                    pct_chg REAL,
                    vol REAL,
                    amount REAL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (ts_code, trade_date)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_code ON index_daily(ts_code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_index_daily_date ON index_daily(trade_date)')
    
    def save_to_database(self, data, table_name, db_path):
        """保存数据到数据库"""
        if data.empty:
            return
        
        max_retries = self.config.db_settings['max_retries']
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=self.config.db_settings['timeout']) as conn:
                    conn.execute(f'PRAGMA journal_mode={self.config.db_settings["journal_mode"]}')
                    conn.execute(f'PRAGMA synchronous={self.config.db_settings["synchronous"]}')
                    data.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
                print(f"✅ 数据已保存到数据库: {table_name} ({len(data)} 条记录)")
                return
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                elif "UNIQUE constraint failed" in str(e):
                    try:
                        self._save_with_replace(data, table_name, db_path)
                        return
                    except Exception as replace_error:
                        print(f"❌ 替换保存失败: {replace_error}")
                        return
                else:
                    print(f"❌ 数据库保存失败: {e}")
                    return
    
    def _save_with_replace(self, data, table_name, db_path):
        """使用INSERT OR REPLACE方式保存数据"""
        with sqlite3.connect(db_path, timeout=self.config.db_settings['timeout']) as conn:
            conn.execute(f'PRAGMA journal_mode={self.config.db_settings["journal_mode"]}')
            conn.execute(f'PRAGMA synchronous={self.config.db_settings["synchronous"]}')
            
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            db_columns = [col[1] for col in columns_info]
            
            data_columns = [col for col in data.columns if col in db_columns]
            filtered_data = data[data_columns]
            
            placeholders = ','.join(['?' for _ in data_columns])
            columns_str = ','.join(data_columns)
            
            for _, row in filtered_data.iterrows():
                cursor.execute(
                    f"INSERT OR REPLACE INTO {table_name} ({columns_str}) VALUES ({placeholders})",
                    tuple(row[data_columns])
                )
            conn.commit()
    
    def load_from_database(self, table_name, db_path, ts_code=None, date_column='trade_date'):
        """从数据库加载数据"""
        max_retries = self.config.db_settings['max_retries']
        for attempt in range(max_retries):
            try:
                with sqlite3.connect(db_path, timeout=self.config.db_settings['timeout']) as conn:
                    if ts_code:
                        query = f"SELECT * FROM {table_name} WHERE ts_code = ? ORDER BY {date_column}"
                        data = pd.read_sql(query, conn, params=[ts_code])
                    else:
                        data = pd.read_sql(f"SELECT * FROM {table_name}", conn)
                    
                    if not data.empty:
                        print(f"📁 从数据库加载: {table_name} ({len(data)} 条记录)")
                    return data
            except Exception as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    print(f"⚠️ 数据库锁定，等待重试... (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(1.0 * (attempt + 1))
                    continue
                else:
                    print(f"❌ 数据库加载失败: {e}")
                    return pd.DataFrame()
    
    def get_latest_date(self, table_name, db_path, ts_code, date_column):
        """获取指定代码的最新数据日期"""
        try:
            with sqlite3.connect(db_path, timeout=self.config.db_settings['timeout']) as conn:
                query = f"SELECT MAX({date_column}) FROM {table_name} WHERE ts_code = ?"
                result = conn.execute(query, (ts_code,)).fetchone()
                return result[0] if result[0] else None
        except Exception as e:
            print(f"❌ 获取最新日期失败: {e}")
            return None
    
    def delete_records(self, table_name, db_path, ts_code):
        """删除指定代码的所有记录"""
        try:
            with sqlite3.connect(db_path, timeout=self.config.db_settings['timeout']) as conn:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name} WHERE ts_code = ?", (ts_code,))
                count = cursor.fetchone()[0]
                
                if count > 0:
                    conn.execute(f"DELETE FROM {table_name} WHERE ts_code = ?", (ts_code,))
                    conn.commit()
                    return count
                return 0
        except Exception as e:
            print(f"❌ 删除记录失败: {e}")
            return 0

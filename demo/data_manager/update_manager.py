"""
数据更新管理器
Update Manager

负责定期数据更新、增量更新、数据清理等功能
"""

import os
import time
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from .config import config
from .api_client import TushareClient
from .database_manager import DatabaseManager
from .basic_data_manager import BasicDataManager

class UpdateManager:
    """数据更新管理器"""
    
    def __init__(self, api_client=None, db_manager=None, basic_manager=None):
        """初始化更新管理器"""
        self.config = config
        self.api = api_client or TushareClient()
        self.db = db_manager or DatabaseManager()
        self.basic = basic_manager or BasicDataManager(self.api)
        
        self.start_time = datetime.now()
        self.log_messages = []
        self.update_stats = {
            'stocks': 0,
            'funds': 0,
            'indexes': 0,
            'industries': 0,
            'total_records': 0
        }
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        self.log_messages.append(log_msg)
    
    # ==================== 股票数据更新 ====================
    
    def update_stocks_data(self, force_update=False, start_date=None, end_date=None):
        """更新股票数据"""
        if force_update:
            self.log("\n📈 开始强制更新股票数据...")
        else:
            self.log("\n📈 开始增量更新股票数据...")
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        success_count = 0
        
        try:
            # 更新股票日线数据
            if not force_update:
                # 增量更新：获取最近几个交易日的数据
                latest_trade_date = self.basic.get_latest_trade_date()
                if latest_trade_date:
                    # 获取最近5个交易日的数据，确保不遗漏
                    prev_trade_date = self.basic.get_previous_trade_date(latest_trade_date, 5)
                    start_date = prev_trade_date if prev_trade_date else latest_trade_date
                else:
                    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            else:
                # 强制更新：从指定日期开始
                if start_date is None:
                    start_date = '20200101'
            
            # 批量获取股票日线数据
            daily_data = self.api.get_batch_stock_daily(start_date, end_date)
            if not daily_data.empty:
                # 过滤有效股票
                current_stocks = self.basic.get_stock_basic()
                if current_stocks is not None:
                    active_codes = set(current_stocks['ts_code'].tolist())
                    before_count = len(daily_data)
                    daily_data = daily_data[daily_data['ts_code'].isin(active_codes)]
                    after_count = len(daily_data)
                    
                    if before_count > after_count:
                        self.log(f"   🔍 过滤退市股票数据: {before_count - after_count} 条")
                
                if not daily_data.empty:
                    self.db.save_to_database(daily_data, 'stock_daily', self.config.stock_db)
                    success_count += len(daily_data)
            
            # 批量获取股票估值数据
            basic_data = self.api.get_batch_stock_basic_data(start_date, end_date)
            if not basic_data.empty:
                # 过滤有效股票
                current_stocks = self.basic.get_stock_basic()
                if current_stocks is not None:
                    active_codes = set(current_stocks['ts_code'].tolist())
                    before_count = len(basic_data)
                    basic_data = basic_data[basic_data['ts_code'].isin(active_codes)]
                    after_count = len(basic_data)
                    
                    if before_count > after_count:
                        self.log(f"   🔍 过滤退市股票数据: {before_count - after_count} 条")
                
                if not basic_data.empty:
                    self.db.save_to_database(basic_data, 'stock_daily_basic', self.config.stock_db)
                    success_count += len(basic_data)
            
            self.update_stats['stocks'] = success_count
            self.update_stats['total_records'] += success_count
            self.log(f"✅ 股票数据更新完成！共更新 {success_count:,} 条记录")
            
        except Exception as e:
            self.log(f"❌ 股票数据更新失败: {e}")
        
        return success_count
    
    # ==================== 基金数据更新 ====================
    
    def update_funds_data(self, force_update=False, start_date=None, end_date=None):
        """更新基金数据"""
        if force_update:
            self.log("\n💰 开始强制更新基金数据...")
        else:
            self.log("\n💰 开始增量更新基金数据...")
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        success_count = 0
        
        try:
            # 获取基金列表
            fund_list = self.basic.get_fund_list()
            if fund_list.empty:
                self.log("⚠️ 无法获取基金列表")
                return 0
            
            # 确定更新日期范围
            if not force_update:
                # 增量更新：获取最近几个交易日的数据
                latest_trade_date = self.basic.get_latest_trade_date()
                if latest_trade_date:
                    # 获取最近5个交易日的数据
                    prev_trade_date = self.basic.get_previous_trade_date(latest_trade_date, 5)
                    start_date = prev_trade_date if prev_trade_date else latest_trade_date
                else:
                    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            else:
                if start_date is None:
                    start_date = '20200101'
            
            # 更新ETF日线数据
            etf_funds = fund_list[fund_list['market'] == 'O']  # ETF基金
            if not etf_funds.empty:
                self.log(f"   更新 {len(etf_funds)} 只ETF数据...")
                
                for _, fund in etf_funds.iterrows():
                    try:
                        etf_data = self.api.get_etf_daily(
                            ts_code=fund['ts_code'],
                            start_date=start_date,
                            end_date=end_date
                        )
                        
                        if not etf_data.empty:
                            self.db.save_to_database(etf_data, 'etf_daily', self.config.fund_db)
                            success_count += len(etf_data)
                        
                        time.sleep(0.1)  # 控制API调用频率
                        
                    except Exception as e:
                        self.log(f"   ⚠️ {fund['ts_code']} ETF数据更新失败: {e}")
                        continue
            
            # 更新基金净值数据
            regular_funds = fund_list[fund_list['market'] == 'E']  # 场外基金
            if not regular_funds.empty:
                self.log(f"   更新 {len(regular_funds)} 只基金净值数据...")
                
                # 分批处理，避免API限制
                batch_size = 50
                for i in range(0, len(regular_funds), batch_size):
                    batch_funds = regular_funds.iloc[i:i+batch_size]
                    
                    for _, fund in batch_funds.iterrows():
                        try:
                            nav_data = self.api.get_fund_nav(
                                ts_code=fund['ts_code'],
                                start_date=start_date,
                                end_date=end_date
                            )
                            
                            if not nav_data.empty:
                                self.db.save_to_database(nav_data, 'fund_nav', self.config.fund_db)
                                success_count += len(nav_data)
                            
                            time.sleep(0.1)  # 控制API调用频率
                            
                        except Exception as e:
                            self.log(f"   ⚠️ {fund['ts_code']} 基金净值更新失败: {e}")
                            continue
                    
                    # 批次间休息
                    if i + batch_size < len(regular_funds):
                        time.sleep(1)
            
            self.update_stats['funds'] = success_count
            self.update_stats['total_records'] += success_count
            self.log(f"✅ 基金数据更新完成！共更新 {success_count:,} 条记录")
            
        except Exception as e:
            self.log(f"❌ 基金数据更新失败: {e}")
        
        return success_count
    
    # ==================== 指数数据更新 ====================
    
    def update_indexes_data(self, force_update=False, start_date=None, end_date=None):
        """更新指数数据"""
        if force_update:
            self.log("\n📊 开始强制更新指数数据...")
        else:
            self.log("\n📊 开始增量更新指数数据...")
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        success_count = 0
        
        try:
            # 获取指数列表
            index_list = self.basic.get_index_list()
            if index_list.empty:
                self.log("⚠️ 无法获取指数列表")
                return 0
            
            # 确定更新日期范围
            if not force_update:
                # 增量更新：获取最近几个交易日的数据
                latest_trade_date = self.basic.get_latest_trade_date()
                if latest_trade_date:
                    # 获取最近5个交易日的数据
                    prev_trade_date = self.basic.get_previous_trade_date(latest_trade_date, 5)
                    start_date = prev_trade_date if prev_trade_date else latest_trade_date
                else:
                    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            else:
                if start_date is None:
                    start_date = '20200101'
            
            # 更新主要指数数据
            major_indexes = [
                '000001.SH',  # 上证指数
                '399001.SZ',  # 深证成指
                '399006.SZ',  # 创业板指
                '000300.SH',  # 沪深300
                '000905.SH',  # 中证500
                '000852.SH',  # 中证1000
            ]
            
            self.log(f"   更新主要指数数据...")
            for ts_code in major_indexes:
                try:
                    index_data = self.api.get_index_daily(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not index_data.empty:
                        self.db.save_to_database(index_data, 'index_daily', self.config.index_db)
                        success_count += len(index_data)
                    
                    time.sleep(0.1)  # 控制API调用频率
                    
                except Exception as e:
                    self.log(f"   ⚠️ {ts_code} 指数数据更新失败: {e}")
                    continue
            
            self.update_stats['indexes'] = success_count
            self.update_stats['total_records'] += success_count
            self.log(f"✅ 指数数据更新完成！共更新 {success_count:,} 条记录")
            
        except Exception as e:
            self.log(f"❌ 指数数据更新失败: {e}")
        
        return success_count
    
    # ==================== 行业数据更新 ====================
    
    def update_industries_data(self, force_update=False, start_date=None, end_date=None):
        """更新行业数据"""
        if force_update:
            self.log("\n🏭 开始强制更新行业数据...")
        else:
            self.log("\n🏭 开始增量更新行业数据...")
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        
        success_count = 0
        
        try:
            # 确定更新日期范围
            if not force_update:
                # 增量更新：获取最近几个交易日的数据
                latest_trade_date = self.basic.get_latest_trade_date()
                if latest_trade_date:
                    # 获取最近5个交易日的数据
                    prev_trade_date = self.basic.get_previous_trade_date(latest_trade_date, 5)
                    start_date = prev_trade_date if prev_trade_date else latest_trade_date
                else:
                    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y%m%d')
            else:
                if start_date is None:
                    start_date = '20200101'
            
            # 更新申万行业指数数据
            self.log(f"   更新申万行业指数数据...")
            
            # 申万一级行业指数代码
            sw_l1_indexes = [
                '801010.SI',  # 农林牧渔
                '801020.SI',  # 采掘
                '801030.SI',  # 化工
                '801040.SI',  # 钢铁
                '801050.SI',  # 有色金属
                '801080.SI',  # 电子
                '801110.SI',  # 家用电器
                '801120.SI',  # 食品饮料
                '801130.SI',  # 纺织服装
                '801140.SI',  # 轻工制造
                '801150.SI',  # 医药生物
                '801160.SI',  # 公用事业
                '801170.SI',  # 交通运输
                '801180.SI',  # 房地产
                '801200.SI',  # 商业贸易
                '801210.SI',  # 休闲服务
                '801230.SI',  # 综合
                '801710.SI',  # 建筑材料
                '801720.SI',  # 建筑装饰
                '801730.SI',  # 电气设备
                '801740.SI',  # 国防军工
                '801750.SI',  # 计算机
                '801760.SI',  # 传媒
                '801770.SI',  # 通信
                '801780.SI',  # 银行
                '801790.SI',  # 非银金融
                '801880.SI',  # 汽车
                '801890.SI',  # 机械设备
            ]
            
            for ts_code in sw_l1_indexes:
                try:
                    sw_data = self.api.get_sw_daily(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not sw_data.empty:
                        # 保存到指数数据库
                        self.db.save_to_database(sw_data, 'index_daily', self.config.index_db)
                        success_count += len(sw_data)
                    
                    time.sleep(0.1)  # 控制API调用频率
                    
                except Exception as e:
                    self.log(f"   ⚠️ {ts_code} 行业指数更新失败: {e}")
                    continue
            
            self.update_stats['industries'] = success_count
            self.update_stats['total_records'] += success_count
            self.log(f"✅ 行业数据更新完成！共更新 {success_count:,} 条记录")
            
        except Exception as e:
            self.log(f"❌ 行业数据更新失败: {e}")

        return success_count

    # ==================== 数据清理功能 ====================

    def cleanup_delisted_data(self, force_cleanup=False):
        """清理退市股票和基金数据"""
        try:
            # 检查是否需要清理（每周一次）
            cleanup_flag_file = os.path.join(self.config.basic_dir, "last_cleanup_date.txt")

            should_cleanup = force_cleanup
            if not force_cleanup:
                if os.path.exists(cleanup_flag_file):
                    with open(cleanup_flag_file, 'r') as f:
                        last_cleanup = f.read().strip()
                        last_cleanup_date = datetime.strptime(last_cleanup, '%Y-%m-%d')
                        days_since_cleanup = (datetime.now() - last_cleanup_date).days
                        should_cleanup = days_since_cleanup >= self.config.cache_settings['cleanup_interval_days']
                else:
                    should_cleanup = True  # 首次运行

            if should_cleanup:
                self.log("\n🧹 开始清理退市股票和基金数据...")

                try:
                    # 查找退市股票
                    current_stocks = self.api.get_active_stocks()
                    if not current_stocks.empty:
                        active_stock_codes = set(current_stocks['ts_code'].tolist())

                        # 从数据库中查找所有股票代码
                        db_stock_codes = set()
                        with sqlite3.connect(self.config.stock_db) as conn:
                            tables = ['stock_daily', 'stock_daily_basic', 'stock_fina_indicator']
                            for table in tables:
                                try:
                                    cursor = conn.execute(f"SELECT DISTINCT ts_code FROM {table}")
                                    codes = [row[0] for row in cursor.fetchall()]
                                    db_stock_codes.update(codes)
                                except sqlite3.OperationalError:
                                    continue

                        delisted_stocks = db_stock_codes - active_stock_codes

                        if delisted_stocks:
                            self.log(f"   发现退市股票: {len(delisted_stocks)} 只")
                            self._clean_delisted_stocks(delisted_stocks)

                    # 查找退市基金
                    current_funds = self.api.get_active_funds()
                    if not current_funds.empty:
                        active_fund_codes = set(current_funds['ts_code'].tolist())

                        # 从数据库中查找所有基金代码
                        db_fund_codes = set()
                        with sqlite3.connect(self.config.fund_db) as conn:
                            tables = ['fund_nav', 'etf_daily']
                            for table in tables:
                                try:
                                    cursor = conn.execute(f"SELECT DISTINCT ts_code FROM {table}")
                                    codes = [row[0] for row in cursor.fetchall()]
                                    db_fund_codes.update(codes)
                                except sqlite3.OperationalError:
                                    continue

                        delisted_funds = db_fund_codes - active_fund_codes

                        if delisted_funds:
                            self.log(f"   发现退市基金: {len(delisted_funds)} 只")
                            self._clean_delisted_funds(delisted_funds)

                    # 更新基础信息文件
                    self.basic.update_all_basic_data(force_update=True)

                    # 更新清理日期标记
                    with open(cleanup_flag_file, 'w') as f:
                        f.write(datetime.now().strftime('%Y-%m-%d'))

                    self.log("✅ 数据清理完成")

                except Exception as e:
                    self.log(f"⚠️ 数据清理失败: {e}")
            else:
                self.log("ℹ️ 距离上次清理不足7天，跳过清理")

        except Exception as e:
            self.log(f"❌ 清理退市数据失败: {e}")

    def _clean_delisted_stocks(self, delisted_stocks):
        """清理退市股票数据"""
        total_deleted = 0
        tables = ['stock_daily', 'stock_daily_basic', 'stock_fina_indicator']

        for ts_code in list(delisted_stocks)[:10]:  # 限制数量，避免过长时间
            for table in tables:
                deleted_count = self.db.delete_records(table, self.config.stock_db, ts_code)
                total_deleted += deleted_count

        if total_deleted > 0:
            self.log(f"   ✅ 清理退市股票数据: {total_deleted} 条记录")

    def _clean_delisted_funds(self, delisted_funds):
        """清理退市基金数据"""
        total_deleted = 0
        tables = ['fund_nav', 'etf_daily']

        for ts_code in list(delisted_funds)[:10]:  # 限制数量，避免过长时间
            for table in tables:
                deleted_count = self.db.delete_records(table, self.config.fund_db, ts_code)
                total_deleted += deleted_count

        if total_deleted > 0:
            self.log(f"   ✅ 清理退市基金数据: {total_deleted} 条记录")

    # ==================== 综合更新功能 ====================

    def update_all(self, force_update=False):
        """全量更新所有数据"""
        if force_update:
            self.log("\n🚀 开始强制全量数据更新...")
        else:
            self.log("\n🚀 开始增量数据更新...")

        # 重置统计
        self.update_stats = {k: 0 for k in self.update_stats}

        # 依次更新各类数据
        self.update_stocks_data(force_update)
        self.update_funds_data(force_update)
        self.update_indexes_data(force_update)
        self.update_industries_data(force_update)

        # 定期清理
        self.cleanup_delisted_data()

        # 更新基础信息
        self.basic.update_all_basic_data(force_update)

        if force_update:
            self.log("🎉 强制全量数据更新完成！")
        else:
            self.log("🎉 增量数据更新完成！")

        return self.update_stats

    def update_quick(self, force_update=False):
        """快速更新（仅更新指数和行业数据）"""
        if force_update:
            self.log("\n⚡ 开始强制快速数据更新...")
        else:
            self.log("\n⚡ 开始增量快速数据更新...")

        # 重置统计
        self.update_stats = {k: 0 for k in self.update_stats}

        # 更新指数数据
        self.update_indexes_data(force_update)

        # 更新行业数据
        self.update_industries_data(force_update)

        # 定期清理
        self.cleanup_delisted_data()

        if force_update:
            self.log("🎉 强制快速数据更新完成！")
        else:
            self.log("🎉 增量快速数据更新完成！")

        return self.update_stats

    # ==================== 统计和报告功能 ====================

    def show_summary(self):
        """显示数据更新汇总"""
        self.log("\n📊 数据更新汇总:")

        # 显示更新统计
        total_time = (datetime.now() - self.start_time).total_seconds()
        self.log(f"⏱️ 总耗时: {total_time:.1f} 秒")
        self.log(f"📈 股票数据: {self.update_stats['stocks']:,} 条")
        self.log(f"💰 基金数据: {self.update_stats['funds']:,} 条")
        self.log(f"📊 指数数据: {self.update_stats['indexes']:,} 条")
        self.log(f"🏭 行业数据: {self.update_stats['industries']:,} 条")
        self.log(f"📋 总记录数: {self.update_stats['total_records']:,} 条")

        # 显示数据库统计
        try:
            # 股票数据库统计
            if os.path.exists(self.config.stock_db):
                stock_size = os.path.getsize(self.config.stock_db) / 1024 / 1024
                self.log(f"📈 股票数据库: {stock_size:.2f} MB")

                with sqlite3.connect(self.config.stock_db) as conn:
                    try:
                        daily_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily").fetchone()[0]
                        basic_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM stock_daily_basic").fetchone()[0]
                        self.log(f"   日线数据: {daily_count:,} 只股票")
                        self.log(f"   估值数据: {basic_count:,} 只股票")
                    except Exception as e:
                        self.log(f"   ⚠️ 股票数据统计获取失败: {e}")

            # 基金数据库统计
            if os.path.exists(self.config.fund_db):
                fund_size = os.path.getsize(self.config.fund_db) / 1024 / 1024
                self.log(f"💰 基金数据库: {fund_size:.2f} MB")

                with sqlite3.connect(self.config.fund_db) as conn:
                    try:
                        etf_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM etf_daily").fetchone()[0]
                        nav_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM fund_nav").fetchone()[0]
                        self.log(f"   ETF数量: {etf_count:,} 只")
                        self.log(f"   基金净值: {nav_count:,} 只")
                    except Exception as e:
                        self.log(f"   ⚠️ 基金数据统计获取失败: {e}")

            # 指数数据库统计
            if os.path.exists(self.config.index_db):
                index_size = os.path.getsize(self.config.index_db) / 1024 / 1024
                self.log(f"📊 指数数据库: {index_size:.2f} MB")

                with sqlite3.connect(self.config.index_db) as conn:
                    try:
                        index_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM index_daily").fetchone()[0]
                        self.log(f"   指数数量: {index_count:,} 只")
                    except Exception as e:
                        self.log(f"   ⚠️ 指数数据统计获取失败: {e}")

        except Exception as e:
            self.log(f"❌ 数据库统计失败: {e}")

        return self.update_stats

    def get_update_report(self):
        """获取更新报告"""
        return {
            'start_time': self.start_time,
            'end_time': datetime.now(),
            'duration': (datetime.now() - self.start_time).total_seconds(),
            'stats': self.update_stats.copy(),
            'log_messages': self.log_messages.copy()
        }

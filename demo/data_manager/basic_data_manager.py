"""
基础数据管理器
Basic Data Manager

负责管理股票、基金、指数等基础信息数据
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from .config import config
from .api_client import TushareClient

class BasicDataManager:
    """基础数据管理器"""
    
    def __init__(self, api_client=None):
        """初始化基础数据管理器"""
        self.config = config
        self.api = api_client or TushareClient()
    
    def _is_cache_valid(self, file_path, max_age_days):
        """检查缓存文件是否有效"""
        if not os.path.exists(file_path):
            return False
        
        file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
        age_days = (datetime.now() - file_time).days
        return age_days < max_age_days
    
    def _load_csv_cache(self, file_path):
        """加载CSV缓存文件"""
        try:
            if os.path.exists(file_path):
                data = pd.read_csv(file_path, encoding='utf-8-sig')
                print(f"📁 从缓存加载: {os.path.basename(file_path)} ({len(data)} 条记录)")
                return data
        except Exception as e:
            print(f"⚠️ 加载缓存失败: {e}")
        return None
    
    def _save_csv_cache(self, data, file_path):
        """保存数据到CSV缓存"""
        try:
            data.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"💾 缓存已保存: {os.path.basename(file_path)} ({len(data)} 条记录)")
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
    
    # ==================== 股票基础信息 ====================
    
    def get_stock_basic(self, force_update=False):
        """获取股票基本信息"""
        file_path = self.config.get_basic_file_path('stock_basic')
        max_age = self.config.cache_settings['stock_basic_max_age_days']
        
        # 检查缓存
        if not force_update and self._is_cache_valid(file_path, max_age):
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                return cached_data
        
        # 从API获取
        print("🔄 从API获取股票基本信息...")
        try:
            stock_basic = self.api.get_active_stocks()
            if not stock_basic.empty:
                self._save_csv_cache(stock_basic, file_path)
                return stock_basic
        except Exception as e:
            print(f"❌ 获取股票基本信息失败: {e}")
            # 尝试返回缓存数据
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                print("⚠️ 使用过期缓存数据")
                return cached_data
        
        return pd.DataFrame()
    
    def get_stock_list(self, exclude_delisted=True):
        """获取股票列表"""
        stock_basic = self.get_stock_basic()
        if stock_basic.empty:
            print("❌ 无法获取股票基本信息")
            return pd.DataFrame()
        
        if exclude_delisted:
            print(f"📊 有效股票数量: {len(stock_basic)} 只（已排除退市股票）")
        
        return stock_basic
    
    # ==================== 基金基础信息 ====================
    
    def get_fund_basic(self, force_update=False):
        """获取基金基本信息"""
        file_path = self.config.get_basic_file_path('fund_basic')
        max_age = self.config.cache_settings['fund_basic_max_age_days']
        
        # 检查缓存
        if not force_update and self._is_cache_valid(file_path, max_age):
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                return cached_data
        
        # 从API获取
        print("🔄 从API获取基金基本信息...")
        try:
            fund_basic = self.api.get_active_funds()
            if not fund_basic.empty:
                self._save_csv_cache(fund_basic, file_path)
                return fund_basic
        except Exception as e:
            print(f"❌ 获取基金基本信息失败: {e}")
            # 尝试返回缓存数据
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                print("⚠️ 使用过期缓存数据")
                return cached_data
        
        return pd.DataFrame()
    
    def get_fund_list(self):
        """获取基金列表"""
        fund_basic = self.get_fund_basic()
        if fund_basic.empty:
            print("❌ 无法获取基金基本信息")
            return pd.DataFrame()
        
        print(f"📊 有效基金数量: {len(fund_basic)} 只")
        return fund_basic
    
    # ==================== 指数基础信息 ====================
    
    def get_index_basic(self, force_update=False):
        """获取指数基本信息"""
        file_path = self.config.get_basic_file_path('index_basic')
        max_age = self.config.cache_settings['index_basic_max_age_days']
        
        # 检查缓存
        if not force_update and self._is_cache_valid(file_path, max_age):
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                return cached_data
        
        # 从API获取
        print("🔄 从API获取指数基本信息...")
        try:
            index_basic = self.api.get_index_basic(market='SSE')
            sz_index = self.api.get_index_basic(market='SZSE')
            
            if not index_basic.empty and not sz_index.empty:
                all_index = pd.concat([index_basic, sz_index], ignore_index=True)
            elif not index_basic.empty:
                all_index = index_basic
            elif not sz_index.empty:
                all_index = sz_index
            else:
                all_index = pd.DataFrame()
            
            if not all_index.empty:
                self._save_csv_cache(all_index, file_path)
                return all_index
        except Exception as e:
            print(f"❌ 获取指数基本信息失败: {e}")
            # 尝试返回缓存数据
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                print("⚠️ 使用过期缓存数据")
                return cached_data
        
        return pd.DataFrame()
    
    def get_index_list(self):
        """获取指数列表"""
        index_basic = self.get_index_basic()
        if index_basic.empty:
            print("❌ 无法获取指数基本信息")
            return pd.DataFrame()
        
        print(f"📊 指数数量: {len(index_basic)} 只")
        return index_basic
    
    # ==================== 交易日历 ====================
    
    def get_trade_calendar(self, force_update=False):
        """获取交易日历"""
        file_path = self.config.get_basic_file_path('trade_calendar')
        max_age = self.config.cache_settings['trade_calendar_max_age_days']
        
        # 检查缓存
        if not force_update and self._is_cache_valid(file_path, max_age):
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                return cached_data
        
        # 从API获取
        print("🔄 从API获取交易日历...")
        try:
            # 获取过去一年到未来一年的交易日历
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            end_date = (datetime.now() + timedelta(days=365)).strftime('%Y%m%d')
            
            trade_cal = self.api.get_trade_calendar(
                exchange='SSE',
                start_date=start_date,
                end_date=end_date
            )
            
            if not trade_cal.empty:
                self._save_csv_cache(trade_cal, file_path)
                return trade_cal
        except Exception as e:
            print(f"❌ 获取交易日历失败: {e}")
            # 尝试返回缓存数据
            cached_data = self._load_csv_cache(file_path)
            if cached_data is not None:
                print("⚠️ 使用过期缓存数据")
                return cached_data
        
        return pd.DataFrame()
    
    def is_trade_date(self, date_str):
        """检查是否为交易日"""
        trade_cal = self.get_trade_calendar()
        if trade_cal.empty:
            return False

        # 确保日期列为字符串类型
        trade_cal['cal_date'] = trade_cal['cal_date'].astype(str)

        trade_dates = trade_cal[trade_cal['is_open'] == 1]['cal_date'].tolist()
        return date_str in trade_dates
    
    def get_latest_trade_date(self):
        """获取最新交易日"""
        trade_cal = self.get_trade_calendar()
        if trade_cal.empty:
            return None

        today = datetime.now().strftime('%Y%m%d')

        # 确保日期列为字符串类型
        trade_cal['cal_date'] = trade_cal['cal_date'].astype(str)

        trade_dates = trade_cal[
            (trade_cal['is_open'] == 1) &
            (trade_cal['cal_date'] <= today)
        ]['cal_date'].tolist()

        return max(trade_dates) if trade_dates else None
    
    def get_previous_trade_date(self, date_str, days=1):
        """获取指定日期前N个交易日"""
        trade_cal = self.get_trade_calendar()
        if trade_cal.empty:
            return None

        # 确保日期列为字符串类型
        trade_cal['cal_date'] = trade_cal['cal_date'].astype(str)

        trade_dates = trade_cal[
            (trade_cal['is_open'] == 1) &
            (trade_cal['cal_date'] < date_str)
        ]['cal_date'].tolist()

        trade_dates.sort(reverse=True)

        if len(trade_dates) >= days:
            return trade_dates[days - 1]
        return None
    
    # ==================== 数据验证 ====================
    
    def validate_stock_code(self, ts_code):
        """验证股票代码是否有效"""
        stock_basic = self.get_stock_basic()
        if stock_basic.empty:
            return False
        
        return ts_code in stock_basic['ts_code'].values
    
    def validate_fund_code(self, ts_code):
        """验证基金代码是否有效"""
        fund_basic = self.get_fund_basic()
        if fund_basic.empty:
            return False
        
        return ts_code in fund_basic['ts_code'].values
    
    def validate_index_code(self, ts_code):
        """验证指数代码是否有效"""
        index_basic = self.get_index_basic()
        if index_basic.empty:
            return False
        
        return ts_code in index_basic['ts_code'].values
    
    # ==================== 批量更新 ====================
    
    def update_all_basic_data(self, force_update=False):
        """更新所有基础数据"""
        print("🔄 更新所有基础数据...")
        
        results = {}
        
        # 更新股票基础信息
        try:
            stock_basic = self.get_stock_basic(force_update)
            results['stock_basic'] = len(stock_basic) if not stock_basic.empty else 0
        except Exception as e:
            print(f"❌ 更新股票基础信息失败: {e}")
            results['stock_basic'] = 0
        
        # 更新基金基础信息
        try:
            fund_basic = self.get_fund_basic(force_update)
            results['fund_basic'] = len(fund_basic) if not fund_basic.empty else 0
        except Exception as e:
            print(f"❌ 更新基金基础信息失败: {e}")
            results['fund_basic'] = 0
        
        # 更新指数基础信息
        try:
            index_basic = self.get_index_basic(force_update)
            results['index_basic'] = len(index_basic) if not index_basic.empty else 0
        except Exception as e:
            print(f"❌ 更新指数基础信息失败: {e}")
            results['index_basic'] = 0
        
        # 更新交易日历
        try:
            trade_cal = self.get_trade_calendar(force_update)
            results['trade_calendar'] = len(trade_cal) if not trade_cal.empty else 0
        except Exception as e:
            print(f"❌ 更新交易日历失败: {e}")
            results['trade_calendar'] = 0
        
        print("✅ 基础数据更新完成:")
        for data_type, count in results.items():
            print(f"   {data_type}: {count} 条记录")
        
        return results

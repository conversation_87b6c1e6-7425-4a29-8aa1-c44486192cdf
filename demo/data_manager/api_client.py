"""
API客户端
API Client

负责与Tushare API的交互
"""

import chinadata.ca_data as ts
import pandas as pd
import time
from .config import config

class TushareClient:
    """Tushare API客户端"""
    
    def __init__(self):
        """初始化API客户端"""
        self.token = config.tushare_token
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        self.rate_limit_delay = 0.2  # API调用间隔
    
    def _api_call_with_retry(self, api_func, max_retries=3, **kwargs):
        """带重试的API调用"""
        for attempt in range(max_retries):
            try:
                time.sleep(self.rate_limit_delay)  # 控制调用频率
                result = api_func(**kwargs)
                
                # 特殊处理pandas构建错误
                if result is None:
                    print(f"⚠️ API返回None (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)
                        continue
                    else:
                        return pd.DataFrame()  # 返回空DataFrame而不是None
                
                # 如果返回的是DataFrame，检查是否有效
                if isinstance(result, pd.DataFrame):
                    return result
                
                # 如果返回其他类型，尝试转换为DataFrame
                try:
                    if hasattr(result, '__iter__') and not isinstance(result, str):
                        df_result = pd.DataFrame(result)
                        return df_result
                    else:
                        print(f"⚠️ API返回非迭代类型: {type(result)}")
                        return pd.DataFrame()
                except Exception as conv_e:
                    print(f"⚠️ 数据转换失败: {conv_e}")
                    return pd.DataFrame()
                
                return result
                
            except ValueError as ve:
                if "scalar values" in str(ve) and "index" in str(ve):
                    print(f"⚠️ pandas构建错误，返回空DataFrame (尝试 {attempt + 1}/{max_retries}): {ve}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)
                        continue
                    else:
                        return pd.DataFrame()  # 返回空DataFrame
                else:
                    raise ve
            except Exception as e:
                if attempt < max_retries - 1:
                    print(f"⚠️ API调用失败，重试中... (尝试 {attempt + 1}/{max_retries}): {e}")
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    print(f"❌ API调用最终失败: {e}")
                    raise e
    
    # ==================== 基础信息API ====================
    
    def get_stock_basic(self, **kwargs):
        """获取股票基本信息"""
        return self._api_call_with_retry(self.pro.stock_basic, **kwargs)
    
    def get_trade_calendar(self, **kwargs):
        """获取交易日历"""
        return self._api_call_with_retry(self.pro.trade_cal, **kwargs)
    
    def get_fund_basic(self, **kwargs):
        """获取基金基本信息"""
        return self._api_call_with_retry(self.pro.fund_basic, **kwargs)
    
    def get_index_basic(self, **kwargs):
        """获取指数基本信息"""
        return self._api_call_with_retry(self.pro.index_basic, **kwargs)
    
    # ==================== 股票数据API ====================
    
    def get_stock_daily(self, **kwargs):
        """获取股票日线数据"""
        return self._api_call_with_retry(self.pro.daily, **kwargs)
    
    def get_stock_daily_basic(self, **kwargs):
        """获取股票估值数据"""
        return self._api_call_with_retry(self.pro.daily_basic, **kwargs)
    
    def get_stock_fina_indicator(self, **kwargs):
        """获取股票财务指标"""
        return self._api_call_with_retry(self.pro.fina_indicator, **kwargs)
    
    def get_stock_fina_mainbz(self, **kwargs):
        """获取主营业务构成"""
        return self._api_call_with_retry(self.pro.fina_mainbz, **kwargs)
    
    def get_stock_forecast(self, **kwargs):
        """获取业绩预告"""
        return self._api_call_with_retry(self.pro.forecast, **kwargs)
    
    def get_stock_dividend(self, **kwargs):
        """获取分红送转数据"""
        return self._api_call_with_retry(self.pro.dividend, **kwargs)
    
    def get_stock_share_float(self, **kwargs):
        """获取限售解禁数据"""
        return self._api_call_with_retry(self.pro.share_float, **kwargs)
    
    def get_stock_holder_number(self, **kwargs):
        """获取股东户数数据"""
        return self._api_call_with_retry(self.pro.stk_holdernumber, **kwargs)
    
    # ==================== 基金数据API ====================
    
    def get_fund_nav(self, **kwargs):
        """获取基金净值数据"""
        return self._api_call_with_retry(self.pro.fund_nav, **kwargs)
    
    def get_etf_daily(self, **kwargs):
        """获取ETF日线数据"""
        return self._api_call_with_retry(self.pro.fund_daily, **kwargs)
    
    # ==================== 指数数据API ====================
    
    def get_index_daily(self, **kwargs):
        """获取指数日线数据"""
        return self._api_call_with_retry(self.pro.index_daily, **kwargs)
    
    def get_index_weight(self, **kwargs):
        """获取指数成分股权重"""
        return self._api_call_with_retry(self.pro.index_weight, **kwargs)
    
    # ==================== 行业数据API ====================
    
    def get_sw_daily(self, **kwargs):
        """获取申万行业指数日线数据"""
        return self._api_call_with_retry(self.pro.sw_daily, **kwargs)
    
    def get_sw_member(self, **kwargs):
        """获取申万行业成分股"""
        return self._api_call_with_retry(self.pro.sw_member, **kwargs)
    
    def get_index_classify(self, **kwargs):
        """获取行业分类数据"""
        return self._api_call_with_retry(self.pro.index_classify, **kwargs)
    
    # ==================== 批量数据获取 ====================
    
    def get_batch_stock_daily(self, start_date, end_date, batch_size=30):
        """批量获取股票日线数据"""
        print(f"📊 批量获取股票日线数据: {start_date} - {end_date}")
        
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        all_data = []
        
        while current_date <= end_dt:
            batch_end = min(current_date + pd.Timedelta(days=batch_size), end_dt)
            batch_start_str = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"  获取日期范围: {batch_start_str} - {batch_end_str}")
            
            try:
                batch_data = self.get_stock_daily(
                    start_date=batch_start_str,
                    end_date=batch_end_str
                )
                
                if not batch_data.empty:
                    all_data.append(batch_data)
                    print(f"  ✅ 获取 {len(batch_data):,} 条记录")
                else:
                    print(f"  ⚠️ 无数据")
                
            except Exception as e:
                print(f"  ❌ 批量获取失败: {e}")
            
            current_date = batch_end + pd.Timedelta(days=1)
        
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            print(f"✅ 批量获取完成: 总计 {len(result):,} 条记录")
            return result
        else:
            print("❌ 批量获取无数据")
            return pd.DataFrame()
    
    def get_batch_stock_basic_data(self, start_date, end_date, batch_size=30):
        """批量获取股票估值数据"""
        print(f"📊 批量获取股票估值数据: {start_date} - {end_date}")
        
        current_date = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        all_data = []
        
        while current_date <= end_dt:
            batch_end = min(current_date + pd.Timedelta(days=batch_size), end_dt)
            batch_start_str = current_date.strftime('%Y%m%d')
            batch_end_str = batch_end.strftime('%Y%m%d')
            
            print(f"  获取日期范围: {batch_start_str} - {batch_end_str}")
            
            try:
                batch_data = self.get_stock_daily_basic(
                    start_date=batch_start_str,
                    end_date=batch_end_str
                )
                
                if not batch_data.empty:
                    all_data.append(batch_data)
                    print(f"  ✅ 获取 {len(batch_data):,} 条记录")
                else:
                    print(f"  ⚠️ 无数据")
                
            except Exception as e:
                print(f"  ❌ 批量获取失败: {e}")
            
            current_date = batch_end + pd.Timedelta(days=1)
        
        if all_data:
            result = pd.concat(all_data, ignore_index=True)
            print(f"✅ 批量获取完成: 总计 {len(result):,} 条记录")
            return result
        else:
            print("❌ 批量获取无数据")
            return pd.DataFrame()
    
    def get_fund_daily(self, start_date=None, end_date=None, ts_codes=None, **kwargs):
        """批量获取ETF日线数据"""
        if start_date and end_date:
            print(f"📊 批量获取ETF数据: {start_date} - {end_date}")
            
            # 如果提供了代码列表，分批处理
            if ts_codes and len(ts_codes) > 0:
                all_data = []
                batch_size = 100  # 每批处理100只ETF
                
                for i in range(0, len(ts_codes), batch_size):
                    batch_codes = ts_codes[i:i+batch_size]
                    codes_str = ','.join(batch_codes)
                    
                    print(f"  批次 {i//batch_size + 1}: 获取 {len(batch_codes)} 只ETF数据")
                    
                    try:
                        batch_result = self._api_call_with_retry(
                            self.pro.fund_daily, 
                            ts_code=codes_str,
                            start_date=start_date, 
                            end_date=end_date, 
                            **kwargs
                        )
                        
                        if batch_result is not None and isinstance(batch_result, pd.DataFrame) and not batch_result.empty:
                            all_data.append(batch_result)
                            print(f"    ✅ 获取 {len(batch_result):,} 条记录")
                        else:
                            print(f"    ⚠️ 批次无数据")
                            
                        time.sleep(0.5)  # 批次间休息
                        
                    except Exception as e:
                        print(f"    ❌ 批次失败: {e}")
                        continue
                
                if all_data:
                    result = pd.concat(all_data, ignore_index=True)
                    print(f"   ✅ 总计获取 {len(result):,} 条ETF数据")
                    return result
                else:
                    print("   ⚠️ 所有批次都失败，返回空DataFrame")
                    return pd.DataFrame()
            else:
                # 没有提供代码列表，尝试直接批量获取（可能失败）
                try:
                    result = self._api_call_with_retry(self.pro.fund_daily, start_date=start_date, end_date=end_date, **kwargs)
                    if result is None or (isinstance(result, pd.DataFrame) and result.empty):
                        print("   ⚠️ 直接批量获取失败，需要提供ETF代码列表")
                        return pd.DataFrame()
                    else:
                        print(f"   ✅ 直接批量获取成功: {len(result):,} 条ETF数据")
                        return result
                except Exception as e:
                    print(f"   ❌ 直接批量获取失败: {e}")
                    return pd.DataFrame()
        else:
            return self._api_call_with_retry(self.pro.fund_daily, **kwargs)
    
    def get_fund_nav(self, start_date=None, end_date=None, ts_codes=None, **kwargs):
        """批量获取基金净值数据"""
        if start_date and end_date:
            print(f"📊 批量获取基金净值数据: {start_date} - {end_date}")

            # 如果提供了代码列表，智能分批处理
            if ts_codes and len(ts_codes) > 0:
                all_data = []

                # 根据基金数量调整批次大小
                if len(ts_codes) <= 100:
                    batch_size = 50  # 小量基金，使用较小批次
                elif len(ts_codes) <= 500:
                    batch_size = 100  # 中量基金，使用中等批次
                else:
                    batch_size = 200  # 大量基金，使用较大批次

                total_batches = (len(ts_codes) + batch_size - 1) // batch_size
                print(f"   将分 {total_batches} 个批次处理，每批最多 {batch_size} 只基金")

                for i in range(0, len(ts_codes), batch_size):
                    batch_codes = ts_codes[i:i+batch_size]
                    codes_str = ','.join(batch_codes)
                    batch_num = i // batch_size + 1

                    print(f"  批次 {batch_num}/{total_batches}: 获取 {len(batch_codes)} 只基金净值数据")

                    try:
                        batch_result = self._api_call_with_retry(
                            self.pro.fund_nav,
                            ts_code=codes_str,
                            start_date=start_date,
                            end_date=end_date,
                            **kwargs
                        )

                        if batch_result is not None and isinstance(batch_result, pd.DataFrame) and not batch_result.empty:
                            all_data.append(batch_result)
                            print(f"    ✅ 获取 {len(batch_result):,} 条记录")
                        else:
                            print(f"    ⚠️ 批次无数据")

                        # 动态调整休息时间
                        if len(ts_codes) > 1000:
                            time.sleep(1.2)  # 大量基金时休息更长
                        elif len(ts_codes) > 500:
                            time.sleep(1.0)  # 中量基金时适中休息
                        else:
                            time.sleep(0.8)  # 少量基金时较短休息

                    except Exception as e:
                        print(f"    ❌ 批次失败: {e}")
                        continue

                if all_data:
                    result = pd.concat(all_data, ignore_index=True)
                    print(f"   ✅ 总计获取 {len(result):,} 条基金净值数据")
                    return result
                else:
                    print("   ⚠️ 所有批次都失败，返回空DataFrame")
                    return pd.DataFrame()
            else:
                # 没有提供代码列表，尝试直接批量获取（通常会失败）
                try:
                    print("   尝试直接批量获取所有基金净值数据...")
                    result = self._api_call_with_retry(self.pro.fund_nav, start_date=start_date, end_date=end_date, **kwargs)
                    if result is None or (isinstance(result, pd.DataFrame) and result.empty):
                        print("   ⚠️ 直接批量获取失败，需要提供基金代码列表")
                        return pd.DataFrame()
                    else:
                        print(f"   ✅ 直接批量获取成功: {len(result):,} 条基金净值数据")
                        return result
                except Exception as e:
                    print(f"   ❌ 直接批量获取失败: {e}")
                    return pd.DataFrame()
        else:
            return self._api_call_with_retry(self.pro.fund_nav, **kwargs)
    
    def get_index_daily(self, start_date=None, end_date=None, **kwargs):
        """批量获取指数日线数据"""
        if start_date and end_date:
            print(f"📊 批量获取指数数据: {start_date} - {end_date}")
            try:
                result = self._api_call_with_retry(self.pro.index_daily, start_date=start_date, end_date=end_date, **kwargs)
                # 验证返回数据
                if result is None:
                    print("   ⚠️ API返回None，返回空DataFrame")
                    return pd.DataFrame()
                elif isinstance(result, pd.DataFrame):
                    if result.empty:
                        print("   ⚠️ API返回空DataFrame")
                    else:
                        print(f"   ✅ 成功获取 {len(result):,} 条指数数据")
                    return result
                else:
                    print(f"   ⚠️ API返回非DataFrame类型: {type(result)}")
                    return pd.DataFrame()
            except Exception as e:
                print(f"   ❌ 批量获取指数数据失败: {e}")
                return pd.DataFrame()
        else:
            return self._api_call_with_retry(self.pro.index_daily, **kwargs)
    
    def get_sw_daily(self, start_date=None, end_date=None, **kwargs):
        """批量获取申万行业指数数据"""
        if start_date and end_date:
            print(f"📊 批量获取申万行业数据: {start_date} - {end_date}")
            try:
                result = self._api_call_with_retry(self.pro.sw_daily, start_date=start_date, end_date=end_date, **kwargs)
                # 验证返回数据
                if result is None:
                    print("   ⚠️ API返回None，返回空DataFrame")
                    return pd.DataFrame()
                elif isinstance(result, pd.DataFrame):
                    if result.empty:
                        print("   ⚠️ API返回空DataFrame")
                    else:
                        print(f"   ✅ 成功获取 {len(result):,} 条申万行业数据")
                    return result
                else:
                    print(f"   ⚠️ API返回非DataFrame类型: {type(result)}")
                    return pd.DataFrame()
            except Exception as e:
                print(f"   ❌ 批量获取申万行业数据失败: {e}")
                return pd.DataFrame()
        else:
            return self._api_call_with_retry(self.pro.sw_daily, **kwargs)
    
    def check_stock_status(self, ts_code):
        """检查股票状态"""
        try:
            stock_info = self.get_stock_basic(ts_code=ts_code, fields='ts_code,list_status')
            if stock_info.empty:
                return 'DELISTED'  # 查不到信息，可能已退市
            
            list_status = stock_info['list_status'].iloc[0]
            return list_status  # L=上市, D=退市, P=暂停上市
            
        except Exception as e:
            print(f"⚠️ 检查 {ts_code} 状态失败: {e}")
            return 'UNKNOWN'
    
    def get_active_stocks(self):
        """获取当前有效的股票列表"""
        try:
            # 获取正常上市的股票
            listed_stocks = self.get_stock_basic(
                exchange='', 
                list_status='L',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            # 获取暂停上市的股票（也保留）
            paused_stocks = self.get_stock_basic(
                exchange='', 
                list_status='P',
                fields='ts_code,symbol,name,area,industry,market,list_date'
            )
            
            # 合并
            active_stocks = pd.concat([listed_stocks, paused_stocks], ignore_index=True)
            
            print(f"✅ 获取有效股票: {len(active_stocks)} 只")
            print(f"   正常上市: {len(listed_stocks)} 只")
            print(f"   暂停上市: {len(paused_stocks)} 只")
            
            return active_stocks
            
        except Exception as e:
            print(f"❌ 获取有效股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_active_funds(self):
        """获取当前有效的基金列表"""
        try:
            # 获取场外基金
            fund_basic = self.get_fund_basic(market='E', status='L')
            # 获取ETF基金
            etf_basic = self.get_fund_basic(market='O', status='L')
            
            # 合并
            active_funds = pd.concat([fund_basic, etf_basic], ignore_index=True)
            
            print(f"✅ 获取有效基金: {len(active_funds)} 只")
            print(f"   场外基金: {len(fund_basic)} 只")
            print(f"   ETF基金: {len(etf_basic)} 只")
            
            return active_funds
            
        except Exception as e:
            print(f"❌ 获取有效基金列表失败: {e}")
            return pd.DataFrame()

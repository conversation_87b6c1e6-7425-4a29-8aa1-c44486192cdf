"""
数据可视化管理器
Visualization Manager

提供K线图、均线、技术指标等可视化功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
import platform
import matplotlib.font_manager as fm

def setup_chinese_fonts():
    """设置中文字体"""
    system = platform.system()

    if system == 'Darwin':  # macOS
        # macOS 系统字体优先级
        font_candidates = [
            'PingFang HK',      # macOS 中文字体
            'PingFang SC',      # macOS 默认中文字体
            'Hiragino Sans GB', # macOS 中文字体
            'STHeiti',          # 华文黑体
            'Heiti TC',         # 繁体中文黑体
            'Kaiti SC',         # 楷体
            'Arial Unicode MS', # 支持中文的Arial
            'SimSong',          # 宋体
        ]
    elif system == 'Windows':
        font_candidates = [
            'SimHei',           # 黑体
            'Microsoft YaHei', # 微软雅黑
            'SimSun',          # 宋体
            'KaiTi',           # 楷体
        ]
    else:  # Linux
        font_candidates = [
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'Noto Sans CJK SC',
            'Source Han Sans SC',
            'DejaVu Sans',
        ]

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    # 选择第一个可用的中文字体
    selected_font = None
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            print(f"✅ 使用中文字体: {font}")
            break

    if selected_font:
        # 强制设置字体，清除默认字体列表
        plt.rcParams['font.sans-serif'] = [selected_font]
        plt.rcParams['font.family'] = 'sans-serif'
    else:
        print("⚠️ 未找到合适的中文字体，使用默认字体")
        plt.rcParams['font.sans-serif'] = font_candidates

    plt.rcParams['axes.unicode_minus'] = False

    # 强制重新加载字体缓存
    fm._rebuild()

    return selected_font

# 初始化字体
setup_chinese_fonts()

# 尝试设置样式，如果失败则使用默认样式
try:
    plt.style.use('seaborn-v0_8')
except:
    try:
        plt.style.use('seaborn')
    except:
        pass  # 使用默认样式

class VisualizationManager:
    """数据可视化管理器"""
    
    def __init__(self, data_cache=None):
        """初始化可视化管理器"""
        if data_cache is None:
            from .data_cache_facade import DataCache
            self.cache = DataCache()
        else:
            self.cache = data_cache
    
    def _prepare_data(self, data):
        """准备数据：转换日期格式，排序等"""
        if data.empty:
            return data
        
        # 复制数据避免修改原数据
        df = data.copy()
        
        # 转换日期格式
        if 'trade_date' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            df.set_index('trade_date', inplace=True)
        elif 'nav_date' in df.columns:
            df['nav_date'] = pd.to_datetime(df['nav_date'])
            df = df.sort_values('nav_date')
            df.set_index('nav_date', inplace=True)
        
        return df
    
    def _calculate_moving_averages(self, data, periods=[5, 10, 20, 60]):
        """计算移动平均线"""
        df = data.copy()
        
        for period in periods:
            if len(df) >= period:
                df[f'MA{period}'] = df['close'].rolling(window=period).mean()
        
        return df
    
    def _calculate_technical_indicators(self, data):
        """计算技术指标"""
        df = data.copy()
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_hist'] = df['MACD'] - df['MACD_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['BB_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
        
        # 成交量移动平均
        df['VOL_MA5'] = df['vol'].rolling(window=5).mean()
        df['VOL_MA10'] = df['vol'].rolling(window=10).mean()
        
        return df
    
    def plot_kline(self, ts_code, days=120, ma_periods=[5, 10, 20, 60], 
                   show_volume=True, show_macd=True, figsize=(15, 10)):
        """绘制K线图"""
        print(f"📊 绘制 {ts_code} K线图...")
        
        # 获取数据
        data = self.cache.get_stock_daily(ts_code, from_db=True)
        if data.empty:
            print(f"❌ 无法获取 {ts_code} 的数据")
            return None
        
        # 准备数据
        df = self._prepare_data(data)
        
        # 只取最近的数据
        if len(df) > days:
            df = df.tail(days)
        
        # 计算技术指标
        df = self._calculate_moving_averages(df, ma_periods)
        df = self._calculate_technical_indicators(df)
        
        # 获取股票名称
        stock_basic = self.cache.get_stock_basic()
        stock_name = ts_code
        if not stock_basic.empty:
            stock_info = stock_basic[stock_basic['ts_code'] == ts_code]
            if not stock_info.empty:
                stock_name = f"{stock_info['name'].iloc[0]}({ts_code})"
        
        # 创建子图
        if show_volume and show_macd:
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=figsize, 
                                               gridspec_kw={'height_ratios': [3, 1, 1]})
        elif show_volume or show_macd:
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize, 
                                          gridspec_kw={'height_ratios': [3, 1]})
        else:
            fig, ax1 = plt.subplots(1, 1, figsize=figsize)
        
        # 绘制K线图
        self._plot_candlestick(ax1, df)
        
        # 绘制移动平均线
        colors = ['orange', 'green', 'red', 'purple', 'brown']
        for i, period in enumerate(ma_periods):
            if f'MA{period}' in df.columns:
                ax1.plot(df.index, df[f'MA{period}'], 
                        label=f'MA{period}', color=colors[i % len(colors)], linewidth=1)
        
        # 设置主图标题和标签
        ax1.set_title(f'{stock_name} K线图', fontsize=16, fontweight='bold')
        ax1.set_ylabel('价格 (元)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 绘制成交量
        if show_volume:
            if show_macd:
                vol_ax = ax2
                macd_ax = ax3
            else:
                vol_ax = ax2
                macd_ax = None
            
            self._plot_volume(vol_ax, df)
        
        # 绘制MACD
        if show_macd:
            if show_volume:
                macd_ax = ax3
            else:
                macd_ax = ax2
            
            self._plot_macd(macd_ax, df)
        
        # 设置x轴日期格式
        for ax in fig.get_axes():
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"kline_{ts_code}_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ K线图已保存: {filename}")
        
        plt.show()
        return fig
    
    def _plot_candlestick(self, ax, df):
        """绘制蜡烛图"""
        # 计算涨跌
        up = df['close'] >= df['open']
        down = ~up
        
        # 绘制蜡烛体
        width = 0.6
        width2 = 0.05
        
        # 上涨蜡烛（红色）
        ax.bar(df.index[up], df['close'][up] - df['open'][up], width, 
               bottom=df['open'][up], color='red', alpha=0.8)
        ax.bar(df.index[up], df['high'][up] - df['close'][up], width2, 
               bottom=df['close'][up], color='red')
        ax.bar(df.index[up], df['low'][up] - df['open'][up], width2, 
               bottom=df['open'][up], color='red')
        
        # 下跌蜡烛（绿色）
        ax.bar(df.index[down], df['close'][down] - df['open'][down], width, 
               bottom=df['open'][down], color='green', alpha=0.8)
        ax.bar(df.index[down], df['high'][down] - df['open'][down], width2, 
               bottom=df['open'][down], color='green')
        ax.bar(df.index[down], df['low'][down] - df['close'][down], width2, 
               bottom=df['close'][down], color='green')
    
    def _plot_volume(self, ax, df):
        """绘制成交量"""
        # 成交量柱状图
        up = df['close'] >= df['open']
        down = ~up
        
        ax.bar(df.index[up], df['vol'][up], color='red', alpha=0.6, width=0.8)
        ax.bar(df.index[down], df['vol'][down], color='green', alpha=0.6, width=0.8)
        
        # 成交量移动平均线
        if 'VOL_MA5' in df.columns:
            ax.plot(df.index, df['VOL_MA5'], color='orange', linewidth=1, label='VOL_MA5')
        if 'VOL_MA10' in df.columns:
            ax.plot(df.index, df['VOL_MA10'], color='blue', linewidth=1, label='VOL_MA10')
        
        ax.set_ylabel('成交量', fontsize=12)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _plot_macd(self, ax, df):
        """绘制MACD"""
        if 'MACD' in df.columns:
            # MACD线
            ax.plot(df.index, df['MACD'], color='blue', linewidth=1, label='MACD')
            ax.plot(df.index, df['MACD_signal'], color='red', linewidth=1, label='Signal')
            
            # MACD柱状图
            colors = ['red' if x >= 0 else 'green' for x in df['MACD_hist']]
            ax.bar(df.index, df['MACD_hist'], color=colors, alpha=0.6, width=0.8)
            
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax.set_ylabel('MACD', fontsize=12)
            ax.legend(loc='upper left')
            ax.grid(True, alpha=0.3)
    
    def plot_comparison(self, ts_codes, days=120, figsize=(15, 8)):
        """绘制多股票对比图"""
        print(f"📊 绘制多股票对比图: {ts_codes}")
        
        fig, ax = plt.subplots(figsize=figsize)
        
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']
        
        for i, ts_code in enumerate(ts_codes):
            # 获取数据
            data = self.cache.get_stock_daily(ts_code, from_db=True)
            if data.empty:
                print(f"⚠️ 无法获取 {ts_code} 的数据")
                continue
            
            # 准备数据
            df = self._prepare_data(data)
            
            # 只取最近的数据
            if len(df) > days:
                df = df.tail(days)
            
            # 计算归一化价格（以第一天为基准）
            if not df.empty:
                normalized_price = (df['close'] / df['close'].iloc[0] - 1) * 100
                
                # 获取股票名称
                stock_basic = self.cache.get_stock_basic()
                stock_name = ts_code
                if not stock_basic.empty:
                    stock_info = stock_basic[stock_basic['ts_code'] == ts_code]
                    if not stock_info.empty:
                        stock_name = stock_info['name'].iloc[0]
                
                ax.plot(df.index, normalized_price, 
                       color=colors[i % len(colors)], linewidth=2, label=stock_name)
        
        ax.set_title('股票价格对比图（归一化）', fontsize=16, fontweight='bold')
        ax.set_ylabel('涨跌幅 (%)', fontsize=12)
        ax.set_xlabel('日期', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 设置x轴日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.MonthLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        # 保存图片
        filename = f"comparison_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 对比图已保存: {filename}")
        
        plt.show()
        return fig

    def plot_technical_analysis(self, ts_code, days=120, figsize=(15, 12)):
        """绘制技术分析图表"""
        print(f"📊 绘制 {ts_code} 技术分析图...")

        # 获取数据
        data = self.cache.get_stock_daily(ts_code, from_db=True)
        if data.empty:
            print(f"❌ 无法获取 {ts_code} 的数据")
            return None

        # 准备数据
        df = self._prepare_data(data)

        # 只取最近的数据
        if len(df) > days:
            df = df.tail(days)

        # 计算技术指标
        df = self._calculate_technical_indicators(df)

        # 获取股票名称
        stock_basic = self.cache.get_stock_basic()
        stock_name = ts_code
        if not stock_basic.empty:
            stock_info = stock_basic[stock_basic['ts_code'] == ts_code]
            if not stock_info.empty:
                stock_name = f"{stock_info['name'].iloc[0]}({ts_code})"

        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=figsize)

        # 1. 价格与布林带
        ax1.plot(df.index, df['close'], color='black', linewidth=2, label='收盘价')
        ax1.plot(df.index, df['BB_upper'], color='red', linewidth=1, label='布林上轨')
        ax1.plot(df.index, df['BB_middle'], color='blue', linewidth=1, label='布林中轨')
        ax1.plot(df.index, df['BB_lower'], color='green', linewidth=1, label='布林下轨')
        ax1.fill_between(df.index, df['BB_upper'], df['BB_lower'], alpha=0.1, color='gray')
        ax1.set_title(f'{stock_name} - 布林带', fontweight='bold')
        ax1.set_ylabel('价格 (元)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. RSI
        ax2.plot(df.index, df['RSI'], color='purple', linewidth=2)
        ax2.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='超买线(70)')
        ax2.axhline(y=30, color='green', linestyle='--', alpha=0.7, label='超卖线(30)')
        ax2.axhline(y=50, color='gray', linestyle='-', alpha=0.5)
        ax2.fill_between(df.index, 70, 100, alpha=0.1, color='red')
        ax2.fill_between(df.index, 0, 30, alpha=0.1, color='green')
        ax2.set_title('RSI 相对强弱指标', fontweight='bold')
        ax2.set_ylabel('RSI')
        ax2.set_ylim(0, 100)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. MACD
        ax3.plot(df.index, df['MACD'], color='blue', linewidth=1, label='MACD')
        ax3.plot(df.index, df['MACD_signal'], color='red', linewidth=1, label='Signal')
        colors = ['red' if x >= 0 else 'green' for x in df['MACD_hist']]
        ax3.bar(df.index, df['MACD_hist'], color=colors, alpha=0.6, width=0.8, label='Histogram')
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax3.set_title('MACD 指标', fontweight='bold')
        ax3.set_ylabel('MACD')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 成交量分析
        up = df['close'] >= df['open']
        down = ~up
        ax4.bar(df.index[up], df['vol'][up], color='red', alpha=0.6, width=0.8)
        ax4.bar(df.index[down], df['vol'][down], color='green', alpha=0.6, width=0.8)
        if 'VOL_MA5' in df.columns:
            ax4.plot(df.index, df['VOL_MA5'], color='orange', linewidth=1, label='VOL_MA5')
        if 'VOL_MA10' in df.columns:
            ax4.plot(df.index, df['VOL_MA10'], color='blue', linewidth=1, label='VOL_MA10')
        ax4.set_title('成交量分析', fontweight='bold')
        ax4.set_ylabel('成交量')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 设置x轴日期格式
        for ax in [ax1, ax2, ax3, ax4]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图片
        filename = f"technical_{ts_code}_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 技术分析图已保存: {filename}")

        plt.show()
        return fig

    def plot_fund_nav(self, ts_code, days=120, figsize=(12, 8)):
        """绘制基金净值图"""
        print(f"📊 绘制 {ts_code} 基金净值图...")

        # 获取基金数据
        if ts_code.endswith('.OF'):
            # 场外基金
            data = self.cache.get_fund_nav(ts_code, from_db=True)
            price_col = 'unit_nav'
            date_col = 'nav_date'
        else:
            # ETF基金
            data = self.cache.get_etf_daily(ts_code, from_db=True)
            price_col = 'close'
            date_col = 'trade_date'

        if data.empty:
            print(f"❌ 无法获取 {ts_code} 的数据")
            return None

        # 准备数据
        df = data.copy()
        df[date_col] = pd.to_datetime(df[date_col])
        df = df.sort_values(date_col)
        df.set_index(date_col, inplace=True)

        # 只取最近的数据
        if len(df) > days:
            df = df.tail(days)

        # 计算移动平均线
        df['MA5'] = df[price_col].rolling(window=5).mean()
        df['MA10'] = df[price_col].rolling(window=10).mean()
        df['MA20'] = df[price_col].rolling(window=20).mean()

        # 获取基金名称
        fund_basic = self.cache.get_fund_basic()
        fund_name = ts_code
        if not fund_basic.empty:
            fund_info = fund_basic[fund_basic['ts_code'] == ts_code]
            if not fund_info.empty:
                fund_name = f"{fund_info['name'].iloc[0]}({ts_code})"

        # 绘图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize,
                                      gridspec_kw={'height_ratios': [3, 1]})

        # 净值走势
        ax1.plot(df.index, df[price_col], color='blue', linewidth=2, label='净值')
        ax1.plot(df.index, df['MA5'], color='orange', linewidth=1, label='MA5')
        ax1.plot(df.index, df['MA10'], color='green', linewidth=1, label='MA10')
        ax1.plot(df.index, df['MA20'], color='red', linewidth=1, label='MA20')

        ax1.set_title(f'{fund_name} 净值走势', fontsize=16, fontweight='bold')
        ax1.set_ylabel('净值', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 涨跌幅
        if len(df) > 1:
            returns = df[price_col].pct_change() * 100
            colors = ['red' if x >= 0 else 'green' for x in returns]
            ax2.bar(df.index, returns, color=colors, alpha=0.6, width=0.8)
            ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            ax2.set_ylabel('涨跌幅 (%)', fontsize=12)
            ax2.grid(True, alpha=0.3)

        # 设置x轴日期格式
        for ax in [ax1, ax2]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # 保存图片
        filename = f"fund_{ts_code}_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 基金净值图已保存: {filename}")

        plt.show()
        return fig

    def plot_sector_analysis(self, figsize=(15, 10)):
        """绘制行业分析图"""
        print("📊 绘制行业分析图...")

        # 直接从数据库查询申万行业指数数据
        import sqlite3
        try:
            with sqlite3.connect(self.cache.config.index_db) as conn:
                query = """
                SELECT ts_code, trade_date, close
                FROM index_daily
                WHERE ts_code LIKE '801%.SI'
                ORDER BY ts_code, trade_date
                """
                index_data = pd.read_sql(query, conn)
        except Exception as e:
            print(f"❌ 无法获取行业指数数据: {e}")
            return None

        if index_data.empty:
            print("❌ 无申万行业指数数据")
            return None

        # 准备数据
        df = index_data.copy()
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df = df.sort_values('trade_date')

        # 计算各行业最近30天的涨跌幅
        sector_performance = []
        for ts_code in df['ts_code'].unique():
            sector_data = df[df['ts_code'] == ts_code].tail(30)
            if len(sector_data) >= 2:
                start_price = sector_data['close'].iloc[0]
                end_price = sector_data['close'].iloc[-1]
                performance = (end_price / start_price - 1) * 100

                # 行业名称映射
                sector_name = self._get_sector_name(ts_code)
                sector_performance.append({
                    'sector': sector_name,
                    'performance': performance,
                    'ts_code': ts_code
                })

        if not sector_performance:
            print("❌ 无法计算行业表现")
            return None

        # 转换为DataFrame并排序
        perf_df = pd.DataFrame(sector_performance)
        perf_df = perf_df.sort_values('performance', ascending=True)

        # 绘图
        fig, ax = plt.subplots(figsize=figsize)

        colors = ['red' if x >= 0 else 'green' for x in perf_df['performance']]
        bars = ax.barh(perf_df['sector'], perf_df['performance'], color=colors, alpha=0.7)

        # 添加数值标签
        for i, (bar, value) in enumerate(zip(bars, perf_df['performance'])):
            ax.text(value + (0.1 if value >= 0 else -0.1), bar.get_y() + bar.get_height()/2,
                   f'{value:.1f}%', ha='left' if value >= 0 else 'right', va='center')

        ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        ax.set_title('申万行业指数表现（最近30天）', fontsize=16, fontweight='bold')
        ax.set_xlabel('涨跌幅 (%)', fontsize=12)
        ax.grid(True, alpha=0.3, axis='x')

        plt.tight_layout()

        # 保存图片
        filename = f"sector_analysis_{datetime.now().strftime('%Y%m%d')}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✅ 行业分析图已保存: {filename}")

        plt.show()
        return fig

    def _get_sector_name(self, ts_code):
        """获取行业名称"""
        sector_map = {
            '801010.SI': '农林牧渔', '801020.SI': '采掘', '801030.SI': '化工',
            '801040.SI': '钢铁', '801050.SI': '有色金属', '801080.SI': '电子',
            '801110.SI': '家用电器', '801120.SI': '食品饮料', '801130.SI': '纺织服装',
            '801140.SI': '轻工制造', '801150.SI': '医药生物', '801160.SI': '公用事业',
            '801170.SI': '交通运输', '801180.SI': '房地产', '801200.SI': '商业贸易',
            '801210.SI': '休闲服务', '801230.SI': '综合', '801710.SI': '建筑材料',
            '801720.SI': '建筑装饰', '801730.SI': '电气设备', '801740.SI': '国防军工',
            '801750.SI': '计算机', '801760.SI': '传媒', '801770.SI': '通信',
            '801780.SI': '银行', '801790.SI': '非银金融', '801880.SI': '汽车',
            '801890.SI': '机械设备'
        }
        return sector_map.get(ts_code, ts_code)

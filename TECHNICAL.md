# 技术实现文档 (Technical Implementation)

## 🏗️ 系统架构

### 1. 整体架构设计

```
┌─────────────────────────────────────────────────────────┐
│                   用户接口层 (UI Layer)                    │
├─────────────────┬─────────────────┬─────────────────────┤
│  数据缓存管理器    │    数据分析器     │    信息查看器        │
│ DataCacheManager │ DataAnalyzer    │ InfoViewer         │
└─────────────────┴─────────────────┴─────────────────────┘
                            │
├─────────────────────────────────────────────────────────┤
│                   业务逻辑层 (Business Layer)              │
├─────────────────────────────────────────────────────────┤
│    数据获取     │    数据处理     │    数据分析     │  数据展示  │
│  Data Fetching │ Data Processing│ Data Analysis │Data Display│
└─────────────────┴─────────────────┴─────────────────┴─────────┘
                            │
├─────────────────────────────────────────────────────────┤
│                   数据存储层 (Storage Layer)               │
├─────────────────────────────────────────────────────────┤
│  CSV文件存储    │     SQLite数据库     │    API接口      │
│  (基础信息)     │    (时序数据)       │   (数据源)      │
└─────────────────┴─────────────────────┴─────────────────┘
```

### 2. 核心组件

#### DataCache 类 (数据缓存管理器)
- **职责**: 数据获取、缓存、增量更新
- **特性**: 
  - 混合存储策略 (CSV + SQLite)
  - 智能增量更新机制
  - API频率控制
  - 数据完整性保证

#### 数据分析模块
- **职责**: 数据统计分析、技术指标计算
- **特性**:
  - 多维度数据分析
  - 相关性分析
  - 估值趋势分析
  - 导出功能

#### 信息查看器
- **职责**: 交互式数据查询和展示
- **特性**:
  - 命令行交互界面
  - 多格式数据展示
  - 实时数据查询

## 💾 数据存储设计

### 1. 混合存储策略

#### CSV文件存储 (静态数据)
- **用途**: 基础信息、元数据
- **优势**: 可读性强、易于备份、跨平台兼容
- **存储内容**:
  - 股票基本信息 (`stock_basic.csv`)
  - 交易日历 (`trade_calendar.csv`)
  - 基金基本信息 (`fund_basic.csv`)
  - 指数基本信息 (`index_basic.csv`)
  - 行业分类信息 (`industry_classify.csv`)

#### SQLite数据库存储 (时序数据)
- **用途**: 历史时序数据、财务数据
- **优势**: 查询性能好、支持复杂查询、事务安全
- **数据库设计**:

```sql
-- 股票数据库 (stock_data.db)
stock_data.db
├── stock_daily              -- 股票日线数据
├── stock_daily_basic        -- 每日估值指标
├── stock_fina_indicator     -- 财务指标数据
├── stock_forecast           -- 业绩预告
├── stock_adj_factor         -- 复权因子
├── stock_dividend           -- 分红送转
├── stock_share_float        -- 限售解禁
├── stock_holder_number      -- 股东户数
├── index_daily              -- 指数日线数据
├── index_weight             -- 指数成分股权重
├── industry_stock           -- 行业成分股
├── industry_daily           -- 行业指数日线
├── sw_industry_daily        -- 申万行业指数
└── stock_industry_classify  -- 行业分类

-- 基金数据库 (fund_data.db)
fund_data.db
├── etf_daily                -- ETF日线数据
└── fund_nav                 -- 基金净值数据
```

### 2. 表结构设计

#### 核心原则
1. **主键设计**: (ts_code, trade_date) 复合主键保证唯一性
2. **索引优化**: 在代码字段和日期字段上建立索引
3. **字段类型**: 合理选择TEXT/REAL类型
4. **扩展性**: 支持动态添加字段

#### 重要表结构示例

```sql
-- 申万行业指数日线数据表
CREATE TABLE sw_industry_daily (
    ts_code TEXT,           -- 指数代码
    trade_date TEXT,        -- 交易日期
    name TEXT,              -- 指数名称
    open REAL,              -- 开盘价
    high REAL,              -- 最高价
    low REAL,               -- 最低价
    close REAL,             -- 收盘价
    pre_close REAL,         -- 前收盘价
    change REAL,            -- 涨跌额
    pct_change REAL,        -- 涨跌幅
    vol REAL,               -- 成交量
    amount REAL,            -- 成交额
    pe REAL,                -- 市盈率
    pb REAL,                -- 市净率
    ps REAL,                -- 市销率
    pcf REAL,               -- 市现率
    market_cap REAL,        -- 总市值
    turnover_rate REAL,     -- 换手率
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (ts_code, trade_date)
);
```

## 🔧 核心算法实现

### 1. 增量更新机制

```python
def get_stock_daily(self, ts_code, start_date='20100101', end_date=None, force_update=False):
    """智能增量更新算法"""
    
    # 1. 检查是否强制更新
    if not force_update:
        # 2. 从数据库加载现有数据
        existing_data = self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
        
        if not existing_data.empty:
            # 3. 获取最新数据日期
            latest_date = self.get_latest_date('stock_daily', self.stock_db, ts_code, 'trade_date')
            
            if latest_date:
                latest_dt = pd.to_datetime(latest_date)
                end_dt = pd.to_datetime(end_date)
                
                # 4. 判断是否需要更新
                if latest_dt >= end_dt - timedelta(days=3):
                    return existing_data  # 使用缓存
                else:
                    # 5. 设置增量更新起始日期
                    start_date = (latest_dt + timedelta(days=1)).strftime('%Y%m%d')
    
    # 6. 获取新数据并保存
    new_data = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
    self.save_to_database(new_data, 'stock_daily', self.stock_db)
    
    return self.load_from_database('stock_daily', self.stock_db, ts_code=ts_code)
```

### 2. 动态字段扩展

```python
def _ensure_sw_industry_columns(self, api_columns):
    """动态字段扩展算法"""
    
    with sqlite3.connect(self.stock_db) as conn:
        # 1. 获取当前表结构
        cursor = conn.execute("PRAGMA table_info(sw_industry_daily)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        # 2. 识别缺失字段
        missing_columns = []
        for col in api_columns:
            if col not in existing_columns and col != 'updated_at':
                missing_columns.append(col)
        
        # 3. 动态添加字段
        for col in missing_columns:
            # 根据字段名推断数据类型
            col_type = 'TEXT' if col in ['ts_code', 'trade_date', 'name'] else 'REAL'
            
            try:
                alter_sql = f"ALTER TABLE sw_industry_daily ADD COLUMN {col} {col_type}"
                conn.execute(alter_sql)
                print(f"✅ 添加字段: {col} ({col_type})")
            except Exception as e:
                print(f"⚠️ 添加字段 {col} 失败: {e}")
```

### 3. 数据库操作优化

```python
def save_to_database(self, data, table_name, db_path):
    """优化的数据库保存操作"""
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with sqlite3.connect(db_path, timeout=30.0) as conn:
                # 1. 设置WAL模式以减少锁定
                conn.execute('PRAGMA journal_mode=WAL')
                conn.execute('PRAGMA synchronous=NORMAL')
                
                # 2. 批量插入数据
                data.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
                return
                
        except Exception as e:
            if "database is locked" in str(e) and attempt < max_retries - 1:
                # 3. 数据库锁定重试机制
                time.sleep(1.0 * (attempt + 1))  # 递增等待时间
                continue
            elif "UNIQUE constraint failed" in str(e):
                # 4. 处理重复数据
                self._save_with_replace(data, table_name, db_path)
                return
```

## 📊 数据分析算法

### 1. 波动率计算 (SQLite兼容)

由于SQLite不支持`STDDEV`函数，我们实现了手动计算标准差的方法：

```sql
-- SQLite兼容的标准差计算
WITH recent_data AS (
    SELECT 
        ts_code,
        name,
        pct_change,
        AVG(pct_change) OVER (PARTITION BY ts_code) as avg_pct
    FROM sw_industry_daily
    WHERE trade_date >= (SELECT date(MAX(trade_date), '-30 days') FROM sw_industry_daily)
    AND pct_change IS NOT NULL
),
variance_calc AS (
    SELECT 
        ts_code,
        name,
        pct_change,
        avg_pct,
        (pct_change - avg_pct) * (pct_change - avg_pct) as squared_diff
    FROM recent_data
)
SELECT 
    ts_code,
    MAX(name) as name,
    AVG(pct_change) as avg_return,
    SQRT(AVG(squared_diff)) as volatility,  -- 手动计算标准差
    MIN(pct_change) as min_return,
    MAX(pct_change) as max_return,
    COUNT(*) as days_count
FROM variance_calc
GROUP BY ts_code
HAVING COUNT(*) >= 20
ORDER BY volatility DESC
```

### 2. 相关性分析

```python
def analyze_sw_industry_correlation():
    """行业相关性分析"""
    
    # 1. 获取所有行业的收益率数据
    query = """
    SELECT ts_code, name, trade_date, pct_change
    FROM sw_industry_daily 
    WHERE trade_date >= date('now', '-252 days')  -- 近一年数据
    AND pct_change IS NOT NULL
    ORDER BY trade_date
    """
    
    # 2. 构建收益率矩阵
    pivot_df = df.pivot(index='trade_date', columns='name', values='pct_change')
    
    # 3. 计算相关性矩阵
    correlation_matrix = pivot_df.corr()
    
    # 4. 可视化展示
    plt.figure(figsize=(12, 10))
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
    plt.title('申万行业指数相关性矩阵')
    plt.show()
```

### 3. 估值趋势分析

```python
def analyze_sw_valuation_trend():
    """估值趋势分析"""
    
    # 1. 获取历史估值数据
    query = """
    SELECT trade_date, AVG(pe) as avg_pe, AVG(pb) as avg_pb, 
           AVG(ps) as avg_ps, COUNT(*) as industry_count
    FROM sw_industry_daily 
    WHERE pe IS NOT NULL AND pb IS NOT NULL 
    GROUP BY trade_date 
    ORDER BY trade_date
    """
    
    # 2. 计算分位数
    df['pe_percentile'] = df['avg_pe'].rolling(252).rank(pct=True)
    df['pb_percentile'] = df['avg_pb'].rolling(252).rank(pct=True)
    
    # 3. 可视化趋势
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # PE趋势
    axes[0,0].plot(df['trade_date'], df['avg_pe'])
    axes[0,0].set_title('市场平均PE趋势')
    
    # PB趋势  
    axes[0,1].plot(df['trade_date'], df['avg_pb'])
    axes[0,1].set_title('市场平均PB趋势')
    
    # PE分位数
    axes[1,0].plot(df['trade_date'], df['pe_percentile'])
    axes[1,0].set_title('PE历史分位数')
    
    # PB分位数
    axes[1,1].plot(df['trade_date'], df['pb_percentile'])
    axes[1,1].set_title('PB历史分位数')
```

## 🛡️ 错误处理和稳定性

### 1. API调用重试机制

```python
def api_call_with_retry(api_func, max_retries=3, delay=1.0):
    """API调用重试装饰器"""
    
    for attempt in range(max_retries):
        try:
            return api_func()
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"⚠️ API调用失败，{delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                time.sleep(delay * (attempt + 1))  # 递增延迟
                continue
            else:
                raise e
```

### 2. 数据完整性检查

```python
def validate_data_integrity(data, required_columns):
    """数据完整性验证"""
    
    # 1. 检查必要字段
    missing_columns = set(required_columns) - set(data.columns)
    if missing_columns:
        raise ValueError(f"缺少必要字段: {missing_columns}")
    
    # 2. 检查数据类型
    for col in ['open', 'high', 'low', 'close']:
        if col in data.columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                data[col] = pd.to_numeric(data[col], errors='coerce')
    
    # 3. 检查数据范围
    if 'pct_change' in data.columns:
        # 涨跌幅异常值检查 (±50%以上视为异常)
        extreme_values = data[abs(data['pct_change']) > 50]
        if not extreme_values.empty:
            print(f"⚠️ 发现{len(extreme_values)}个极端值")
    
    return data
```

### 3. 资源管理

```python
class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.connection = None
    
    def __enter__(self):
        self.connection = sqlite3.connect(self.db_path, timeout=30.0)
        self.connection.execute('PRAGMA journal_mode=WAL')
        return self.connection
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.connection:
            if exc_type is None:
                self.connection.commit()
            else:
                self.connection.rollback()
            self.connection.close()
```

## 🚀 性能优化策略

### 1. 数据库优化

```python
# 1. 批量操作优化
def batch_insert(data, table_name, batch_size=1000):
    """批量插入数据"""
    for i in range(0, len(data), batch_size):
        batch_data = data[i:i+batch_size]
        batch_data.to_sql(table_name, conn, if_exists='append', index=False)

# 2. 索引优化
optimized_indices = [
    "CREATE INDEX IF NOT EXISTS idx_sw_industry_code_date ON sw_industry_daily(ts_code, trade_date)",
    "CREATE INDEX IF NOT EXISTS idx_sw_industry_date ON sw_industry_daily(trade_date)",
    "CREATE INDEX IF NOT EXISTS idx_sw_industry_name ON sw_industry_daily(name)",
]

# 3. 查询优化
def optimized_query():
    """优化的查询语句"""
    return """
    SELECT ts_code, trade_date, close, pct_change
    FROM sw_industry_daily 
    WHERE trade_date >= ?  -- 使用参数化查询
    AND ts_code = ?
    ORDER BY trade_date DESC
    LIMIT ?
    """
```

### 2. 内存优化

```python
def process_large_dataset(data_loader, chunk_size=10000):
    """分块处理大数据集"""
    
    results = []
    for chunk in data_loader(chunk_size=chunk_size):
        # 处理数据块
        processed_chunk = process_chunk(chunk)
        results.append(processed_chunk)
        
        # 及时释放内存
        del chunk
        gc.collect()
    
    return pd.concat(results, ignore_index=True)
```

### 3. 缓存策略

```python
from functools import lru_cache

class DataCacheWithMemory:
    """带内存缓存的数据管理器"""
    
    def __init__(self):
        self._memory_cache = {}
    
    @lru_cache(maxsize=128)
    def get_stock_basic_cached(self):
        """LRU缓存的股票基本信息"""
        return self.get_stock_basic()
    
    def get_with_cache(self, key, loader_func, ttl=3600):
        """带TTL的缓存机制"""
        now = time.time()
        
        if key in self._memory_cache:
            data, timestamp = self._memory_cache[key]
            if now - timestamp < ttl:
                return data
        
        # 缓存过期或不存在，重新加载
        data = loader_func()
        self._memory_cache[key] = (data, now)
        return data
```

## 📝 代码质量保证

### 1. 单元测试示例

```python
import unittest
from data_cache_manager import DataCache

class TestDataCache(unittest.TestCase):
    
    def setUp(self):
        self.cache = DataCache()
    
    def test_get_stock_daily(self):
        """测试股票日线数据获取"""
        data = self.cache.get_stock_daily('000001.SZ', start_date='20230101', end_date='20230131')
        
        self.assertIsNotNone(data)
        self.assertFalse(data.empty)
        self.assertIn('ts_code', data.columns)
        self.assertIn('trade_date', data.columns)
        self.assertIn('close', data.columns)
    
    def test_dynamic_field_expansion(self):
        """测试动态字段扩展"""
        # 模拟API返回新字段
        api_columns = ['ts_code', 'trade_date', 'new_field1', 'new_field2']
        
        # 执行字段扩展
        self.cache._ensure_sw_industry_columns(api_columns)
        
        # 验证字段已添加
        with sqlite3.connect(self.cache.stock_db) as conn:
            cursor = conn.execute("PRAGMA table_info(sw_industry_daily)")
            columns = [row[1] for row in cursor.fetchall()]
            
            self.assertIn('new_field1', columns)
            self.assertIn('new_field2', columns)
```

### 2. 日志记录

```python
import logging

class DataCacheWithLogging(DataCache):
    """带详细日志的数据缓存管理器"""
    
    def __init__(self):
        super().__init__()
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('data_cache.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_stock_daily(self, ts_code, *args, **kwargs):
        """带日志的股票数据获取"""
        self.logger.info(f"开始获取股票数据: {ts_code}")
        
        try:
            result = super().get_stock_daily(ts_code, *args, **kwargs)
            self.logger.info(f"成功获取股票数据: {ts_code}, 记录数: {len(result)}")
            return result
        except Exception as e:
            self.logger.error(f"获取股票数据失败: {ts_code}, 错误: {e}")
            raise
```

## 🔮 扩展性设计

### 1. 插件化架构

```python
class DataSourcePlugin:
    """数据源插件基类"""
    
    def get_stock_daily(self, ts_code, start_date, end_date):
        raise NotImplementedError
    
    def get_stock_basic(self):
        raise NotImplementedError

class TusharePlugin(DataSourcePlugin):
    """Tushare数据源插件"""
    
    def __init__(self, token):
        import tushare as ts
        ts.set_token(token)
        self.pro = ts.pro_api()
    
    def get_stock_daily(self, ts_code, start_date, end_date):
        return self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)

class ExtensibleDataCache(DataCache):
    """可扩展的数据缓存管理器"""
    
    def __init__(self):
        super().__init__()
        self.plugins = {}
    
    def register_plugin(self, name, plugin):
        """注册数据源插件"""
        self.plugins[name] = plugin
    
    def get_data_from_plugin(self, plugin_name, method_name, *args, **kwargs):
        """从插件获取数据"""
        if plugin_name not in self.plugins:
            raise ValueError(f"未找到插件: {plugin_name}")
        
        plugin = self.plugins[plugin_name]
        method = getattr(plugin, method_name)
        return method(*args, **kwargs)
```

### 2. 配置管理

```python
import configparser

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file='config.ini'):
        self.config = configparser.ConfigParser()
        self.config_file = config_file
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            self.config.read(self.config_file)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['API'] = {
            'tushare_token': 'your_token_here',
            'request_delay': '0.2',
            'max_retries': '3'
        }
        
        self.config['DATABASE'] = {
            'stock_db': 'data_cache/stock_data.db',
            'fund_db': 'data_cache/fund_data.db',
            'batch_size': '1000'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    
    def get(self, section, key, fallback=None):
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)
```

---

本技术文档详细介绍了系统的核心技术实现，包括架构设计、算法实现、性能优化和扩展性设计。通过这些技术细节，开发者可以更好地理解和维护系统，并为未来的功能扩展提供参考。

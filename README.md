# 本地量化分析系统 (Local Quantitative Analysis System)

本项目是一个基于Python的本地量化数据缓存和分析系统，提供高效的金融数据管理、分析和可视化功能。

## 🚀 核心功能

### 📊 数据缓存管理 (Data Cache Management)
- **混合存储架构**: CSV文件存储基础信息，SQLite数据库存储时序数据
- **智能增量更新**: 自动检测数据更新需求，避免重复下载
- **多数据源支持**: 股票、基金、指数、行业板块数据全覆盖
- **API频率控制**: 智能控制请求频率，避免API限制

### 📈 数据分析功能 (Data Analysis)
- **股票基本面分析**: 财务指标、估值分析、业绩预告
- **技术指标计算**: 移动平均、波动率、相关性分析
- **行业板块分析**: 申万行业指数分析、行业相关性、估值趋势
- **基金分析**: ETF和场外基金的净值分析、表现评估

### 🔍 信息查看器 (Information Viewer)
- **交互式查询**: 命令行界面，支持多种数据展示方式
- **详细信息展示**: 股票基本信息、财务数据、技术指标
- **行业分析**: 申万行业指数详细分析和可视化
- **导出功能**: 支持数据导出为CSV和Excel格式

## 📁 项目结构

```
quant/
├── demo/                           # 主要代码目录
│   ├── data_cache_manager.py       # 核心数据缓存管理器
│   ├── analyze_cached_data.py      # 数据分析模块
│   ├── stock_info_viewer.py        # 信息查看器
│   ├── check_sw_fields.py          # 申万字段检查工具
│   ├── test_industry_fix.py        # 行业数据测试工具
│   └── data_cache/                 # 数据存储目录
│       ├── basic/                  # CSV基础信息文件
│       ├── stock_data.db           # 股票数据库
│       └── fund_data.db            # 基金数据库
├── data_cache/                     # 历史数据缓存
│   └── daily_*.csv                 # 股票日线数据
└── README.md                       # 项目说明文档
```

## 🛠️ 技术栈

- **Python 3.8+**: 主要编程语言
- **pandas**: 数据处理和分析
- **sqlite3**: 本地数据库存储
- **chinadata (tushare)**: 金融数据API接口
- **matplotlib**: 数据可视化

## 📦 安装和配置

### 1. 环境要求
```bash
# Python 3.8或更高版本
python --version

# 安装依赖包
pip install pandas sqlite3 chinadata matplotlib
```

### 2. API配置
在`data_cache_manager.py`中配置您的Tushare API Token：
```python
self.token = "your_tushare_api_token_here"
```

### 3. 初始化系统
```bash
cd demo
python data_cache_manager.py
```

## 🎯 使用指南

### 数据缓存管理器 (data_cache_manager.py)

#### 主要功能选项:
1. **缓存股票数据**: 批量下载所有股票的日线数据
2. **缓存基金数据**: ETF和场外基金净值数据
3. **基本面数据**: 财务指标、业绩预告等
4. **估值数据**: PE、PB、PS等估值指标
5. **扩展数据**: 复权因子、分红送转、限售解禁等
6. **指数数据**: 指数日线和成分股权重
7. **行业数据**: 申万行业指数和成分股

#### 使用示例:
```python
from data_cache_manager import DataCache

# 初始化缓存管理器
cache = DataCache()

# 获取股票日线数据
stock_data = cache.get_stock_daily('000001.SZ')

# 获取申万行业指数数据
industry_data = cache.get_sw_industry_daily('801010.SI')

# 批量缓存所有股票数据
cache.cache_all_stocks_data()
```

### 数据分析模块 (analyze_cached_data.py)

#### 核心分析功能:
- **股票分析**: 基本统计、技术指标、基本面分析
- **基金分析**: 净值分析、表现统计
- **行业分析**: 申万行业指数的多维度分析
- **相关性分析**: 股票/行业间的相关性矩阵
- **估值分析**: 历史估值趋势和分布

#### 使用示例:
```bash
# 运行分析模块
python analyze_cached_data.py

# 选择分析类型
# 1. 股票数据分析
# 2. 基金数据分析  
# 3. 申万行业指数分析
```

### 信息查看器 (stock_info_viewer.py)

#### 查看功能:
- **股票信息查看**: 基本信息、历史数据、技术指标
- **基金信息查看**: 净值走势、收益统计
- **行业信息查看**: 申万行业指数详细分析
- **扩展数据展示**: 财务指标、分红送转等
- **数据导出**: 多格式数据导出功能

#### 使用示例:
```bash
# 启动信息查看器
python stock_info_viewer.py

# 选择查看模式
# 1. 股票信息查看
# 2. 基金信息查看
# 3. 扩展数据展示
# 4. 批量数据导出
# 5. 数据库统计信息
# 6. 申万行业指数分析
```

## 📊 数据表结构

### 主要数据表:

#### 股票相关表:
- `stock_daily`: 股票日线数据
- `stock_daily_basic`: 每日估值指标
- `stock_fina_indicator`: 财务指标数据
- `stock_dividend`: 分红送转数据
- `stock_adj_factor`: 复权因子

#### 基金相关表:
- `fund_nav`: 基金净值数据
- `etf_daily`: ETF日线数据

#### 行业相关表:
- `sw_industry_daily`: 申万行业指数日线数据
- `industry_stock`: 行业成分股数据
- `industry_daily`: 行业指数日线数据

#### 指数相关表:
- `index_daily`: 指数日线数据
- `index_weight`: 指数成分股权重

## 🔧 高级特性

### 1. 动态字段扩展
系统支持自动检测API返回的新字段，并动态扩展数据库表结构：
```python
def _ensure_sw_industry_columns(self, api_columns):
    """确保sw_industry_daily表包含API返回的所有字段"""
    # 自动添加缺失字段到数据库表
```

### 2. 智能增量更新
系统会自动检测数据的最新日期，只下载增量数据：
```python
# 检查是否需要增量更新
if not force_update:
    latest_date = self.get_latest_date(table_name, db_path, ts_code, date_column)
    if latest_date:
        # 从最新日期后开始更新
```

### 3. 错误处理和重试机制
内置数据库锁定处理和API调用重试机制：
```python
max_retries = 3
for attempt in range(max_retries):
    try:
        # 数据库操作
    except Exception as e:
        if "database is locked" in str(e) and attempt < max_retries - 1:
            time.sleep(1.0 * (attempt + 1))
            continue
```

### 4. 数据完整性保证
- 主键约束确保数据唯一性
- 自动清理重复记录
- 数据类型验证和转换

## 📈 性能优化

### 1. 数据库优化
- SQLite WAL模式提高并发性能
- 合理的索引设计提高查询速度
- 批量操作减少I/O开销

### 2. API调用优化
- 智能频率控制避免API限制
- 增量更新减少数据传输
- 错误重试机制保证稳定性

### 3. 内存管理
- 分批处理大量数据
- 及时释放pandas DataFrame
- 数据库连接池管理

## 🚀 未来规划

### 即将支持的功能:
1. **实时数据推送**: WebSocket实时行情数据
2. **策略回测框架**: 内置策略回测和评估
3. **风险管理模块**: VaR、最大回撤等风险指标
4. **机器学习集成**: 价格预测和模式识别
5. **Web界面**: 基于Flask/Django的Web管理界面
6. **数据可视化增强**: 更丰富的图表和交互功能

### 架构优化:
1. **分布式存储**: 支持多节点数据分布
2. **缓存机制**: Redis缓存热点数据
3. **异步处理**: 异步I/O提高并发性能
4. **监控告警**: 系统监控和异常告警

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境搭建:
```bash
# 克隆项目
git clone <repository-url>

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements.txt
```

### 代码规范:
- 遵循PEP 8代码风格
- 添加适当的注释和文档字符串
- 编写单元测试
- 提交前运行代码检查

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至: [<EMAIL>]

---

**注意**: 使用本系统需要有效的Tushare API权限。请确保遵守相关数据使用协议和法律法规。

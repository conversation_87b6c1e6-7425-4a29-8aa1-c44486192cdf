#!/bin/bash

# 本地量化分析系统安装脚本
# Local Quantitative Analysis System Installation Script

echo "🚀 开始安装本地量化分析系统..."
echo "=" * 50

# 检查Python版本
echo "📋 检查Python环境..."
python_version=$(python --version 2>&1)
echo "   Python版本: $python_version"

if ! python -c "import sys; sys.exit(0 if sys.version_info >= (3,8) else 1)"; then
    echo "❌ 错误: 需要Python 3.8或更高版本"
    exit 1
fi

echo "✅ Python版本检查通过"

# 检查pip
echo "📋 检查pip..."
if ! command -v pip &> /dev/null; then
    echo "❌ 错误: 未找到pip，请先安装pip"
    exit 1
fi

echo "✅ pip检查通过"

# 创建虚拟环境 (可选)
read -p "🤔 是否创建虚拟环境? (推荐) [y/N]: " create_venv

if [[ $create_venv =~ ^[Yy]$ ]]; then
    echo "📦 创建虚拟环境..."
    python -m venv quant_env
    
    echo "🔄 激活虚拟环境..."
    source quant_env/bin/activate
    
    echo "✅ 虚拟环境创建完成"
    echo "   激活命令: source quant_env/bin/activate"
    echo "   退出命令: deactivate"
fi

# 安装依赖包
echo "📦 安装依赖包..."
pip install --upgrade pip

echo "   安装核心依赖..."
pip install pandas numpy matplotlib seaborn

echo "   安装数据源..."
pip install chinadata

echo "   安装Excel支持..."
pip install openpyxl xlsxwriter

echo "✅ 依赖包安装完成"

# 创建必要目录
echo "📁 创建项目目录..."
mkdir -p demo/data_cache/basic

echo "✅ 目录结构创建完成"

# 配置检查
echo "⚙️ 系统配置检查..."

# 检查Tushare API Token
echo "📋 检查API配置..."
echo "   请在data_cache_manager.py中配置您的Tushare API Token"
echo "   位置: self.token = \"your_tushare_api_token_here\""

# 权限检查
echo "📋 检查文件权限..."
if [ -w "." ]; then
    echo "✅ 当前目录可写"
else
    echo "❌ 当前目录不可写，请检查权限"
fi

# 完成安装
echo ""
echo "🎉 安装完成！"
echo "=" * 50
echo ""
echo "📚 快速开始:"
echo "   1. 配置API Token (在data_cache_manager.py中)"
echo "   2. 运行数据缓存: cd demo && python data_cache_manager.py"
echo "   3. 运行数据分析: cd demo && python analyze_cached_data.py"
echo "   4. 运行信息查看: cd demo && python stock_info_viewer.py"
echo ""
echo "📖 更多信息请参考:"
echo "   - README.md: 项目介绍和使用指南"
echo "   - TECHNICAL.md: 技术实现文档"
echo "   - CHANGELOG.md: 版本更新日志"
echo ""
echo "🆘 如遇问题请提交Issue到项目仓库"
echo ""
echo "🚀 享受量化分析的乐趣！"
